import {
  gusSfHzuEducationcloudIntegration,
  gusSfHzuEducationcloudFailedRecordProcessor,
} from "@functions/gus-sf-hzu-educationcloud-integration";
import {
  outboundEventIntegration,
  outboundEventIntegrationFailedRecordProcessor,
} from "@functions/outbound-event-integration";
import {
  hzuEducationcloudGusSfIntegration,
  hzuEducationcloudgusSfFailedRecordProcessor,
} from "@functions/hzu-educationcloud-gus-sf-integration";
import {
  myucwGusSfIntegration,
  myucwGusSfIntegrationFailedRecordProcessor,
} from "@functions/myucw-gus-sf-integration";
import {
  gusSfMyUCWFailedRecordProcessor,
  gusSfMyUCWIntegration,
} from "@functions/gus-sf-myucw-integration";
import {
  UECampusNetGusSfIntegration,
  UECampusNetgusSfFailedRecordProcessor,
} from "@functions/ue-campusnet-gus-sf-integration/index";
import {
  gusDepositEmailStatusTracker,
  gusMailer,
} from "@functions/gus-mailer-service";
import type { AWS } from "@serverless/typescript";
import {
  gusSfUECampusNetFailedRecordProcessor,
  gusSfUECampusNetIntegration,
} from "@functions/gus-sf-ue-cn-integration";
import {
  ULAWGusSfFailedRecordProcessor,
  ULAWGusSfIntegration,
} from "@functions/ulaw-gus-sf-integration";
import { unfcIntegrationPollingService } from "@functions/unfc-integration-polling-service";
import {
  unfcGusSfIntegration,
  unfcGusSfIntegrationBacklogEventProcessor,
} from "@functions/unfc-gus-sf-integration";
import { unfcSftpService } from "@functions/unfc-sftp-service";
import { permissions } from "./permissions";
import { gusSfUnfcIntegration } from "@functions/gus-sf-unfc-integration";

const serverlessConfiguration: AWS = {
  service: "gus-eip-integration-handlers",
  plugins: ["serverless-esbuild"],
  frameworkVersion: "*",
  provider: {
    name: "aws",
    runtime: "nodejs16.x",
    region: "eu-west-1",
    stage: "${opt:stage}",
    environment: {
      stage: "${opt:stage}",
      REGION: "${self:custom.variables.region.${self:provider.stage}}",
      HZU_EDUCATIONCLOUD_GUS_SF_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.hzuEducationcloudGusSfIntegrationSqsQueueArn.${self:provider.stage}}",
      GUS_SF_HZU_EDUCATIONCLOUD_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.gusSfHzuEducationcloudIntegrationSqsQueueArn.${self:provider.stage}}",
      GUS_MAILER_SQS_QUEUE_ARN:
        "${self:custom.variables.gusMailerQueueArn.${self:provider.stage}}",
      MYUCW_GUS_SF_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.myucwGusSfIntegrationSqsQueueArn.${self:provider.stage}}",
      GUS_MIDDLEWARE_API_KEY:
        "${self:custom.variables.gusMiddlewareApiKey.${self:provider.stage}}",
      HZU_KEY: "yphESfRh2o5J7L87WsKfh2MIBWSdPHev5TNPSGNZ",
      LIM_KEY: "yphESfRh2o5J7L87WsKfh2MIBWSdPHev4TNPSGNZ",
      GUS_MIDDLEWARE_API:
        "${self:custom.variables.gusMiddlewareApi.${self:provider.stage}}",
      OAP_API: "${self:custom.variables.oapApi.${self:provider.stage}}",
      UCW_KEY: "rLknjx19jUaPaoqGtbLawha8ylaHwBs8zP6aoGrh",
      UE_KEY: "zxTQGpLm9r2K5V34YpMfX8NOCJWdBHtv7RLDSAEK",
      UNFC_KEY: "yphESfRh2o5J7L87WsKfh2MIBWSdPHev6TNPSGNZ",
      // ULAW_KEY: "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa",
      ELLUCIAN_API_BASE_URL: "https://integrate.elluciancloud.ca/api",
      GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME:
        "${self:custom.variables.gusEipFailedRecordsTableName.${self:provider.stage}}",
      GUS_SF_OUTBOUND_TOPIC_ARN:
        "${self:custom.variables.gusSfOutboundTopicArn.${self:provider.stage}}",
      HZU_EDUCATIONCLOUD_OUTBOUND_TOPIC_ARN:
        "${self:custom.variables.hzuEducationcloudOutboundTopicArn.${self:provider.stage}}",
      MYUCW_OUTBOUND_TOPIC_ARN:
        "${self:custom.variables.myucwOutboundTopicArn.${self:provider.stage}}",
      UE_CN_OUTBOUND_TOPIC_ARN:
        "${self:custom.variables.ueOutboundTopicArn.${self:provider.stage}}",
      ULAW_OUTBOUND_TOPIC_ARN:
        "${self:custom.variables.ulawOutboundTopicArn.${self:provider.stage}}",
      GUS_DEPOSIT_EMAIL_STATUS_TRACKER_ARN:
        "${self:custom.variables.gusDepositEmailStatusTrackerArn.${self:provider.stage}}",
      GUS_EIP_INTEGRATION_PICKLIST_VALUE_TABLE_NAME:
        "${self:custom.variables.gusEipIntegrationPicklistValueTableName.${self:provider.stage}}",
      LOGGER_LOG_GROUP_NAME:
        "${self:custom.variables.loggerLogGroupName.${self:provider.stage}}",
      HZU_ADMISSION_LETTER_BUCKET_NAME:
        "${self:custom.variables.hzuAdmissionLetterBucketName.${self:provider.stage}}",
      TEAMS_WEBHOOK_URL:
        "${self:custom.variables.teamsWebhookUrl.${self:provider.stage}}",
      HZU_OWNER_ID:
        "${self:custom.variables.hzuOwnerId.${self:provider.stage}}",
      GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE:
        "${self:custom.variables.gusEipIntegrationHandlersAccessLambdaRole.${self:provider.stage}}",
      S3_EIP_CROSS_ACCOUNT_ACCESS_ROLE_ARN:
        "${self:custom.variables.s3EipCrossAccountAccessRole.${self:provider.stage}}",
      S3_CROSS_ACCOUNT_ACCESS_ROLE_ARN:
        "${self:custom.variables.s3CrossAccountAccessRole.${self:provider.stage}}",
      REVIEW_CENTER_BUCKET_NAME:
        "${self:custom.variables.reviewCenterBucketName.${self:provider.stage}}",
      MYUCW_CRED: "${self:custom.variables.myUCWCred.${self:provider.stage}}",
      GUS_SF_MY_UCW_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.gusSfMyUCWIntegrationQueueArn.${self:provider.stage}}",
      GUS_SF_UE_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.gusSfUEIntegrationQueueArn.${self:provider.stage}}",
      GUS_SENDER_EMAIL:
        "${self:custom.variables.gusSenderEmail.${self:provider.stage}}",
      UE_CAMPUSNET_GUS_SF_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.ueCampusNetGusSfIntegrationQueueArn.${self:provider.stage}}",
      GUS_SF_UE_CAMPUSNET_INTEGRATION_SQS_QUEUE_URL:
        "${self:custom.variables.gusSfUECampusNetIntegrationQueueUrl.${self:provider.stage}}",
      UCW_ACCESS_TOKEN_SECRET:
        "${self:custom.variables.ucwAccessTokenSecret.${self:provider.stage}}",
      GUS_SF_ULAW_INTEGRATION_SQS_QUEUE_URL:
        "${self:custom.variables.gusSfULawIntegrationSqsQueueArn.${self:provider.stage}}",
      ULAW_GUS_SF_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.ulawGusSfIntegrationSqsQueueArn.${self:provider.stage}}",
      UE_BUCKET_NAME:
        "${self:custom.variables.ueBucketName.${self:provider.stage}}",
      UNFC_OUTBOUND_TOPIC_ARN:
        "${self:custom.variables.unfcOutboundTopicArn.${self:provider.stage}}",
      UNFC_GUS_SF_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.unfcGusSfIntegrationSqsQueueArn.${self:provider.stage}}",
      GUS_SF_UNFC_INTEGRATION_SQS_QUEUE_ARN:
        "${self:custom.variables.gusSfUnfcIntegrationSqsQueueArn.${self:provider.stage}}",
      GUS_EIP_INTEGRATION_EVENT_BACKLOG_TABLE:
        "${self:custom.variables.gusEipIntegrationEventBacklogTable.${self:provider.stage}}",
    },
  },
  functions: {
    hzuEducationcloudGusSfIntegration,
    gusSfHzuEducationcloudIntegration,
    gusSfHzuEducationcloudFailedRecordProcessor,
    hzuEducationcloudgusSfFailedRecordProcessor,
    myucwGusSfIntegration,
    myucwGusSfIntegrationFailedRecordProcessor,
    gusSfMyUCWIntegration,
    gusSfMyUCWFailedRecordProcessor,
    UECampusNetGusSfIntegration,
    gusSfUECampusNetIntegration,
    gusSfUECampusNetFailedRecordProcessor,
    UECampusNetgusSfFailedRecordProcessor,
    gusMailer,
    gusDepositEmailStatusTracker,
    ULAWGusSfFailedRecordProcessor,
    ULAWGusSfIntegration,
    unfcIntegrationPollingService,
    unfcGusSfIntegration,
    unfcSftpService,
    gusSfUnfcIntegration,
    unfcGusSfIntegrationBacklogEventProcessor,
    outboundEventIntegration,
    outboundEventIntegrationFailedRecordProcessor,
    // gusSfUnfcFailedRecordProcessor,
  },
  resources: {
    Resources: {
      ...permissions,
    },
  },
  package: {
    individually: true,
    patterns: [
      "!node_modules/**",
      "node_modules/ssh2/**",
      "node_modules/cpu-features/**",
      "sshcrypto.node",
      "cpufeatures.node",
    ],
  },
  custom: {
    esbuild: {
      bundle: true,
      minify: false,
      sourcemap: true,
      exclude: ["aws-sdk"],
      target: "node18",
      define: { "require.resolve": undefined },
      platform: "node",
      concurrency: 10,
      external: ["ssh2", "cpu-features"],
      loader: {
        ".node": "file",
      },
    },
    variables: {
      gusMailerQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-GUS-MAILER-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-GUS-MAILER-QUEUE.fifo",
      },
      region: {
        dev: "eu-west-1",
        prod: "eu-west-1",
      },
      AccountId: {
        dev: "************",
        prod: "************",
      },
      hzuEducationcloudGusSfIntegrationSqsQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-HZU-EDUCATIONCLOUD-GUS-SF-INTEGRATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-HZU-EDUCATIONCLOUD-GUS-SF-INTEGRATION-QUEUE.fifo",
      },
      gusSfHzuEducationcloudIntegrationSqsQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-GUS-SF-HZU-EDUCATIONCLOUD-INTEGRATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-GUS-SF-HZU-EDUCATIONCLOUD-INTEGRATION-QUEUE.fifo",
      },
      myucwGusSfIntegrationSqsQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-MYUCW-GUS-SF-INTEGRATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-MYUCW-GUS-SF-INTEGRATION-QUEUE.fifo",
      },
      gusMiddlewareApiKey: {
        dev: "yphESfRh2o5J7L87WsKfh2MIBWSdPHev5TNPSGNZ",
        prod: "yphESfRh2o5J7L87WsKfh2MIBWSdPHev5TNPSGNZ",
      },
      gusMiddlewareApi: {
        dev: "https://dev-api.guseip.io",
        prod: "https://api.guseip.io",
      },
      oapApi: {
        dev: "https://oap-dev-api.apphero.io",
        prod: "https://oap-api.apphero.io",
      },
      ucwAccessTokenSecret: {
        dev: "ucw-dev-access-token",
        prod: "ucw-prod-access-token",
      },
      gusEipFailedRecordsTableName: {
        dev: "gus-eip-integration-failed-record-dev",
        prod: "gus-eip-integration-failed-record-prod",
      },
      gusSfOutboundTopicArn: {
        dev: "arn:aws:sns:eu-west-1:${aws:accountId}:DEV-GUS-SF-OUTBOUND-TOPIC.fifo",
        prod: "arn:aws:sns:eu-west-1:${aws:accountId}:PROD-GUS-SF-OUTBOUND-TOPIC.fifo",
      },
      myucwOutboundTopicArn: {
        dev: "arn:aws:sns:eu-west-1:${aws:accountId}:DEV-MYUCW-OUTBOUND-TOPIC.fifo",
        prod: "arn:aws:sns:eu-west-1:${aws:accountId}:PROD-MYUCW-OUTBOUND-TOPIC.fifo",
      },
      ueOutboundTopicArn: {
        dev: "arn:aws:sns:eu-west-1:${aws:accountId}:DEV-UE-CN-OUTBOUND-TOPIC.fifo",
        prod: "arn:aws:sns:eu-west-1:${aws:accountId}:PROD-UE-CN-OUTBOUND-TOPIC.fifo",
      },
      ulawOutboundTopicArn: {
        dev: "arn:aws:sns:eu-west-1:${aws:accountId}:DEV-ULAW-OUTBOUND-TOPIC.fifo",
        prod: "arn:aws:sns:eu-west-1:${aws:accountId}:PROD-ULAW-OUTBOUND-TOPIC.fifo",
      },
      gusDepositEmailStatusTrackerArn: {
        dev: "arn:aws:sns:eu-west-1:${aws:accountId}:gus-deposit-email-status-tracker-dev",
        prod: "arn:aws:sns:eu-west-1:${aws:accountId}:gus-deposit-email-status-tracker-prod",
      },
      hzuEducationcloudOutboundTopicArn: {
        dev: "arn:aws:sns:eu-west-1:${aws:accountId}:DEV-HZU-EDUCATIONCLOUD-OUTBOUND-TOPIC.fifo",
        prod: "arn:aws:sns:eu-west-1:${aws:accountId}:PROD-HZU-EDUCATIONCLOUD-OUTBOUND-TOPIC.fifo",
      },
      gusSfULawIntegrationSqsQueueArn: {
        dev: "https://sqs.eu-west-1.amazonaws.com/${aws:accountId}/DEV-GUS-SF-ULAW-INTEGRATION-QUEUE.fifo",
        prod: "https://sqs.eu-west-1.amazonaws.com/${aws:accountId}/PROD-GUS-SF-ULAW-INTEGRATION-QUEUE.fifo",
      },
      ulawGusSfIntegrationSqsQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-ULAW-GUS-SF-INTEGRATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-ULAW-GUS-SF-INTEGRATION-QUEUE.fifo",
      },
      gusEipIntegrationPicklistValueTableName: {
        dev: "gus-eip-integration-picklist-value-dev",
        prod: "gus-eip-integration-picklist-value-prod",
      },
      loggerLogGroupName: {
        dev: "eip-integration-logs-dev",
        prod: "eip-integration-logs-prod",
      },
      hzuAdmissionLetterBucketName: {
        dev: "herzingdocuments-dev",
        prod: "herzingdocuments",
      },
      teamsWebhookUrl: {
        dev: "https://gus.webhook.office.com/webhookb2/8217b7a4-b475-459a-86a1-703210be0009@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/5199793fdf48449aac90fbedd65583c7/d3348445-411e-47c1-bce3-f0508548f373/V222TSuQlVU9ft0jSmb3hOIf1CD5EaKQL311DQLUJhS4A1",
        prod: "https://gus.webhook.office.com/webhookb2/8217b7a4-b475-459a-86a1-703210be0009@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/2394e9b8d8c24eeab9f7f422f94dec63/d3348445-411e-47c1-bce3-f0508548f373/V2gAExYexFiQac9Jkx3S6MzPvUvTLdKjkhxiHcB6tKg241",
      },
      hzuOwnerId: {
        dev: "0054x000004RP0O",
        prod: "0054x000004RP0OAAW",
      },
      gusEipIntegrationHandlersAccessLambdaRole: {
        dev: "arn:aws:iam::${aws:accountId}:role/gus-eip-integration-handlers-access-${self:provider.stage}",
        prod: "arn:aws:iam::${aws:accountId}:role/gus-eip-integration-handlers-access-${self:provider.stage}",
      },
      s3EipCrossAccountAccessRole: {
        dev: "arn:aws:iam::************:role/s3CrossAccountAccessRole-prod",
        prod: "arn:aws:iam::${aws:accountId}:role/s3CrossAccountAccessRole-${self:provider.stage}",
      },
      s3CrossAccountAccessRole: {
        dev: "arn:aws:iam::************:role/s3OAPCrossAccountAccessRole-prod",
        prod: "arn:aws:iam::${aws:accountId}:role/s3OAPCrossAccountAccessRole-${self:provider.stage}",
      },
      reviewCenterBucketName: {
        dev: "reviewcenter-stage",
        prod: "reviewcenter",
      },
      myUCWCred: {
        dev: '{"username":"sf.auth","password":"yE!e6N_75Ks0mReW","sis_token":"BGq602fyDKRO1PJsGhY02h0hJxmg3Q4F66I9Qb8ilkWI9N8ed9zfWxsb6U5d3316","myUcwPath":"https://ucw.mysis.ca/"}',
        prod: '{"username":"sfprod.auth","password":"1eGn!CNaS.4EADiE","sis_token":"30Ikz8rko13TSPbzc78n7xvoO0L92317f30B5IlNd03sFz0LQ2n880b53YL22GFk","myUcwPath":"https://www.myucwest.ca/"}',
      },
      gusEipIntegrationEventBacklogTable: {
        dev: "arn:aws:dynamodb:eu-west-1:${aws:accountId}:table/gus-eip-integration-event-backlog-dev",
        prod: "arn:aws:dynamodb:eu-west-1:${aws:accountId}:table/gus-eip-integration-event-backlog-prod",
      },
      gusSfMyUCWIntegrationQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-GUS-SF-MYUCW-INTEGRATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-GUS-SF-MYUCW-INTEGRATION-QUEUE.fifo",
      },
      gusSfUEIntegrationQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-GUS-SF-UE-INTEGRATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-GUS-SF-UE-INTEGRATION-QUEUE.fifo",
      },
      gusSfUECampusNetIntegrationQueueUrl: {
        dev: "https://sqs.eu-west-1.amazonaws.com/${aws:accountId}/sf-campusnet-stage.fifo",
        prod: "https://sqs.eu-west-1.amazonaws.com/${aws:accountId}/sf-campusnet.fifo",
      },
      ueCampusNetGusSfIntegrationQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:campusnet-sf-stage.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:campusnet-sf.fifo",
      },
      gusSenderEmail: {
        dev: "<EMAIL>",
        prod: "<EMAIL>",
      },
      ueBucketName: {
        dev: "campusnet-sf-sync-stage",
        prod: "campusnet-sf-sync",
      },
      unfcOutboundTopicArn: {
        dev: "arn:aws:sns:eu-west-1:${aws:accountId}:DEV-UNFC-OUTBOUND-TOPIC.fifo",
        prod: "arn:aws:sns:eu-west-1:${aws:accountId}:PROD-UNFC-OUTBOUND-TOPIC.fifo",
      },
      unfcGusSfIntegrationSqsQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-UNFC-GUS-SF-INTEGRATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-UNFC-GUS-SF-INTEGRATION-QUEUE.fifo",
      },
      gusSfUnfcIntegrationSqsQueueArn: {
        dev: "arn:aws:sqs:eu-west-1:${aws:accountId}:DEV-GUS-SF-UNFC-INTEGRATION-QUEUE.fifo",
        prod: "arn:aws:sqs:eu-west-1:${aws:accountId}:PROD-GUS-SF-UNFC-INTEGRATION-QUEUE.fifo",
      },
      EipApiId: {
        dev: "ybafgiu3j6",
        prod: "ycwq8lve4d",
      },
    },
  },
};

module.exports = serverlessConfiguration;
