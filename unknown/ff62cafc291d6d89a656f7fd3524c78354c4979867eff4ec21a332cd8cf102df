import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { postOapData } from "src/connectors/oap-connector";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
export class DocumentUploadH<PERSON>ler implements PlatformEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private email: string;
  async fetchGusSFOpportunityId(event: any) {
    throw new Error("Method not implemented.");
  }
  async fetchGusSFDetails(opportunityId: any) {
    throw new Error("Method not implemented.");
  }
  async syncInHZU(event: any, transformedData: any): Promise<any> {
    throw new Error("Method not implemented.");
  }
  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Inside DUH");
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      this.correlationId = platformEventMessage.event?.EventUuid;
      this.applicationFormId = platformEventMessage.payload.GUS_Application_Form_ID__c;
      this.brand = 'HZU';
      this.usecase = platformEventMessage.payload?.Scenario__c;
      this.email = platformEventMessage.payload?.Email_ID__c
      await this.log(
        platformEventMessage,
        `fetch content document link by entityid initiated`,
        loggerEnum.Event.FETCH_CONTENT_DOCUMENT_LINK_INITIATED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        platformEventMessage
      );
      const contentDocumentIdResponse = await getData(
        `hzu/contentDocumentLinkByLinkedEntityId/${platformEventMessage.payload.Document_Checklist_Item_ID__c}?scenario=${this.usecase}`,
        this.correlationId
      );
      await this.log(
        platformEventMessage,
        `fetch content document link by entityid completed`,
        loggerEnum.Event.FETCH_CONTENT_DOCUMENT_LINK_COMPLETED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        platformEventMessage,
        contentDocumentIdResponse
      );
      console.log(
        "ContentDocumentId -->",
        contentDocumentIdResponse.ContentDocumentId
      );
      if (contentDocumentIdResponse.ContentDocumentId) {
        await this.log(
          platformEventMessage,
          `fetch content version Id by content document id initiated`,
          loggerEnum.Event.FETCH_CONTENT_VERSION_ID_INITIATED,
          loggerEnum.Component.HZU_EDUCATIONCLOUD,
          platformEventMessage
        );
        const contentVersionId = await getData(
          `hzu/contentDocumentById/${contentDocumentIdResponse.ContentDocumentId}?scenario=${this.usecase}`
        );
        await this.log(
          platformEventMessage,
          `fetch content version Id by content document id completed`,
          loggerEnum.Event.FETCH_CONTENT_VERSION_ID_COMPLETED,
          loggerEnum.Component.HZU_EDUCATIONCLOUD,
          platformEventMessage,
          contentVersionId
        );
        await this.log(
          platformEventMessage,
          `fetch version data by latest published version id initiated`,
          loggerEnum.Event.FETCH_VERSION_DATA_INITIATED,
          loggerEnum.Component.HZU_EDUCATIONCLOUD,
          platformEventMessage
        );
        const versionData = await getData(
          `hzu/contentVersionById/${contentVersionId.LatestPublishedVersionId}?scenario=${this.usecase}`,
          this.correlationId
        );
        console.log("versionData", versionData);
        await this.log(
          platformEventMessage,
          `fetch version data by latest published version id completed`,
          loggerEnum.Event.FETCH_VERSION_DATA_COMPLETED,
          loggerEnum.Component.HZU_EDUCATIONCLOUD,
          platformEventMessage,
          versionData
        );
        if (versionData.base64) {
          await this.log(
            platformEventMessage,
            `fetch opportunity by applicationform id initiated`,
            loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
            loggerEnum.Component.GUS_SALESFORCE,
            platformEventMessage
          );
          const opportunityResponse = await getData(
            `gus/opportunityId/${platformEventMessage.payload.GUS_Application_Form_ID__c}?scenario=${this.usecase}`,
            this.correlationId
          );
          await this.log(
            platformEventMessage,
            `fetch opportunity by applicationform id completed`,
            loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
            loggerEnum.Component.GUS_SALESFORCE,
            platformEventMessage,
            opportunityResponse
          );
          console.log("OPP id -->", opportunityResponse.Id);

          if (platformEventMessage.payload.Scenario__c === "I_20") {
            await this.log(
              platformEventMessage,
              `update opportunity by opportunity id initiated`,
              loggerEnum.Event.UPDATE_OPPORTUNITY_INITIATED,
              loggerEnum.Component.GUS_SALESFORCE,
              platformEventMessage
            );
            const updateI20Field = await postData(
              `gus/updateOpportunity/${opportunityResponse.Id}?scenario=${this.usecase}`,
              {
                I_20__c: "Yes",
              }
            );
            console.log("Update opp", updateI20Field);
            await this.log(
              platformEventMessage,
              `update opportunity by opportunity id completed`,
              loggerEnum.Event.UPDATE_OPPORTUNITY_COMPLETED,
              loggerEnum.Component.GUS_SALESFORCE,
              platformEventMessage,
              updateI20Field
            );
          }

          const uploadFilePayload = {
            base64File: versionData?.base64,
            documentType:
              platformEventMessage.payload.Scenario__c === "I_20"
                ? "Current I20"
                : "Conditional Offer Letter",
            fileName: versionData.pathOnClient,
            applicationFormId:
              platformEventMessage.payload.GUS_Application_Form_ID__c,
            opportunityId: opportunityResponse.Id,
            bucketName: process.env.HZU_ADMISSION_LETTER_BUCKET_NAME,
          }

          await this.log(
            platformEventMessage,
            `upload opportunity files initiated`,
            loggerEnum.Event.UPLOAD_FILES_INITIATED,
            loggerEnum.Component.GUS_SALESFORCE,
            uploadFilePayload
          );
          const opportunityFilesUploadResponse = await postOapData(
            `opportunityfiles/upload`,
            uploadFilePayload
          );

          await this.log(
            platformEventMessage,
            `upload opportunity files completed`,
            loggerEnum.Event.UPLOAD_FILES_COMPLETED,
            loggerEnum.Component.GUS_SALESFORCE,
            uploadFilePayload,
            opportunityFilesUploadResponse
          );
          console.log(
            "OpportunityFilesUploadResponse",
            opportunityFilesUploadResponse
          );

          const pathOnClient = opportunityFilesUploadResponse.key.split("/")[2];
          const fileName = pathOnClient.split(".")[0];
          console.log("File type -->", pathOnClient);
          console.log("File type -->", fileName);
          const opportunityFilePayload = {
            ApplicationId__c:
              platformEventMessage.payload.GUS_Application_Form_ID__c,
            DocumentType__c:
              platformEventMessage.payload.Scenario__c === "I_20"
                ? "Current I20"
                : "Conditional Offer Letter",
            Name: pathOnClient,
            FilePath__c: pathOnClient,
            Opportunity__c: opportunityResponse.Id,
            FullUrl__c: opportunityFilesUploadResponse.key,
            OriginalValue__c: pathOnClient,
            S3FileName__c: opportunityFilesUploadResponse.key,
            LetterType__c:
              platformEventMessage.payload.Scenario__c === "I_20"
                ? "Current I20"
                : "Conditional Offer Letter",
            BucketName__c: process.env.HZU_ADMISSION_LETTER_BUCKET_NAME,
            DocumentSource__c: "Herzing",
            Status__c: "Accepted",
          };
          await this.log(
            platformEventMessage,
            `Upload document initiated`,
            loggerEnum.Event.UPLOAD_DOCUMENT_INITIATED,
            loggerEnum.Component.GUS_SALESFORCE,
            opportunityFilePayload
          );
          const uploadFileResponse = await postData(
            `gus/opportunityfile`,
            opportunityFilePayload,
            this.correlationId
          );

          console.log("Upload response ->", uploadFileResponse);
          if (uploadFileResponse) {
            await this.log(
              platformEventMessage,
              `Upload document successful`,
              loggerEnum.Event.OPERATION_COMPLETED,
              loggerEnum.Component.GUS_SALESFORCE,
              opportunityFilePayload,
              uploadFileResponse
            );
          }
          return uploadFileResponse;
        }
      } else {
        throw new Error(
          "No Content document present for the given Linked Entity Id"
        );
      }
    } catch (error) {
      console.log("Error ..", error);
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}

