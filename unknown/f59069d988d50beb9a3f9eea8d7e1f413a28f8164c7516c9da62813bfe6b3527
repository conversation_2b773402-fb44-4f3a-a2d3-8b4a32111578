
// This is the central registry
const handlerRegistry: Map<string, new () => ICampusNetInboundHandler> = new Map();

// This is the decorator to register each handler
export function RegisterHandler(scenario: string) {
  return function (constructor: new () => ICampusNetInboundHandler) {
    handlerRegistry.set(scenario, constructor);
  };
}

// This function will be used by the factory to get the correct handler
export function getRegisteredHandler(scenario: string): ICampusNetInboundHandler | undefined {
  const HandlerClass = handlerRegistry.get(scenario);
  return HandlerClass ? new HandlerClass() : undefined;
}
