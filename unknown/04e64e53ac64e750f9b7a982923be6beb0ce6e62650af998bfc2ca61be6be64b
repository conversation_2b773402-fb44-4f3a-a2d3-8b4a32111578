import { LoggerService } from "src/common/cloudwatchService";
const loggerService = new LoggerService();
import { getData } from "src/connectors/eip-connector";
import { LoggerEnum } from "@gus-eip/loggers";
import { sendToSQS } from "src/common/sqsService";
import { RegisterHandler } from "../HandlerRegistry";
import { opportunityWithdrawalFieldMapping } from "../mapper/opportunityWithdrawalFieldMapping";
const loggerEnum = new LoggerEnum();

@RegisterHandler("GUS_WITHDRAW_APPLICATION")
export class WithdrawApplicationHandler implements ICampusNetInboundHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(opportunityId: any): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `Fetch application details by opportunityId initiated ${opportunityId}`,
        loggerEnum.Event.FETCH_GUS_APPLICATION_INTIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return await getData(
        `gus/getapplicationsdetails/${opportunityId}?scenario=${this.usecase}`,
        this.correlationId,
        process.env.UE_KEY
      );
    } catch (error) {
      await this.error(
        opportunityId,
        error,
        loggerEnum.Event.FETCH_GUS_APPLICATION_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching gus object details: ${error}`);
    }
  }
  syncToTargetSystem(
    event: unknown,
    transformData?: unknown,
    correlationId?: unknown
  ): Promise<any> {
    throw new Error("Method not implemented.");
  }
  async handleMessage(event: any): Promise<any> {
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.applicationFormId =
      platformEventMessage.payload.Application_Form_Id__c;
    this.brand = platformEventMessage.payload.BusinessUnitFilter__c;
    const opportunityId = platformEventMessage.payload.Opportunity_Id__c;
    await this.log(
      platformEventMessage.payload,
      `Withdraw application initiated ${this.applicationFormId}`,
      loggerEnum.Event.SYNC_IN_UE_CAMPUSNET_INITIATED
    );
    try {
      if (this.applicationFormId) {
        const sfDetails = await this.fetchGusSFDetails(opportunityId);
        if (!sfDetails) {
          await this.error(
            event,
            `No application details found for ${opportunityId}`,
            loggerEnum.Event.SYNC_IN_UE_CAMPUSNET_FAILED
          );
          throw new Error(`No application details found for ${opportunityId}`);
        }
        await this.log(
          event,
          `fetch application details by opportunityId completed`,
          loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          opportunityId,
          {},
          "Opportunity"
        );
        sfDetails.Opportunity = await this.enrichOpportunityWithLineItemData(
          sfDetails.Opportunity
        );
        const transformedData = await this.transformData(
          sfDetails,
          opportunityWithdrawalFieldMapping
        );
        console.log("transformedData", transformedData);
        const sqsPayload = {
          name: "cn-application",
          apiVersion: 2,
          externalId: opportunityId,
          createdAt: new Date().toISOString(),
          data: transformedData,
        };

        // Send to SQS
        const SQS_QUEUE_URL =
          process.env.GUS_SF_UE_CAMPUSNET_INTEGRATION_SQS_QUEUE_URL;
        await sendToSQS(
          SQS_QUEUE_URL,
          sqsPayload,
          opportunityId,
          `${opportunityId}-${Date.now()}`
        );

        await this.log(
          transformedData,
          `Successfully sent transformed data to SQS for ${opportunityId}`,
          loggerEnum.Event.OPERATION_COMPLETED
        );
        await this.log(
          platformEventMessage.payload,
          `Sync withdraw in ue campusnet completed ${opportunityId}`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.UE_CAMPUSNET,
          {},
          {}
        );
        return {
          statusCode: 200,
          body: JSON.stringify({}),
        };
      } else {
        await this.error(
          platformEventMessage.payload,
          "Invalid  Application Form ID",
          loggerEnum.Event.SYNC_IN_UE_CAMPUSNET_FAILED
        );
        throw new Error("Invalid Application Form ID");
      }
    } catch (error) {
      await this.error(
        platformEventMessage,
        error.message ? error.message : error,
        loggerEnum.Event.WITHDRAW_APPLICATION_FAILED,
        loggerEnum.Component.UE_CAMPUSNET
      );
      throw error.message ? error.message : error;
    }
  }
  private async enrichOpportunityWithLineItemData(
    opportunity: any
  ): Promise<any> {
    let enrichedOpportunity = { ...opportunity };

    if (opportunity?.OpportunityLineItems?.records?.length) {
      const lineItem = opportunity.OpportunityLineItems.records[0];

      enrichedOpportunity = {
        ...opportunity,
        Product2Id: lineItem?.Product2Id ?? "",
      };
    }

    return enrichedOpportunity;
  }

  private async transformData(
    inputData: any,
    salesforceConfig: any
  ): Promise<any> {
    try {
      const output: any = {};

      // Iterate over each object type in the config (e.g., Opportunity, Account)
      for (const [objectType, mappings] of Object.entries(salesforceConfig)) {
        // Get the input data for the current object type (e.g., inputData.Opportunity)
        let inputObject = inputData[objectType];

        // Skip if the input object doesn't exist
        if (!inputObject) {
          inputObject = {};
        }

        // Process each mapping for the current object type
        for (const [inputPath, outputField] of Object.entries(mappings)) {
          const keys = inputPath.replace(`${objectType}.`, "").split("."); // Remove prefix
          let value = inputObject;

          // Traverse nested properties
          for (const key of keys) {
            if (value && value[key] !== undefined) {
              value = value[key];
            } else {
              value = null;
              break;
            }
          }

          // Assign the transformed value to the output object
          output[outputField] = value;
        }
      }

      return output;
    } catch (error) {
      throw new Error(`Error in transformData: ${error.message}`);
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.UE_CAMPUSNET,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.UE_CAMPUSNET,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
