import { DynamoDBService } from "src/common/dynamodbService";
import { EventHandlerFactory } from "./eventHandlerFactory";
import { SnsService } from "src/common/snsService";
import { storeFailedRecordsQueue } from "src/common/storeFailedRecords";
import { checkApplicationIdExist } from "src/common/checkFailedRecords";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const dbService = new DynamoDBService();
const snsService = new SnsService();
let correlationId: string;
let usecase: string;
let applicationFormId: string;
let brand: string;
export const handleSfRequests = async (event) => {
  const clonedEvent = deepClone(event);
  const responses = [];
  for (const record of clonedEvent.Records) {
    const eventBody = JSON.parse(record.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    platformEventMessage.eventId = record?.messageId;
    console.log('platformEventMessage', platformEventMessage)
    usecase = platformEventMessage?.scenario;
    correlationId = platformEventMessage?.eventId;
    brand = "UCW";
    applicationFormId = platformEventMessage.payload.GUS_appid || platformEventMessage.payload.sprogramID;
    await loggerService.log(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      loggerEnum.Component.GUS_SALESFORCE,
      loggerEnum.Event.EVENT_INITIATED,
      usecase,
      record,
      {},
      "event initiated",
      brand,
      applicationFormId,
      "Application_Form_Id__c",
      applicationFormId,
      "",
      "",
      "",
      applicationFormId,
    );
    const isFailedMessageGroupData = await checkApplicationIdExist(
      record,
      applicationFormId,
      process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
      "MYUCW_GUS_SF"
    );
    if (isFailedMessageGroupData === "No messages to process") {
      try {
        const handler = EventHandlerFactory.getHandler(
          platformEventMessage.scenario
        );
        if (handler && typeof handler.handleMessage === "function") {
          const response = await handler.handleMessage(platformEventMessage);
          console.log("Response", response)
          if (response && platformEventMessage.status === "Failed") {
            await dbService.deleteItem(
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
              {
                PK: `MYUCW_GUS_SF#${applicationFormId}`,
                SK: platformEventMessage?.uuid || record?.messageId
              },
            )

            const queryParams = {
              TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
              KeyConditionExpression: "PK = :pkValue",
              ExpressionAttributeValues: {
                ":pkValue": `MYUCW_GUS_SF#${applicationFormId}`,
              },
            };

            const checkExcistingFailedRecordForMessageGrpId = await dbService.queryObjects(queryParams)
            console.log('checkExcistingFailedRecordForMessageGrpId -->', checkExcistingFailedRecordForMessageGrpId)

            if (checkExcistingFailedRecordForMessageGrpId.Items.length === 0) {
              await dbService.deleteItem(
                process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                {
                  PK: "MYUCW_GUS_SF",
                  SK: applicationFormId
                }
              )
            }
          }
          responses.push(response);
        } else {
          responses.push(null);
        }
      } catch (error) {
        console.log("ERROR ->", error);
        await storeFailedRecordsQueue(
          applicationFormId,
          process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
          "MYUCW_GUS_SF",
          record
        );
      }
    } else {
      await loggerService.log(
        correlationId,
        new Date().toISOString(),
        loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
        loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
        loggerEnum.Component.GUS_SALESFORCE,
        loggerEnum.Event.OPERATION_COMPLETED,
        usecase,
        platformEventMessage,
        {},
        "A record with the same messageGroupId failed earlier, so this record can't proceed further",
        brand,
        applicationFormId,
        "Application_Form_Id__c",
        applicationFormId,
        "",
        "",
        "",
        applicationFormId,
      );
    }
  }
  return responses;
};
const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item));
  }
  const clonedObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }
  return clonedObj;
};

export const handleFailedRecords = async () => {
  try {
    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": "MYUCW_GUS_SF",
      },
    };

    const partitionResponse = await dbService.queryObjects(params);
    console.log("PartitionItemData -->", partitionResponse);
    if (partitionResponse.Items && partitionResponse.Items.length > 0) {
      for (const partitionItem of partitionResponse.Items) {
        console.log("Item -->", partitionItem);
        if (partitionItem.status === "Failed" && (partitionItem.retryCount <= 3 || !partitionItem.retryCount)) {
          const params = {
            TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            KeyConditionExpression: "PK = :partitionKey",
            ExpressionAttributeValues: {
              ":partitionKey": `${partitionItem.PK}#${partitionItem.SK}`,
            },
          };
          const records = await dbService.queryObjects(params);
          for (const record of records.Items) {
            const eventBody = JSON.parse(record.body);
            const eventMessage = JSON.parse(eventBody.Message);
            eventMessage.status = "Failed"
            console.log("Event message ->", eventMessage);
            const publishMessages = await snsService.publishMessages(
              eventMessage,
              eventMessage.payload.GUS_appid || eventMessage.uuid,
              process.env.MYUCW_OUTBOUND_TOPIC_ARN
            );

            console.log("publishMessages", publishMessages)
            //   if (publishMessages) {
            //     const deleteResponse = await dbService.deleteItem(
            //       process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            //       {
            //         PK: `${partitionItem.PK}#${partitionItem.SK}`,
            //         SK: eventMessage.event.replayId.toString(),
            //       }
            //     );
            //     console.log("Deleted item:", deleteResponse);
            //   } else {
            //     throw new Error("Error publishing messages");
            //   }
          }
          const currentRetryCount = partitionItem.retryCount || 0;
          await dbService.updateObject(
            process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            {
              PK: partitionItem.PK,
              SK: partitionItem.SK,
            },
            {
              retryCount: currentRetryCount + 1,
            }
          );
        }
      }
      return "All records processed successfully";
    } else {
      return "No records to process";
    }
  } catch (error) {
    throw new Error(error);
  }
};
