import { DynamoDBService } from "src/common/dynamodbService";

const dbService = new DynamoDBService()

export async function getPicklistValue(outputField, currentValue, type=null): Promise<string> {
    const tableName = process.env.GUS_EIP_INTEGRATION_PICKLIST_VALUE_TABLE_NAME;
    const key = {
        PK: outputField,
        SK: type? type : "GUS_SF_HZU_EDUCATIONCLOUD",
    };

    const data = await dbService.getObject(tableName, key);
    if (data.Item && data.Item.Picklist) {
        return data.Item.Picklist[currentValue] || null;
    }
    return null;
}
export async function getPicklistByField(type, field): Promise<string> {
  const tableName = process.env.GUS_EIP_INTEGRATION_PICKLIST_VALUE_TABLE_NAME;
  const key = {
      PK: type,
      SK: field,
  };

  const data = await dbService.getObject(tableName, key);
  return data.Item.Picklist;
}
export async function getAllPicklistValues(type: string): Promise<any> {
    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_PICKLIST_VALUE_TABLE_NAME,
      KeyConditionExpression: "PK = :pkValue",
      ExpressionAttributeValues: {
        ":pkValue": type,
      },
    };
  
    const data = await dbService.queryObjects(params);
    return data.Items;
  }