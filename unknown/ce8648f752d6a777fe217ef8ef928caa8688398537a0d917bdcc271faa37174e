export interface FileData {
  s3BucketName: string;
  originalFileName: string;
  filePath: string;
  OpportunityFileId: string;
}

export interface EQHEData {
  eqheDate: string;
  eqheCity: string;
  eqhecountry: string;
  eqheTitle: string;
}

export interface ApplicationData {
  programName: string;
  intakeDate: string;
  formLanguage: string;
  educationSchoolsTranscriptFile?: FileData[];
  applyReasonFile?: FileData[];
  filePassport?: FileData[];
  educationSchoolsCertificateFile?: FileData[];
  photo?: FileData[];
  fileCV?: FileData[];
  files?: FileData[];
  higherEducationExits: string;
  eqhe: EQHEData[];
  postalCode: string;
  correspondenceAddress: string;
  state: string;
  city: string;
  addressCo: string;
  mailingAddress: string;
  correspondenceLanguage: string;
  passportNumber: string;
  citizenship: string;
  countryOfBirth: string;
  placeOfBirth: string;
  dateOfBirth: string;
  title: string;
  intake: string;
  country: string;
  declarationInfoTrue: string[];
  duration: string;
  firstName: string;
  language: string;
  legalFamilyName: string;
  level: string;
  location: string;
  personalEmail: string;
  personalInfoTrue: string[];
  phone: string;
  phoneHome: string;
  program: string;
  applicationForm: FileData[];
}
export const defaultApplicationData: ApplicationData = {
    programName: "",
    intakeDate: "",
    formLanguage: "",
    educationSchoolsTranscriptFile: [],
    applyReasonFile: [],
    filePassport: [],
    educationSchoolsCertificateFile: [],
    photo: [],
    fileCV: [],
    files: [],
    higherEducationExits: "0",
    eqhe: [],
    postalCode: "",
    correspondenceAddress: "",
    state: "",
    city: "",
    addressCo: "",
    mailingAddress: "",
    correspondenceLanguage: "",
    passportNumber: "",
    citizenship: "",
    countryOfBirth: "",
    placeOfBirth: "",
    dateOfBirth: "",
    title: "",
    intake: "",
    country: "",
    declarationInfoTrue: ["1"],
    duration: "",
    firstName: "",
    language: "",
    legalFamilyName: "",
    level: "",
    location: "",
    personalEmail: "",
    personalInfoTrue: ["1"],
    phone: "",
    phoneHome: "",
    program: "",
    applicationForm: [],
  };
  