import { postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { S3Service } from "src/common/s3Service";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const s3Service = new S3Service();

export class OpportunityDocumentUploadHandler implements PlatformEventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private email: string;
  fetchGusSFDetails(event: any): Promise<any> {
    throw new Error("Method not implemented.");
  }
  syncInHZU(event: any, transformData?: any): Promise<any> {
    throw new Error("Method not implemented.");
  }
  async handleMessage(event: any): Promise<any> {
    const clonedEvent = { ...event };
    const eventBody = JSON.parse(clonedEvent.body);
    const platformEventMessageJSON = JSON.parse(eventBody.Message);
    const platformEventMessage = { ...platformEventMessageJSON };
    this.correlationId = platformEventMessage.event?.EventUuid
    this.applicationFormId = platformEventMessage.payload.GUS_Application_Form_ID__c
    this.brand = "HZU"
    this.usecase = platformEventMessage.payload?.Scenario__c
    this.email = platformEventMessage.payload?.Email_ID__c
    console.log("GUS DOCUMENT UPLOAD EVENT -->", platformEventMessage);
    console.log(
      "GUS Opportunity file details:",
      platformEventMessage?.payload?.opportunityFileDetails
    );
    console.log(
      "HZU Checklist ID and Name",
      platformEventMessage?.payload?.Id,
      platformEventMessage?.payload?.Name
    );
    if (platformEventMessage?.payload?.opportunityFileDetails) {
      let item = platformEventMessage?.payload?.opportunityFileDetails;
      item.scenario = platformEventMessage.payload?.Scenario__c
      const fileName = item?.Name.split(".")[0];
      const fileType = item?.Name.split(".")[1];
      const request = {
        fileName: fileName,
        fileNameWithExtension: item.Name,
        fileExtension: fileType,
        fileType: fileType.toUpperCase(),
        documentCheckListId: platformEventMessage.payload?.Id,
        externalId: item.Id,
      };
      const requestBody = {
        uploadDetails: request,
        additionalDetails: item,
      };
      try {
        await this.log(
          platformEventMessage,
          "upload document to hzu initiated",
          loggerEnum.Event.UPLOAD_FILES_INITIATED,
          loggerEnum.Component.HZU_EDUCATIONCLOUD,
          requestBody
        )
        const uploadFileResponse = await postData(
          "hzu/fetchanduploadfile",
          requestBody,
          this.correlationId
        );

        if (uploadFileResponse) {
          await this.log(
            platformEventMessage,
            "upload document to hzu successful",
            loggerEnum.Event.OPERATION_COMPLETED,
            loggerEnum.Component.HZU_EDUCATIONCLOUD,
            requestBody,
            uploadFileResponse
          )
        }
        return uploadFileResponse;
      } catch (error) {
        console.log(error);
        await this.error(
          platformEventMessage,
          error.message ? JSON.stringify(error.message) : JSON.stringify(error),
          loggerEnum.Event.SYNC_DOCUMENT_FAILED,
          loggerEnum.Component
            .HZU_EDUCATIONCLOUD,
        )
        throw error;
      }
    }

  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message ? errorMessage.message : JSON.stringify(errorMessage),
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
