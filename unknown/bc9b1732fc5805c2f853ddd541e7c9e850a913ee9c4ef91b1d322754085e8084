import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
export class WithdrawApplicationHandler implements PlatformEventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private email: string;
  async fetchGusSFDetails(event: any, correlationId: string) {
    try {
      await this.log(
        event,
        `fetch gus opportunity details initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        event
      );
      return await getData(
        `gus/getOpportunitiesById/${event.payload.Opportunity_Id__c}`,
        correlationId
      );
    } catch (error) {
      throw new Error(`Error fetching gus object details: ${JSON.stringify(error)}`);
    }
  }
  async syncInHZU(event: any, updateById, correlationId): Promise<any> {
    try {
      await this.log(
        event,
        `sync in hzu initiated`,
        loggerEnum.Event.SYNC_IN_HZU_INITIATED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        event,
      );
      console.log("withdraw", event, `hzu/opportunity/${updateById[0].Id}`);
      return await postData(
        `hzu/opportunity/${updateById[0].Id}?scenario=${this.usecase}`,
        event,
        correlationId
      );

    } catch (error) {
      throw new Error(`Error syncing in hzu: ${JSON.stringify(error)}`);
    }
  }
  async getHZUOpportunityId(
    applicationFormId: any,
    correlationId: string,
  ): Promise<void> {
    return await getData(
      `hzu/opportunityByApplicationId/${applicationFormId}?scenario=${this.usecase}`,
      correlationId
    );
  }
  async handleMessage(event: any): Promise<void> {
    let syncPayload: any;
    try {
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      this.correlationId = platformEventMessage.event?.EventUuid
      this.applicationFormId = platformEventMessage.payload.Application_Form_Id__c
      this.brand = platformEventMessage.payload?.BusinessUnitFilter__c
      this.usecase = platformEventMessage.payload?.Scenario__c
      this.email = platformEventMessage.payload?.Email__c
      let opportunityDetails = await this.fetchGusSFDetails(
        platformEventMessage,
        this.correlationId
      );
      opportunityDetails = opportunityDetails[0];
      await this.log(
        platformEventMessage,
        `fetch gus opportunity details completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        platformEventMessage,
        opportunityDetails
      );
      syncPayload = {
        Application_Withdrawn_Reason__c: `${opportunityDetails["Application_Marked_for_Closure__c"]} || ${opportunityDetails["Closing_Comments__c"]}`,
      };

      const response = await this.syncInHZU(
        syncPayload,
        await this.getHZUOpportunityId(
          opportunityDetails["ApplicationFormId__c"],
          this.correlationId
        ),
        this.correlationId
      );

      if (response) {
        await this.log(
          platformEventMessage,
          `Sync withdraw in hzu completed`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.HZU_EDUCATIONCLOUD,
          syncPayload,
          response
        );
        return response
      }
    } catch (error) {
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      await this.error(
        platformEventMessage,
        error.message ? JSON.stringify(error.message) : JSON.stringify(error),
        loggerEnum.Event.WITHDRAW_APPLICATION_FAILED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        platformEventMessage
      );
      throw error;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message ? errorMessage.message : JSON.stringify(errorMessage),
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}



