import { handlerPath } from '@libs/handler-resolver';

export const gusSfUECampusNetIntegration = {
    handler: `${handlerPath(__dirname)}/gusSfUEIntegrationService.handleSfRequests`,
    name: 'gus-sf-ue-integration-${self:provider.stage}',
    events: [
        {
            sqs: {
                arn: '${self:provider.environment.GUS_SF_UE_INTEGRATION_SQS_QUEUE_ARN}',
            }
        }
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 180,
    memorySize: 512,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};

export const gusSfUECampusNetFailedRecordProcessor = {
    handler: `${handlerPath(__dirname)}/gusSfUEIntegrationService.handleFailedRecords`,
    name: 'gus-sf-ue-failed-record-processor-${self:provider.stage}',
    events: [
        {
            schedule: 'rate(60 minutes)',
        },
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 60,
    memorySize: 512,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};