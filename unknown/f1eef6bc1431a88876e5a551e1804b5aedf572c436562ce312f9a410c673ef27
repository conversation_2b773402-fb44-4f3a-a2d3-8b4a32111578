import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { SnsService } from "src/common/snsService";
import { getPicklistValue } from "src/common/getPickListValue";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const snsService = new SnsService();

export class OpportunityDocumentUploadHandler implements PlatformEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private email: string;
  async fetchGusSFOpportunityId(event: any, correlationId: string) {
    try {
      await this.log(
        event,
        `fetch opportunity id by application form id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        event
      );
      const response = await getData(
        `gus/opportunityId/${event.payload.GUS_Application_Form_ID__c}?scenario=${this.usecase}`,
        correlationId
      );
      await this.log(
        event,
        `fetch opportunity id by application form id completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        event,
        response
      );

      return response.Id;
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`)
    }
  }
  async fetchGusSFDetails(opportunityId: any, correlationId: string) {
    return await getData(`hzu/opportunityById/${opportunityId}?scenario=${this.usecase}`, correlationId);
  }
  async syncInHZU(
    event: any,
    transformedData: any,
    correlationId: string
  ): Promise<any> {
    const saveResponse = await postData(
      `gus/updateOpportunity/${event.payload.OpportunityID__c}?scenario=${this.usecase}`,
      transformedData,
      correlationId
    );
    return saveResponse;
  }
  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Inside handle message");
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      this.correlationId = platformEventMessage.event?.EventUuid;
      this.applicationFormId = platformEventMessage.payload.GUS_Application_Form_ID__c;
      this.brand = 'HZU';
      this.usecase = platformEventMessage.payload?.Scenario__c;
      this.email = platformEventMessage.payload?.Email_ID__c;
      console.log("PlatformEventMessage", platformEventMessage);
      const gusOpportunityId = await this.fetchGusSFOpportunityId(
        platformEventMessage,
        this.correlationId
      );
      await this.log(
        platformEventMessage,
        `fetch opportunity files by opportunity id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FILES_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage
      );
      let gusOpportunityFileResponse = await getData(
        `gus/getopportunityfiles/${gusOpportunityId}`
      );
      await this.log(
        platformEventMessage,
        `fetch opportunity files by opportunity id completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FILES_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        { opportunityId: gusOpportunityId },
        gusOpportunityFileResponse
      );
      await this.log(
        platformEventMessage,
        `fetch hzu application by external id initiated`,
        loggerEnum.Event.FETCH_HZU_APPLICATION_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage
      );
      const applicationResponse = await getData(
        `hzu/applicationIdByExternalId/${platformEventMessage.payload.GUS_Application_Form_ID__c}`,
        this.correlationId
      );
      await this.log(
        platformEventMessage,
        `fetch hzu application by external id completed`,
        loggerEnum.Event.FETCH_HZU_APPLICATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage,
        applicationResponse
      );

      if (applicationResponse?.Id) {
        await this.log(
          platformEventMessage,
          `fetch document checklist item by application id initiated`,
          loggerEnum.Event.FETCH_HZU_DOCUMENT_CHECKLIST_ITEM_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          platformEventMessage
        );

        const documentChecklistItemResponse = await getData(
          `hzu/documentChecklistItemDetails/${applicationResponse.Id}`,
          this.correlationId
        );
        await this.log(
          platformEventMessage,
          `fetch document checklist item by application id completed`,
          loggerEnum.Event.FETCH_HZU_DOCUMENT_CHECKLIST_ITEM_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          platformEventMessage,
          documentChecklistItemResponse
        );

        await this.log(
          platformEventMessage,
          `update hzu application initiated`,
          loggerEnum.Event.UPDATE_APPLICATION_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          platformEventMessage
        );
        const updateApplication = await postData(
          `hzu/updateApplication/${applicationResponse?.Id}`,
          { Platform_Event_Sync_Date__c: new Date().toISOString() }
        );
        await this.log(
          platformEventMessage,
          `update hzu application completed`,
          loggerEnum.Event.UPDATE_APPLICATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          platformEventMessage,
          updateApplication
        );

        console.log(updateApplication);
        const publishMessages = [];
        if (documentChecklistItemResponse && gusOpportunityFileResponse) {
          for (let opportunityFile of gusOpportunityFileResponse.response) {
            await this.log(
              platformEventMessage,
              `get hzu document type for ${opportunityFile?.DocumentType__c} initiated`,
              loggerEnum.Event.GET_DOCUMENT_TYPE,
              loggerEnum.Component.GUS_SALESFORCE,
              opportunityFile
            );
            const hzuDocumentType = await this.getHZUDocumentType(
              opportunityFile,
              platformEventMessage
            );
            await this.log(
              platformEventMessage,
              `get hzu document type for ${opportunityFile?.DocumentType__c} completed`,
              loggerEnum.Event.GET_DOCUMENT_TYPE,
              loggerEnum.Component.GUS_SALESFORCE,
              opportunityFile,
              hzuDocumentType
            );
            console.log(
              `hzuDocumentType for ${opportunityFile?.DocumentType__c}`,
              hzuDocumentType
            );
            if (hzuDocumentType) {
              const checklistItem = documentChecklistItemResponse.find(
                (checklistItem) =>
                  checklistItem.Name.toLowerCase() ===
                  hzuDocumentType.toLowerCase()
              );
              if (checklistItem) {
                const newEventmessage = { ...platformEventMessage };
                newEventmessage.payload.Id = checklistItem.Id;
                newEventmessage.payload.Name = checklistItem.Name;
                newEventmessage.payload.opportunityFileDetails = {
                  ...opportunityFile,
                };
                console.log("platformEventMessage", platformEventMessage);
                await this.log(
                  platformEventMessage,
                  `Publish message to sns initiated`,
                  loggerEnum.Event.PUBLISH_SNS_MSG,
                  loggerEnum.Component.GUS_SALESFORCE_OUTBOUND_SNS_TOPIC,
                  newEventmessage
                );
                const publishMessage = await snsService.publishMessages(
                  newEventmessage,
                  platformEventMessage.payload.GUS_Application_Form_ID__c,
                  process.env.GUS_SF_OUTBOUND_TOPIC_ARN,
                  "HZU"
                );
                publishMessages.push(publishMessage);
                if (publishMessage.MessageId) {
                  await this.log(
                    platformEventMessage,
                    `Publish message to sns completed`,
                    loggerEnum.Event.PUBLISH_SNS_MSG,
                    loggerEnum.Component.GUS_SALESFORCE_OUTBOUND_SNS_TOPIC,
                    newEventmessage,
                    publishMessage
                  );
                }
              }
            }
          }
          return publishMessages
        } else {
          await this.log(
            platformEventMessage,
            `Opportunity file response or document checklist response is empty`,
            loggerEnum.Event.OPERATION_COMPLETED,
            loggerEnum.Component.GUS_SALESFORCE_OUTBOUND_SNS_TOPIC,
            platformEventMessage
          );
        }
        return publishMessages;
      } else {
        throw new Error(
          `No herzing application found for gus application formId: ${platformEventMessage.payload.GUS_Application_Form_ID__c}`
        );
      }
    } catch (error) {
      throw error;
    }
  }
  async getHZUDocumentType(documentDetails, event): Promise<any> {
    if (documentDetails.DocumentType__c === "Degree transcripts") {
      let institutionDetails;
      if (documentDetails.Related_Education_History__c) {
        await this.log(
          event,
          `get education history by oppId initiated`,
          loggerEnum.Event.GET_EDU_HISTORY_INITIATED,
          loggerEnum.Component.GUS_EIP_SERVICE,
          `gus/geteducationhistorybyoppid/${documentDetails.Opportunity__c}`
        );
        const educationHistory = await getData(
          `gus/geteducationhistorybyoppid/${documentDetails.Opportunity__c}`,
          event?.event?.EventUuid,
        );
        await this.log(
          event,
          `get education history by oppId completed`,
          loggerEnum.Event.GET_EDU_HISTORY_COMPLETED,
          loggerEnum.Component.GUS_EIP_SERVICE,
          `gus/geteducationhistorybyoppid/${documentDetails.Opportunity__c}`,
          educationHistory
        );
        console.log("institutionDetails", educationHistory);
        institutionDetails = educationHistory.find(
          (item) => item.Id === documentDetails?.Related_Education_History__c
        );
        documentDetails.Additional_Info__c = institutionDetails.InstitutionName__c
      } else {
        await this.log(
          event,
          `get education history by additional info initiated`,
          loggerEnum.Event.GET_EDU_HISTORY_INITIATED,
          loggerEnum.Component.GUS_EIP_SERVICE,
          `gus/geteducationhistory?institutionName=${encodeURIComponent(
            documentDetails.Additional_Info__c
          )}&opportunityId=${documentDetails.Opportunity__c}`,
        );
        institutionDetails = await getData(
          `gus/geteducationhistory?institutionName=${encodeURIComponent(
            documentDetails.Additional_Info__c
          )}&opportunityId=${documentDetails.Opportunity__c}`,
          event?.event?.EventUuid
        );
        await this.log(
          event,
          `get education history by additional info completed`,
          loggerEnum.Event.GET_EDU_HISTORY_COMPLETED,
          loggerEnum.Component.GUS_EIP_SERVICE,
          `gus/geteducationhistory?institutionName=${encodeURIComponent(
            documentDetails.Additional_Info__c
          )}&opportunityId=${documentDetails.Opportunity__c}`,
          institutionDetails
        );
      }
      console.log("filtered", institutionDetails);
      const schoolLevelTypes = ["High School", "Home School", "GED"];
      if (
        institutionDetails &&
        schoolLevelTypes.includes(institutionDetails.Institution_Type__c) &&
        documentDetails.Opportunity__r.LevelCode__c === "UG"
      ) {
        return await getPicklistValue("DocumentType__c", "School");
      } else if (institutionDetails.Institution_Type__c === "University") {
        let type = await getPicklistValue("DocumentType__c", "College");
        return `${type} ${documentDetails.Additional_Info__c}`;
      }
    }
    const hzuDocumentType = await getPicklistValue(
      "DocumentType__c",
      documentDetails.DocumentType__c
    );
    return hzuDocumentType;
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
