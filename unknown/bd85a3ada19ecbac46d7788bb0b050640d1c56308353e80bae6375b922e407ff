# Dynamic Mapping Service

A flexible and powerful service for mapping data from Salesforce objects to specific response formats based on metadata definitions.

## Overview

The Dynamic Mapping Service provides a way to transform data from Salesforce objects (like Opportunity, Account, etc.) into specific response formats required by external systems. It uses metadata definitions to specify how fields should be mapped, and supports complex transformations including nested objects, arrays, and conditional formatting.

## Key Features

- **Metadata-driven mapping**: Define mappings in a declarative way using metadata
- **Support for nested objects**: Map complex nested structures
- **Support for arrays**: Map array elements with specific indices
- **Conditional formatting**: Format addresses differently based on citizenship status
- **Post-processing**: Apply additional transformations after mapping
- **SNS publishing**: Publish mapped data to SNS topics

## Components

The service consists of the following components:

1. **DynamicMappingService**: Core service for mapping data based on metadata
2. **MappingMetadata**: Definitions of how to map fields from source to target
3. **DynamicMapper**: High-level mapper that uses the service and metadata
4. **Examples**: Sample code showing how to use the service

## Usage

### Basic Usage

```typescript
import dynamicMapper from '../common/dynamicMapper';

// Map Salesforce data to a person response
const mappedPerson = dynamicMapper.mapToPerson(salesforceData);

// Map Salesforce data to an application response
const mappedApplication = dynamicMapper.mapToApplication(salesforceData);

// Map Salesforce data to an academic history response
const mappedAcademicHistory = dynamicMapper.mapToAcademicHistory(salesforceData);

// Map Salesforce data to an assessment response
const mappedAssessment = dynamicMapper.mapToAssessment(salesforceData);
```

### Publishing to SNS

```typescript
// Map and publish to SNS
const snsResponse = await dynamicMapper.publishToSNS(
  mappedData,
  'arn:aws:sns:eu-west-1:************:DEV-UNFC-OUTBOUND-TOPIC.fifo'
);
```

### Complete Example

```typescript
import dynamicMapper from '../common/dynamicMapper';

// Sample Salesforce data
const salesforceData = {
  Opportunity: {
    Id: '********-0000-0000-0000-********0000',
  },
  Account: {
    FirstName: 'Test',
    LastName: '123',
    // ... other fields
  }
};

// Map to person format
const mappedPerson = dynamicMapper.mapToPerson(salesforceData);

// Publish to SNS
const snsResponse = await dynamicMapper.publishToSNS(
  mappedPerson,
  'arn:aws:sns:eu-west-1:************:DEV-UNFC-OUTBOUND-TOPIC.fifo'
);
```

## Customizing Mappings

You can customize the mappings by modifying the metadata definitions in `mappingMetadata.ts`. Each mapping specifies how a field in the source object maps to a field in the target object.

### Example Mapping

```typescript
export const personMappingMetadata = {
  'id': 'Opportunity.Id',
  'names[0].firstName': 'Account.FirstName',
  'names[0].lastName': 'Account.LastName',
  // ... other mappings
};
```

In this example:
- `'id'` in the target maps to `Opportunity.Id` in the source
- `'names[0].firstName'` in the target maps to `Account.FirstName` in the source
- `'names[0].lastName'` in the target maps to `Account.LastName` in the source

## Running Tests

To run the tests, execute the `testRunner.ts` file:

```bash
ts-node src/examples/testRunner.ts
```

This will run all the test cases with sample data and output the results.

## Dependencies

- `aws-sdk`: For SNS publishing
- `i18n-iso-countries`: For handling ISO 3166-1 alpha-3 country codes
- `libphonenumber-js`: For parsing and formatting phone numbers
