import { DynamoDBService } from "src/common/dynamodbService";
import { SnsService } from "src/common/snsService";
import { PlatformEventHandlerFactory } from './eventHandlerFactory';
import { storeFailedRecords } from "src/common/storeFailedRecords";
import { checkExistingMessageGroupId } from "src/common/checkFailedRecords";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerEnum = new LoggerEnum();
const dbService = new DynamoDBService()
const snsService = new SnsService()

export const handleSfRequests = async (event) => {
    const clonedEvent = deepClone(event);
    console.log('clonedEvent-->', clonedEvent)
    for (const record of clonedEvent.Records) {
        try {
            const isFailedMessageGroupData = await checkExistingMessageGroupId(record, process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME, 'GUS_SF_HZU_EDUCATIONCLOUD');
            if (isFailedMessageGroupData === "No messages to process") {
                const eventBody = JSON.parse(record.body);
                console.log('eventBody', eventBody);
                const platformEventMessage = JSON.parse(eventBody.Message);
                const handler = PlatformEventHandlerFactory.getHandler(platformEventMessage.payload.Scenario__c);
                if (handler && typeof handler.handleMessage === 'function') {
                    const response = await handler.handleMessage(record);
                    console.log('response -->', response)
                    //update failed record status
                    if (response && platformEventMessage.status === "Failed") {
                        await dbService.deleteItem(
                            process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                            {
                                PK: `GUS_SF_HZU_EDUCATIONCLOUD#${record?.attributes?.MessageGroupId}`,
                                SK: platformEventMessage?.uuid || record?.messageId
                            },
                        )

                        const queryParams = {
                            TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                            KeyConditionExpression: "PK = :pkValue",
                            ExpressionAttributeValues: {
                                ":pkValue": `GUS_SF_HZU_EDUCATIONCLOUD#${record.attributes.MessageGroupId}`,
                            },
                        };

                        const checkExcistingFailedRecordForMessageGrpId = await dbService.queryObjects(queryParams)
                        console.log('checkExcistingFailedRecordForMessageGrpId -->', checkExcistingFailedRecordForMessageGrpId)

                        if (checkExcistingFailedRecordForMessageGrpId.Items.length === 0) {
                            await dbService.deleteItem(
                                process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                                {
                                    PK: "GUS_SF_HZU_EDUCATIONCLOUD",
                                    SK: record.attributes.MessageGroupId
                                }
                            )
                        }
                    }
                    console.log('Response:', response);
                } else {
                    console.log('Response:', null);
                }
            }
        } catch (error) {
            console.log('Error ->', error);
            await storeFailedRecords(record, process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME, 'GUS_SF_HZU_EDUCATIONCLOUD');
        }
    }
};

const deepClone = (obj) => {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map(item => deepClone(item));
    }
    const clonedObj = {};
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            clonedObj[key] = deepClone(obj[key]);
        }
    }
    return clonedObj;
};
export const handleFailedRecords = async () => {
    try {
        const params = {
            TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            KeyConditionExpression: 'PK = :partitionKey',
            ExpressionAttributeValues: {
                ':partitionKey': 'GUS_SF_HZU_EDUCATIONCLOUD',
            },
        }

        const partitionResponse = await dbService.queryObjects(params)
        console.log("PartitionItemData -->", partitionResponse)
        if (partitionResponse.Items && partitionResponse.Items.length > 0) {
            for (const partitionItem of partitionResponse.Items) {
                console.log("Item -->", partitionItem)
                if (partitionItem.status === "Failed" && (partitionItem.retryCount <= 3 || !partitionItem.retryCount)) {
                    const params = {
                        TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                        KeyConditionExpression: 'PK = :partitionKey',
                        ExpressionAttributeValues: {
                            ':partitionKey': `${partitionItem.PK}#${partitionItem.SK}`,
                        },
                    }
                    const records = await dbService.queryObjects(params)
                    for (const record of records.Items) {
                        const eventBody = JSON.parse(record.body);
                        const eventMessage = JSON.parse(eventBody.Message);
                        eventMessage.status = "Failed"
                        console.log('Event message ->', eventMessage)
                        const publishMessages = await snsService.publishMessages(
                            eventMessage,
                            eventMessage.payload.Application_Form_Id__c || eventMessage.payload.GUS_Application_Form_ID__c,
                            process.env.GUS_SF_OUTBOUND_TOPIC_ARN,
                            'HZU'
                        );

                        console.log(publishMessages)
                        // if (publishMessages) {
                        //     const deleteResponse = await dbService.deleteItem(
                        //         process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                        //         {
                        //             PK: `${partitionItem.PK}#${partitionItem.SK}`,
                        //             SK: (eventMessage.event.replayId).toString()
                        //         }
                        //     );
                        //     console.log('Deleted item:', deleteResponse);
                        // } else {
                        //     throw new Error('Error publishing messages');
                        // }
                    }
                    const currentRetryCount = partitionItem.retryCount || 0;
                    await dbService.updateObject(
                        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                        {
                            PK: partitionItem.PK,
                            SK: partitionItem.SK
                        },
                        {
                            retryCount: currentRetryCount + 1,
                        }
                    );

                }
            }
            console.log('All records processed successfully')
            return 'All records processed successfully';
        } else {
            return 'No records to process';
        }
    } catch (error) {
        throw new Error(error)
    }
}
