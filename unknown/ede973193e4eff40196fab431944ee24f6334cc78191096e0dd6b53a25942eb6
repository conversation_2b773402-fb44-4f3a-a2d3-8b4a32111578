import { ApplicationData } from "../models/application-creation.interface";

export enum DocumentTypeEnum {
  CV = "CV",
  ID_PASSPORT = "ID/Passport",
  DEGREE_CERTIFICATE = "Degree Certificate",
  PHOTOGRAPH = "Photograph",
  APPLICATION_FORM = "Application form",
  DEGREE_TRANSCRIPTS = "Degree transcripts",
  APPLY_REASON = "Letter of Motivation"
}

export const documentTypeMapping: Record<string, keyof ApplicationData> = {
  [DocumentTypeEnum.CV]: "fileCV",
  [DocumentTypeEnum.ID_PASSPORT]: "filePassport",
  [DocumentTypeEnum.DEGREE_CERTIFICATE]: "educationSchoolsCertificateFile",
  [DocumentTypeEnum.PHOTOGRAPH]: "photo",
  [DocumentTypeEnum.APPLICATION_FORM]: "applicationForm",
  [DocumentTypeEnum.DEGREE_TRANSCRIPTS]: "educationSchoolsTranscriptFile",
  [DocumentTypeEnum.APPLY_REASON]: "applyReasonFile",
};
