// PlatformEventHandlerFactory.ts

// **Ensure all decorated handlers are loaded so they can register themselves**
import "./eventHandlers/documentSyncHandler";
import "./eventHandlers/newApplicationHandler";
import "./eventHandlers/withdrawApplicationHandler";

import { getRegisteredHandler } from "./HandlerRegistry";

export class PlatformEventHandlerFactory {
  static getHandler(scenario: string) {
    const handler = getRegisteredHandler(scenario);
    if (!handler) {
      console.error(`No handler found for scenario: ${scenario}`);
    }
    return handler;
  }
}
