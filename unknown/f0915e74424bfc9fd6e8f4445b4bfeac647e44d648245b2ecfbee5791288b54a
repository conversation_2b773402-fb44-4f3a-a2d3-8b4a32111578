import {
  salesforceSubObjectsConfig,
  salesforceConfig,
} from "../salesforceconfig";
import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { getPicklistValue } from "src/common/getPickListValue";


const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();


export class NewApplicationHandler implements PlatformEventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private email: string;
  async fetchGusSFDetails(event: any, correlationId): Promise<any> {
    try {
      await this.log(
        event,
        `fetch gus object details for ${event.payload.Opportunity_Id__c} initiated`,
        loggerEnum.Event.FETCH_GUS_APPLICATION_INTIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        event
      );
      return await getData(
        `gus/getapplicationsdetails/${event.payload.Opportunity_Id__c}?scenario=${this.usecase}`,
        correlationId
      );
    } catch (error) {
      throw new Error(`Error fetching gus object details: ${error}`);
    }
  }
  async syncInHZU(transformedData: any, correlationId): Promise<any> {
    try {
      const saveResponse = await postData(
        "hzu/createApplication",
        transformedData,
        correlationId
      );

      if (Array.isArray(saveResponse)) {
        const hasErrors = saveResponse.some((response) => {
          const statusCode = response.httpStatusCode;
          return statusCode !== 200 && statusCode !== 201 && statusCode !== 204;
        });

        if (hasErrors) {
          throw new Error(JSON.stringify(saveResponse));
        }
      }

      return saveResponse;
    } catch (error) {
      throw new Error(`Error syncing in hzu: ${error}`);
    }
  }
  async updateCustomFields(applicationResponse: any): Promise<any> {
    try {
      applicationResponse.Account["AccountRecordType"] = await getPicklistValue(
        "RecordTypeId",
        "Account"
      );
      applicationResponse.Account["ContactRecordType"] = await getPicklistValue(
        "RecordTypeId",
        "Contact"
      );
      const AccountName = `${applicationResponse.Account.FirstName} ${applicationResponse.Account.LastName} Administrative Account`;
      applicationResponse.Account["AccountName"] = AccountName;
      applicationResponse.Account["Preferred_Email__c"] = "Personal Email";
      applicationResponse.Application__c["Application_Status__c"] = "Started";
      applicationResponse.Application__c["Application_Date__c"] = new Date();
      applicationResponse.Application__c[
        "EmergencyContactName"
      ] = `${applicationResponse.Application__c["First_Name_Emergency__c"]} ${applicationResponse.Application__c["Surname_Emergency__c"]}`;
      let miscDetails =
        applicationResponse.Opportunity.Application_Misc_Details__c;
      if (miscDetails) {
        const fields = [
          "Citizenship_Status__c",
          "Ethinicity__c",
          "Race__c",
          "Preferred_Contact_Time__c",
          "Highschool_Completion_Status__c",
        ];
        let miscDetailsObject = JSON.parse(miscDetails);

        fields.forEach((field) => {
          applicationResponse.Opportunity[field] = miscDetailsObject[field];
        });
      }
      if (!applicationResponse.Lead) {
        applicationResponse.Lead = {};
      }
      applicationResponse.Lead["LeadSource__c"] = "IS-GUS Gateway";
      applicationResponse.Opportunity["Affiliated_Account_Name__c"] =
        "IS-InUni/GUS";
      applicationResponse.Opportunity["Affiliated_Account_Type__c"] =
        "International";
      applicationResponse.Opportunity["CampusAccountId"] = "0014x00000dz8mVAAQ";
      applicationResponse.Opportunity["Type"] = "New";
      applicationResponse.Opportunity["OwnerId"] = process.env.HZU_OWNER_ID;
      applicationResponse.Opportunity["Serviced_by__c"] = "Campus";
      const defaultEduAccountId = '0014x00000pqFnmAAE';
      if (applicationResponse.EducationHistoryRecord__c && applicationResponse.EducationHistoryRecord__c.length > 0) {
        applicationResponse.EducationHistoryRecord__c.forEach(record => {
          if (!record.hed__Account__c) {
            record.hed__Account__c = defaultEduAccountId;
          }
        });
      }
    } catch (error) {
      throw new Error(`Error updateCustomFields: ${error}`);
    }
  }


  async handleMessage(event: any): Promise<void> {
    let transformedData: any;
    try {
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      this.correlationId = platformEventMessage.event?.EventUuid;
      this.usecase = platformEventMessage?.payload?.Scenario__c;
      this.applicationFormId = platformEventMessage?.payload?.Application_Form_Id__c;
      this.brand = platformEventMessage?.payload?.BusinessUnitFilter__c;
      this.email = platformEventMessage?.payload?.Email__c

      let applicationResponse = await this.fetchGusSFDetails(
        platformEventMessage,
        this.correlationId
      );

      await this.log(
        platformEventMessage,
        `fetch gus opportunity details completed`,
        loggerEnum.Event.FETCH_GUS_APPLICATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage,
        applicationResponse
      );

      await this.updateCustomFields(applicationResponse);
      transformedData = await this.transformData(
        applicationResponse,
        salesforceConfig
      );

      console.log("transformedData", transformedData);
      transformedData["eventUuid"] = platformEventMessage.event?.EventUuid;
      transformedData["applicationId"] =
        platformEventMessage.payload.Application_Form_Id__c;
      transformedData["gusOpportunityId"] =
        platformEventMessage.payload.Opportunity_Id__c;
      transformedData["scenario"] =
        platformEventMessage.payload.Scenario__c;
      await this.log(
        platformEventMessage,
        `sync in hzu initiated`,
        loggerEnum.Event.SYNC_IN_HZU_INITIATED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        transformedData,
      )
      const response = await this.syncInHZU(transformedData, this.correlationId);
      if (response) {
        await this.log(
          platformEventMessage,
          'create hzu application completed',
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.HZU_EDUCATIONCLOUD,
          transformedData,
          response
        );
      }
      return response;
    } catch (error) {
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      console.log("Error ---->", error);

      await this.error(
        platformEventMessage,
        error.message ? JSON.stringify(error.message) : JSON.stringify(error),
        loggerEnum.Event.CREATE_APPLICATION_FAILED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        transformedData
      );

      let errorDetails;
      if (error instanceof Error) {
        errorDetails = JSON.stringify({
          message: error.message,
          stack: error.stack,
          ...error,
        });
      } else {
        errorDetails = JSON.stringify(error);
      }
      throw errorDetails;
    }
  }

  pickListFields = {
    StageName: "Opportunity",
    hed__Gender__c: "Contact",
    apex_Marital_Status__c: "Contact",
    apex_Transferring_Credits__c: "EducationHistoryRecord__c",
    apex_Preferred_Contact_Method__c: "Contact",
    apex_Best_Time_To_Contact__c: "Contact",
    apex_Prior_Conviction__c: "Contact",
    apex_Campus_Code_Picklist__c: "Contact",
    apex_How_Did_You_Hear_About_Us__c: "hed__Application__c",
    apex_Application_Campus__c: "hed__Application__c",
    apex_Tuition_Reimbursement_By_Employer__c: "hed__Application__c",
    hed__Citizenship_Status__c: "Contact",
    apex_Visa_Type__c: "hed__Application__c",
    apex_expected_start_date__c: "Opportunity",
    apex_Application_Term__c: "hed__Application__c",
    hed__Term__c: "hed__Application__c",
    apex_First_Generation_College_Student__c: "Contact",
    apex_Current_Employee_Of_Herzing__c: "hed__Application__c",
    apex_Institution_Type__c: "EducationHistoryRecord__c",
    apex_Application_Program__c: "hed__Application__c",
    hed__Applying_To__c: "hed__Application__c",
    apex_program__c: "Opportunity",
    Name: "Opportunity",
    apex_Applying_To__c: "Opportunity",
    apex_Campus__c: ["hed__Application__c", "Opportunity"],
    Degree_Earned_Student_Provided__c: ["EducationHistoryRecord__c"],
  };

  async transformData(input, mapping) {
    try {
      const output = {};

      for (const [outputKey, fieldsMapping] of Object.entries(mapping)) {
        if (Array.isArray(fieldsMapping)) {
          output[outputKey] = [];

          const subObjectMapping = fieldsMapping[0];
          if (input[subObjectMapping]) {
            for (const record of input[subObjectMapping]) {
              const transformedData = await this.transformArrayData(
                {
                  ...record,
                },
                salesforceSubObjectsConfig[subObjectMapping],
                subObjectMapping
              );
              output[outputKey].push(transformedData);
            }
          }
        } else {
          output[outputKey] = {};
          for (const [inputPath, outputField] of Object.entries(
            fieldsMapping
          )) {
            const keys = inputPath.split(".");
            let currentValue = input;
            for (const key of keys) {
              if (currentValue && currentValue[key] !== undefined) {
                currentValue = currentValue[key];
              } else {
                currentValue = null;
                break;
              }
            }

            if (currentValue !== null) {
              if (
                typeof currentValue === "string" &&
                currentValue.match(/^\d{4}-\d{2}-\d{2}$/)
              ) {
                currentValue = this.convertDateFormat(currentValue);
              }

              if (Array.isArray(outputField)) {
                for (const field of outputField) {
                  if (
                    this.pickListFields.hasOwnProperty(field) &&
                    ((Array.isArray(this.pickListFields[field]) &&
                      this.pickListFields[field].includes(outputKey)) ||
                      this.pickListFields[field] === outputKey)
                  ) {
                    const pickListValue = await getPicklistValue(
                      field,
                      currentValue
                    );
                    await this.setValueToPath(
                      output[outputKey],
                      field,
                      pickListValue
                    );
                  } else {
                    await this.setValueToPath(
                      output[outputKey],
                      field,
                      currentValue
                    );
                  }
                }
              } else {
                if (
                  this.pickListFields.hasOwnProperty(outputField) &&
                  ((Array.isArray(this.pickListFields[outputField]) &&
                    this.pickListFields[outputField].includes(outputKey)) ||
                    this.pickListFields[outputField] === outputKey)
                ) {
                  const pickListValue = await getPicklistValue(
                    outputField,
                    currentValue
                  );
                  await this.setValueToPath(
                    output[outputKey],
                    outputField,
                    pickListValue
                  );
                } else {
                  await this.setValueToPath(
                    output[outputKey],
                    outputField,
                    currentValue
                  );
                }
              }
            }
          }
        }
      }

      return output;
    } catch (error) {
      throw new Error(`Error in transformData: ${JSON.stringify(error.message)}`);
    }
  }

  async transformArrayData(
    input: any,
    mapping: Record<string, string>,
    outputKey
  ) {
    try {
      const output: Record<string, any> = {};

      for (const [inputPath, outputField] of Object.entries(mapping)) {
        const keys = inputPath.split(".");
        let currentValue: any = input;
        for (const key of keys) {
          if (currentValue && currentValue[key] !== undefined) {
            currentValue = currentValue[key];
          } else {
            currentValue = null;
            break;
          }
        }

        if (currentValue !== null) {
          if (
            typeof currentValue === "string" &&
            currentValue.match(/^\d{4}-\d{2}-\d{2}$/)
          ) {
            currentValue = this.convertDateFormat(currentValue);
          }

          const outputFieldString = outputField as string;

          if (
            this.pickListFields.hasOwnProperty(outputFieldString) &&
            ((Array.isArray(this.pickListFields[outputFieldString]) &&
              this.pickListFields[outputFieldString].includes(outputKey)) ||
              this.pickListFields[outputFieldString] === outputKey)
          ) {
            const pickListValue = await getPicklistValue(
              outputFieldString,
              currentValue
            );
            output[outputFieldString] = pickListValue;
          } else {
            output[outputFieldString] = currentValue;
          }
        }
      }

      return output;
    } catch (error) {
      console.log(error);
    }
  }

  convertDateFormat(dateString) {
    const [year, month, day] = dateString.split("-");
    return `${year}-${month}-${day}`;
  }

  async setValueToPath(obj, path, value): Promise<void> {
    const keys = path.split(".");
    let currentObj = obj;

    keys.forEach((key, index) => {
      if (index === keys.length - 1) {
        currentObj[key] = value;
      } else {
        if (!currentObj[key]) {
          currentObj[key] = {};
        }
        currentObj = currentObj[key];
      }
    });
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }

  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }

}



