import { PlatformEventHandlerFactory } from "./eventsHandlerFactory";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { storeFailedRecords } from "src/common/storeFailedRecords";
import { checkExistingMessageGroupId } from "src/common/checkFailedRecords";
import { DynamoDBService } from "src/common/dynamodbService";
import { SnsService } from "src/common/snsService";

const dbService = new DynamoDBService();
const snsService = new SnsService();
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

export const handleSfRequests = async (event) => {
  console.log("Event", JSON.stringify(event));
  for (let record of event.Records) {
    try {
      const isFailedMessageGroupData = await checkExistingMessageGroupId(
        record,
        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
        "HZU_EDUCATIONCLOUD_GUS_SF"
      );
      console.log("isFailedMessageGroupData ", isFailedMessageGroupData);
      if (isFailedMessageGroupData === "No messages to process") {
        const eventBody = JSON.parse(record.body);
        const platformEventMessage = JSON.parse(eventBody.Message);
        console.log("PlatformEventMessage", platformEventMessage);
        const handler = PlatformEventHandlerFactory.getHandler(
          platformEventMessage.payload.Scenario__c
        );
        if (handler && typeof handler.handleMessage === "function") {
          const response = await handler.handleMessage(record);
          console.log("response -->", response);
          if (response && platformEventMessage.status === "Failed") {
            await dbService.deleteItem(
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
              {
                PK: `HZU_EDUCATIONCLOUD_GUS_SF#${record?.attributes?.MessageGroupId}`,
                SK: platformEventMessage?.uuid || record?.messageId
              },
            )

            const queryParams = {
              TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
              KeyConditionExpression: "PK = :pkValue",
              ExpressionAttributeValues: {
                ":pkValue": `HZU_EDUCATIONCLOUD_GUS_SF#${record?.attributes?.MessageGroupId}`,
              },
            };

            const checkExcistingFailedRecordForMessageGrpId = await dbService.queryObjects(queryParams)
            console.log('checkExcistingFailedRecordForMessageGrpId -->', checkExcistingFailedRecordForMessageGrpId)

            if (checkExcistingFailedRecordForMessageGrpId.Items.length === 0) {
              await dbService.deleteItem(
                process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                {
                  PK: "HZU_EDUCATIONCLOUD_GUS_SF",
                  SK: record?.attributes?.MessageGroupId
                }
              )
            }
          }
          console.log("Response:", response);
        } else {
          console.log("Response:", null);
        }
      }
    } catch (error) {
      console.log("Error -->", error);
      await storeFailedRecords(
        record,
        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
        "HZU_EDUCATIONCLOUD_GUS_SF"
      );
      const eventBody = JSON.parse(record.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      await loggerService.error(
        platformEventMessage.event?.EventUuid,
        new Date().toISOString(),
        loggerEnum.Component
          .HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_HANDLER,
        loggerEnum.Component
          .HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_QUEUE,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SYNC_APPLICATION_STATUS_UPDATE,
        platformEventMessage.payload?.Scenario__c,
        platformEventMessage,
        platformEventMessage,
        error.message ? JSON.stringify(error.message) : JSON.stringify(error),
        platformEventMessage.payload?.BusinessUnitFilter__c,
        platformEventMessage.payload?.Email_ID__c,
        "Application_Form_Id__c",
        platformEventMessage.payload.GUS_Application_Form_ID__c,
        "Opportunity",
        platformEventMessage.payload.GUS_Application_Form_ID__c,
        "Opportunity",
        ""
      );
    }
  }
};

export const handleFailedRecords = async () => {
  try {
    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": "HZU_EDUCATIONCLOUD_GUS_SF",
      },
    };

    const partitionResponse = await dbService.queryObjects(params);
    console.log("PartitionItemData -->", partitionResponse);
    if (partitionResponse.Items && partitionResponse.Items.length > 0) {
      for (const partitionItem of partitionResponse.Items) {
        console.log("Item -->", partitionItem);
        if (
          partitionItem.status === "Failed" &&
          (partitionItem.retryCount <= 3 || !partitionItem.retryCount)
        ) {
          const params = {
            TableName:
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            KeyConditionExpression: "PK = :partitionKey",
            ExpressionAttributeValues: {
              ":partitionKey": `${partitionItem.PK}#${partitionItem.SK}`,
            },
          };
          const records = await dbService.queryObjects(params);
          console.log("Records -->", records);
          for (const record of records.Items) {
            console.log("Current Record -->", record);
            const eventBody = JSON.parse(record.body);
            const eventMessage = JSON.parse(eventBody.Message);
            eventMessage.status = "Failed";
            console.log("Event message ->", eventMessage);
            const publishMessages = await snsService.publishMessages(
              eventMessage,
              eventMessage.payload.GUS_Application_Form_ID__c,
              process.env.HZU_EDUCATIONCLOUD_OUTBOUND_TOPIC_ARN
            );
            console.log("publishMessages", publishMessages);
            // if (publishMessages) {
            //     console.log("KEY ->", {
            //         PK: `${partitionItem.PK}#${partitionItem.SK}`,
            //         SK: eventMessage.event.replayId
            //     })
            //     const deleteResponse = await dbService.deleteItem(
            //         process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            //         {
            //             PK: `${partitionItem.PK}#${partitionItem.SK}`,
            //             SK: (eventMessage.event.replayId).toString()
            //         }
            //     );
            //     console.log('Deleted item:', deleteResponse);
            // } else {
            //     throw new Error('Error publishing messages');
            // }
          }
          const currentRetryCount = partitionItem.retryCount || 0;
          await dbService.updateObject(
            process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            {
              PK: partitionItem.PK,
              SK: partitionItem.SK,
            },
            {
              retryCount: currentRetryCount + 1,
            }
          );
        }
      }
      console.log("All records processed successfully");
      return "All records processed successfully";
    } else {
      return "No records to process";
    }
  } catch (error) {
    throw new Error(error);
  }
};
