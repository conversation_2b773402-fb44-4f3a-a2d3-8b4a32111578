import { DynamoDBService } from "src/common/dynamodbService";
import { SnsService } from "src/common/snsService";
import { EventHandlerFactory } from "./eventHandlerFactory";
import { storeFailedRecords } from "src/common/storeFailedRecords";
import { checkExistingMessageGroupId } from "src/common/checkFailedRecords";
import { LoggerEnum } from "@gus-eip/loggers";

const loggerEnum = new LoggerEnum();
const dbService = new DynamoDBService();
const snsService = new SnsService();

export const handleSfRequests = async (event) => {
  const clonedEvent = deepClone(event);
  console.log("clonedEvent-->", clonedEvent);
  for (const record of clonedEvent.Records) {
    try {
      const isFailedMessageGroupData = await checkExistingMessageGroupId(record, process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME, 'GUS_SF_MYUCW');
      if (isFailedMessageGroupData === "No messages to process") {
        const eventBody = JSON.parse(record.body);
        console.log("eventBody", eventBody);
        const platformEventMessage = JSON.parse(eventBody.Message);
        const handler = EventHandlerFactory.getHandler(
          platformEventMessage.payload.Scenario__c
        );
        if (handler && typeof handler.handleMessage === "function") {
          const response = await handler.handleMessage(record);
          // update failed record status
          console.log("Response", response)
          if (response && platformEventMessage.status === "Failed") {
            await dbService.deleteItem(
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
              {
                PK: `GUS_SF_MYUCW#${record?.attributes?.MessageGroupId}`,
                SK: platformEventMessage?.uuid || record?.messageId
              },
            )

            const queryParams = {
              TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
              KeyConditionExpression: "PK = :pkValue",
              ExpressionAttributeValues: {
                ":pkValue": `GUS_SF_MYUCW#${record.attributes.MessageGroupId}`,
              },
            };

            const checkExcistingFailedRecordForMessageGrpId = await dbService.queryObjects(queryParams)
            console.log('checkExcistingFailedRecordForMessageGrpId -->', checkExcistingFailedRecordForMessageGrpId)

            if (checkExcistingFailedRecordForMessageGrpId.Items.length === 0) {
              await dbService.deleteItem(
                process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                {
                  PK: "GUS_SF_MYUCW",
                  SK: record.attributes.MessageGroupId
                }
              )
            }
          }
          console.log("Response:", response);
        } else {
          console.log("Response:", null);
        }
      }
    } catch (error) {
      console.log("ERROR ->", error);
      await storeFailedRecords(
        record,
        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
        "GUS_SF_MYUCW"
      );
    }
  }
};

const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item));
  }
  const clonedObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }
  return clonedObj;
};

//Handle failed records
export const handleFailedRecords = async () => {
  try {
    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": "GUS_SF_MYUCW",
      },
    };

    const partitionResponse = await dbService.queryObjects(params);
    console.log("PartitionItemData -->", partitionResponse);
    if (partitionResponse.Items && partitionResponse.Items.length > 0) {
      for (const partitionItem of partitionResponse.Items) {
        console.log("Item -->", partitionItem);
        if (partitionItem.status === "Failed" && (partitionItem.retryCount <= 3 || !partitionItem.retryCount)) {
          const params = {
            TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            KeyConditionExpression: "PK = :partitionKey",
            ExpressionAttributeValues: {
              ":partitionKey": `${partitionItem.PK}#${partitionItem.SK}`,
            },
          };
          const records = await dbService.queryObjects(params);
          for (const record of records.Items) {
            const eventBody = JSON.parse(record.body);
            const eventMessage = JSON.parse(eventBody.Message);
            eventMessage.status = "Failed"
            console.log("Event message ->", eventMessage);
            const publishMessages = await snsService.publishMessages(
              eventMessage,
              eventMessage.payload.Application_Form_Id__c,
              process.env.GUS_SF_OUTBOUND_TOPIC_ARN,
              "UCW"
            );

            console.log("Publish messages", publishMessages)
          }
          const currentRetryCount = partitionItem.retryCount || 0;
          await dbService.updateObject(
            process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            {
              PK: partitionItem.PK,
              SK: partitionItem.SK,
            },
            {
              retryCount: currentRetryCount + 1,
            }
          );
        }
      }
      return 'All records processed successfully';
    } else {
      return "No records to process";
    }
  } catch (error) {
    throw new Error(error);
  }
};
