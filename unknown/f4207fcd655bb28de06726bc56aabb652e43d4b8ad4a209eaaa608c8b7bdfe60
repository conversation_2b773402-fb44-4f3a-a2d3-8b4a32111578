import { gusMailingService } from "./mailingService";

export const gusMailingHandler: any = async (event) => {
  const clonedEvent = deepClone(event);
  console.log("clonedEvent-->", clonedEvent);
  for (const record of clonedEvent.Records) {
    try {
      const eventBody = JSON.parse(record.body);
      console.log("eventBody", eventBody);
      const platformEventMessage = JSON.parse(eventBody.Message);
      await gusMailingService(platformEventMessage)
    } catch (error) {
      console.log("Error", error)
      throw error
    }
  }
};
const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item));
  }
  const clonedObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }
  return clonedObj;
};
export const handleGusMail = gusMailingHandler;



export const gusDepositEmailStatusTracker: any = async (event) => {
  console.log("Event -->", JSON.stringify(event))
}