/**
 * Utility class for handling ignorable errors in UCW integration handlers
 */
export class UCWErrorHandler {
  /**
   * Configuration mapping UCW scenarios to their ignorable error patterns
   */
  private static readonly IGNORABLE_ERRORS: Record<string, string[]> = {
    UCW_OFFERED: [
      "Added identical opportunity file for opportunity"
    ],
    UCW_WITHDRAW_OFFER: [
      "A Closed Won opportunity  Stage can not be edited."
    ],
    UCW_REJECT_APPLICATION_VS: [
      "A Closed Won opportunity  Stage can not be edited."
    ],
    UCW_REJECT_DOCUMENT: [
      "No Accepted documents found for"
    ]
  };

  /**
   * Check if an error should be ignored for a specific UCW scenario
   * @param error - The error object or string
   * @param scenario - The UCW scenario (e.g., "UCW_OFFERED", "UCW_WITHDRAW_OFFER", "UCW_REJECT_APPLICATION_VS")
   * @returns boolean - true if the error should be ignored
   */
  public static isIgnorableError(error: any, scenario: string): boolean {
    const errorMessage = this.extractErrorMessage(error);
    const ignorablePatterns = this.IGNORABLE_ERRORS[scenario];
    
    if (!ignorablePatterns) {
      return false;
    }

    return ignorablePatterns.some(pattern => 
      errorMessage.includes(pattern)
    );
  }

  /**
   * Extract error message from various error formats
   * @param error - The error object, string, or other format
   * @returns string - The extracted error message
   */
  private static extractErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.response?.data) {
      return typeof error.response.data === 'string' 
        ? error.response.data 
        : JSON.stringify(error.response.data);
    }
    
    return JSON.stringify(error);
  }

  /**
   * Get a descriptive message for logging when an error is ignored
   * @param error - The error that was ignored
   * @param scenario - The UCW scenario
   * @returns string - A descriptive message for logging
   */
  public static getIgnoredErrorLogMessage(error: any, scenario: string): string {
    const errorMessage = this.extractErrorMessage(error);
    return `Ignoring expected error for ${scenario}: ${errorMessage}`;
  }
}
