import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { getPicklistValue } from "src/common/getPickListValue";
import { decode } from "he";
import { LoggerEnum } from "@gus-eip/loggers";
import { UCWErrorHandler } from "../utils/errorHandler";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
export class OfferHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private errors: Error[] = [];
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        sid,
        error,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInGus(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(
        new Error(`Error syncing opportunity details: ${error}`)
      );
    }
  }
  private async updateGusAccount(
    updateRequest,
    AccountId,
    files?
  ): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateAccount/${AccountId}`,
        updateRequest,
        this.correlationId,
        process.env.UCW_KEY,
        "PATCH"
      );
      await this.log(
        updateRequest,
        "Account update completed",
        files > 0
          ? loggerEnum.Event.UPDATE_ACCOUNT_COMPLETED
          : loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        "Account",
        saveResponse
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        updateRequest,
        error,
        loggerEnum.Event.UPDATE_ACCOUNT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(new Error(`Error updating account: ${error}`));
    }
  }
  async handleMessage(event: any): Promise<any> {
    this.usecase = event?.scenario;
    this.correlationId = event?.eventId;
    this.brand = "UCW";
    const {
      files = [],
      Hs_lv_cgpaeq,
      CN_approval_notes,
      GUS_appid,
      status,
      sprogramID,
    } = event?.["payload"] || {};

    this.applicationFormId = GUS_appid || sprogramID;
    await this.log(
      event,
      `offer stage handler initiated`,
      loggerEnum.Event.OFFER_INITIATED
    );
    if (!sprogramID) {
      await this.error(
        event,
        "sprogramID not found",
        loggerEnum.Event.SPROGRAMID_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`sprogramID not found`);
    }
    let gpaScore;
    if (Hs_lv_cgpaeq) {
      gpaScore = await getPicklistValue(
        "GUS_SF_MY_UCW",
        Hs_lv_cgpaeq,
        "Hs_lv_cgpaeq"
      );
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID initiated`,
      loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      {},
      "Opportunity"
    );
    const opportunity = await this.fetchGusSFDetails(sprogramID);
    if (!opportunity?.length) {
      await this.error(
        event,
        `No opportunity found for ${sprogramID}`,
        loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`No opportunity found for ${sprogramID}`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID completed`,
      loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      opportunity,
      "Opportunity"
    );
    const opportunityUpdateRequest: any = {
      StageName: "Offer",
      AdmissionsStage__c:
        status === "status54"
          ? "Conditional Offer"
          : status === "status53"
          ? "Unconditional Offer"
          : undefined,
      Admissions_Condition__c:
        CN_approval_notes === undefined || CN_approval_notes === null
          ? undefined
          : decode(CN_approval_notes),
      GPAScore__c: gpaScore,
    };
    if (opportunity[0].Stage_Update_Date__c) {
      opportunityUpdateRequest["Stage_Update_Date__c"] = null;
    }
    await this.log(
      event,
      `sync opportunity details initiated`,
      loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      opportunityUpdateRequest,
      {},
      "Opportunity"
    );
    await this.syncInGus(opportunityUpdateRequest, opportunity[0].Id);
    await this.log(
      event,
      `sync opportunity details completed`,
      loggerEnum.Event.SYNC_OPPORTUNITY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE
    );

    const accountUpdateRequest: any = {
      GPA_Foreign_Credential_Evaluation__c: gpaScore,
    };
    await this.updateGusAccount(
      accountUpdateRequest,
      opportunity[0].AccountId,
      files.length
    );

    // Handle document upload if files exist
    if (files.length > 0) {
      const documentPromises = files.map((file) => {
        const documentUpdateRequest = {
          DocumentType__c:
            status === "status54" ? "Conditional Offer Letter" : "Offer letter",
          LetterType__c:
            status === "status54" ? "Conditional Offer Letter" : "Offer letter",
          ApplicationId__c: GUS_appid,
          Name: file?.filename,
          FilePath__c: file?.link,
          Opportunity__c: opportunity[0].Id,
          FullUrl__c: file?.link,
          OriginalValue__c: file?.filename,
          S3FileName__c: file?.link,
          BucketName__c:
            process.env.stage === "dev" ? "gusstage" : "ucwcadocuments",
          DocumentSource__c: "UCW",
          Status__c: "Accepted",
        };
        this.log(
          event,
          `Syncing document for file: ${file?.filename}`,
          loggerEnum.Event.SYNC_DOCUMENT_REQUEST,
          loggerEnum.Component.GUS_SALESFORCE,
          documentUpdateRequest,
          "OpportunityFile__c"
        );

        // Sync document update
        return this.syncGusDocument(documentUpdateRequest);
      });

      // Await all document syncs
      await Promise.all(documentPromises);

      if (this.errors.length > 0) {
        const errorMessage = this.errors
          .map((error, index) => `${index + 1}. ${error.message}`)
          .join("\n");
        this.errors = [];
        throw new Error(`Error occurred:\n${errorMessage}`);
      }

      await this.log(
        event,
        `sync document details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        "OpportunityFile__c"
      );
    }

    return {
      status: true,
      message: `sync opportunity details completed`,
    };
  }
  async syncGusDocument(documentUpdateRequest) {
    try {
      await postData(
        `gus/opportunityfile`,
        documentUpdateRequest,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      console.log("error", error);
      // Check if this is the specific ignorable error for UCW_OFFERED
      if (UCWErrorHandler.isIgnorableError(error, "UCW_OFFERED")) {
        console.log("ignorable error", error);
        await this.log(
          documentUpdateRequest,
          UCWErrorHandler.getIgnoredErrorLogMessage(error, "UCW_OFFERED"),
          loggerEnum.Event.SYNC_DOCUMENT_FAILED,
          loggerEnum.Component.GUS_SALESFORCE
        );
        return; // Don't add to errors array, just log and continue
      }

      await this.error(
        documentUpdateRequest,
        error,
        loggerEnum.Event.SYNC_DOCUMENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(new Error(`Error syncing document details: ${error}`));
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
