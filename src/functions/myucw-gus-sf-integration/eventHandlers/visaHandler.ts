import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
export class VisaHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private errors: Error[] = [];
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        sid,
        error,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInGus(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(
        new Error(`Error syncing opportunity details: ${error}`)
      );
    }
  }
  async handleMessage(event: any): Promise<any> {
    this.usecase = event?.scenario;
    this.correlationId = event?.eventId;
    this.brand = "UCW";
    const {
      files = [],
      Res_expiry_date,
      GUS_appid,
      reasonName,
      sprogramID,
    } = event?.["payload"] || {};

    this.applicationFormId = GUS_appid || sprogramID;
    await this.log(
      event,
      `visa stage handler initiated`,
      loggerEnum.Event.VISA_INITIATED
    );
    if (!sprogramID) {
      await this.error(
        event,
        "sprogramID not found",
        loggerEnum.Event.SPROGRAMID_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`sprogramID not found`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID initiated`,
      loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      {},
      "Opportunity"
    );
    const opportunity = await this.fetchGusSFDetails(sprogramID);
    if (!opportunity?.length) {
      await this.error(
        event,
        `No opportunity found for ${sprogramID}`,
        loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`No opportunity found for ${sprogramID}`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID completed`,
      loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      opportunity,
      "Opportunity"
    );

    const opportunityUpdateRequest: any = {
      Visa_Status__c: "Approved",
      VisaExpiryDate__c: Res_expiry_date || undefined,
      VisaComments__c: reasonName || undefined,
    };
    if (opportunity[0].DateMDA__c) {
      opportunityUpdateRequest["StageName"] = "Visa";
    }
    if (opportunity[0].Stage_Update_Date__c) {
      opportunityUpdateRequest["Stage_Update_Date__c"] = null;
    }
    await this.syncInGus(opportunityUpdateRequest, opportunity[0].Id);

    await this.log(
      event,
      `sync opportunity details completed`,
      files.length > 0
        ? loggerEnum.Event.SYNC_OPPORTUNITY_COMPLETED
        : loggerEnum.Event.OPERATION_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE
    );

    if (files.length > 0) {
      const documentPromises = files.map((file) => {
        const documentUpdateRequest = {
          DocumentType__c: "Visa",
          ApplicationId__c: GUS_appid,
          Name: file?.filename,
          FilePath__c: file?.link,
          Opportunity__c: opportunity[0].Id,
          FullUrl__c: file?.link,
          OriginalValue__c: file?.filename,
          S3FileName__c: file?.link,
          BucketName__c:
            process.env.stage === "dev" ? "gusstage" : "ucwcadocuments",
          DocumentSource__c: "UCW",
          Status__c: "Accepted",
        };
        this.log(
          event,
          `Syncing document for file: ${file?.filename}`,
          loggerEnum.Event.SYNC_DOCUMENT_REQUEST,
          loggerEnum.Component.GUS_SALESFORCE,
          documentUpdateRequest,
          "OpportunityFile__c"
        );
        // Sync document update
        return this.syncGusDocument(documentUpdateRequest);
      });

      // Await all document syncs
      await Promise.all(documentPromises);
      await this.log(
        event,
        `sync document details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        "OpportunityFile__c"
      );

      if (this.errors.length > 0) {
        const errorMessage = this.errors
          .map((error, index) => `${index + 1}. ${error.message}`)
          .join("\n");
        this.errors = [];
        throw new Error(`Error occurred:\n${errorMessage}`);
      }
    }

    return {
      status: true,
      message: `sync opportunity files completed`,
    };
  }
  async syncGusDocument(documentUpdateRequest) {
    try {
      await postData(
        `gus/opportunityfile`,
        documentUpdateRequest,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        documentUpdateRequest,
        error,
        loggerEnum.Event.SYNC_DOCUMENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(new Error(`Error syncing document details: ${error}`));
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      "MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER",
      "MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE",
      destination || "",
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      "MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER",
      "MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE",
      destination || "",
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
