import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { getPicklistByField } from "src/common/getPickListValue";
import { LoggerEnum } from "@gus-eip/loggers";
import { UCWErrorHandler } from "../utils/errorHandler";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
export class OpportunityClosureHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(sid, error, loggerEnum.Event.FETCH_OPPORTUNITY_FAILED, loggerEnum.Component.GUS_SALESFORCE);
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInGus(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      // Check if this is the specific ignorable error for UCW_WITHDRAW_OFFER or UCW_REJECT_APPLICATION_VS
      if (UCWErrorHandler.isIgnorableError(error, this.usecase)) {
        await this.log(
          event,
          UCWErrorHandler.getIgnoredErrorLogMessage(error, this.usecase),
          loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
          loggerEnum.Component.GUS_SALESFORCE
        );
        return null; // Return null to indicate the operation was skipped but not failed
      }

      await this.error(event, error, loggerEnum.Event.SYNC_OPPORTUNITY_FAILED, loggerEnum.Component.GUS_SALESFORCE);
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async getClosureReasonByScenario(
    scenario: string,
    lossReason: string,
    lossOtherReason: string,
    rejectReason: string
  ): Promise<any> {
    if (scenario === "UCW_REJECT_APPLICATION_VS") return "Visa";
    if (scenario === "UCW_REJECT_APPLICATION") {
      const documentMapping = await getPicklistByField(
        "GUS_SF_MY_UCW",
        "Adm_reason_decline"
      );
      return documentMapping?.[rejectReason] || null;
    }
    if (lossReason === "option11443__c") return lossOtherReason;

    const documentMapping = await getPicklistByField(
      "GUS_SF_MY_UCW",
      "App_withdraw_reason"
    );
    return documentMapping?.[lossReason] || null;
  }

  async handleMessage(event: any): Promise<any> {
    this.usecase = event?.scenario;
    this.correlationId = event?.eventId;
    this.brand = "UCW";
    const {
      sprogramID,
      App_withdraw_reason,
      App_withdraw_reason_other,
      Adm_reason_decline,
      GUS_appid,
    } = event?.["payload"] || {};
    this.applicationFormId = GUS_appid || sprogramID;
    await this.log(
      event,
      `opportunity closure stage handler initiated`,
      loggerEnum.Event.DOCUMENT_CLOSURE_INITIATED
    );
    if (!sprogramID) {
      await this.error(
        event,
        "sprogramID not found",
        loggerEnum.Event.SPROGRAMID_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`sprogramID not found`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID initiated`,
      loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      {},
      "Opportunity"
    );
    const opportunity = await this.fetchGusSFDetails(sprogramID);
    if (!opportunity?.length) {
      await this.error(
        event,
        `No opportunity found for ${sprogramID}`,
        loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`No opportunity found for ${sprogramID}`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID completed`,
      loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      opportunity,
      "Opportunity"
    );
    const lossReason = await this.getClosureReasonByScenario(
      this.usecase,
      App_withdraw_reason,
      App_withdraw_reason_other,
      Adm_reason_decline
    );
    if (
      !lossReason &&
      this.usecase !== "UCW_WITHDRAW_OFFER" &&
      this.usecase !== "UCW_WITHDRAW_APPLICATION"
    ) {
      await this.error(
        event,
        "Loss reason not found",
        loggerEnum.Event.LOSS_REASON_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`Loss reason not found`);
    }
    const opportunityUpdateRequest: any = {
      StageName: "Closed Lost",
      Loss_Reason__c: lossReason
        ? lossReason
        : this.usecase === "UCW_WITHDRAW_OFFER" ||
          this.usecase === "UCW_WITHDRAW_APPLICATION"
          ? "Withdrawn"
          : null,
    };
    if (opportunity[0].Stage_Update_Date__c) {
      opportunityUpdateRequest["Stage_Update_Date__c"] = null;
    }
    await this.log(
      event,
      `sync opportunity details initiated`,
      loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      opportunityUpdateRequest,
      {},
      "Opportunity"
    );

    await this.syncInGus(opportunityUpdateRequest, opportunity[0].Id);
    await this.log(
      event,
      `sync opportunity details completed`,
      loggerEnum.Event.OPERATION_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE
    );
    return {
      status: true,
      message: `sync opportunity details completed`
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
