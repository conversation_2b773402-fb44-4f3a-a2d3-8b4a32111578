import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { getPicklistByField } from "src/common/getPickListValue";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
export class ApplicationDifferedHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      await this.log(
        sid,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        sid,
        {},
        "Opportunity"
      );
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(sid, error, loggerEnum.Event.FETCH_OPPORTUNITY_FAILED, loggerEnum.Component.GUS_SALESFORCE);
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInGus(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(event, error, loggerEnum.Event.SYNC_OPPORTUNITY_FAILED, loggerEnum.Component.GUS_SALESFORCE);
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    this.usecase = event?.scenario;
    this.correlationId = event?.eventId;
    this.brand = "UCW";
    const { GUS_appid, IntakeID, sprogramID } = event?.["payload"] || {};
    this.applicationFormId = GUS_appid || sprogramID;
    await this.log(
      event,
      `application differed handler initiated`,
      loggerEnum.Event.APPLICATION_DIFFERED_INITIATED
    );
    if (!sprogramID || !IntakeID) {
      await this.error(
        event,
        "sprogramID or IntakeID not found",
        loggerEnum.Event.SPROGRAMID_OR_INTAKE_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`sprogramID or IntakeID not found`);
    }
    await this.log(
      event,
      `term mapping initiated`,
      loggerEnum.Event.TERM_MAPPING_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      IntakeID
    );
    console.log("IntakeID", IntakeID);
    const primaryTermIDMappings = await getPicklistByField(
      "GUS_SF_MY_UCW",
      "primaryTermID"
    );
    let mappedPrimaryTermID;
    for (const [key, value] of Object.entries(primaryTermIDMappings)) {
      if (value === IntakeID) {
        mappedPrimaryTermID = key;
        break;
      }
    }
    console.log("mappedPrimaryTermID", mappedPrimaryTermID);
    if (mappedPrimaryTermID) {
      await this.log(
        event,
        `term mapping completed`,
        loggerEnum.Event.TERM_MAPPING_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        mappedPrimaryTermID
      );
      const opportunity = await this.fetchGusSFDetails(sprogramID);
      if (!opportunity?.length) {
        await this.error(
          event,
          `No opportunity found for ${sprogramID}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${sprogramID}`);
      }
      await this.log(
        event,
        `fetch opportunity by sprogramID completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID,
        opportunity,
        "Opportunity"
      );
      const opportunityUpdateRequest: any = {
        OverallStartDate__c: mappedPrimaryTermID,
      };
      if (opportunity[0].Stage_Update_Date__c) {
        opportunityUpdateRequest["Stage_Update_Date__c"] = null;
      }
      await this.log(
        event,
        `sync opportunity details initiated`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityUpdateRequest,
        {},
        "Opportunity"
      );
      await this.syncInGus(opportunityUpdateRequest, opportunity[0].Id);
      await this.log(
        event,
        `sync opportunity details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      return {
        status: true,
        message: `sync opportunity details completed`
      }
    } else {
      await this.error(
        event,
        `PrimaryTermID mappings not found for ${IntakeID}`,
        loggerEnum.Event.PRIMARYTERMID_MAPPINGS_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`primaryTermID mappings not found for ${IntakeID}`);
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message ? errorMessage.message : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
