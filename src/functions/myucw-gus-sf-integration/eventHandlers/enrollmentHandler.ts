import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
export class EnrolledH<PERSON>ler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(sid, error, loggerEnum.Event.FETCH_OPPORTUNITY_FAILED, loggerEnum.Component.GUS_SALESFORCE);
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInGus(event: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/event/GUS_SF_OppStage_Update__e`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    this.usecase = event?.scenario;
    this.correlationId = event?.eventId;
    this.brand = "UCW";
    const { GUS_appid, sprogramID } = event?.["payload"] || {};

    this.applicationFormId = GUS_appid || sprogramID;
    await this.log(
      event,
      `enrollment stage handler initiated`,
      loggerEnum.Event.ENROLLMENT_INITIATED
    );
    if (!sprogramID) {
      await this.error(
        event,
        "sprogramID not found",
        loggerEnum.Event.SPROGRAMID_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`sprogramID not found`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID initiated`,
      loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      {},
      "Opportunity"
    );
    const opportunity = await this.fetchGusSFDetails(sprogramID);
    if (!opportunity?.length) {
      await this.error(
        event,
        `No opportunity found for ${sprogramID}`,
        loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`No opportunity found for ${sprogramID}`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID completed`,
      loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      opportunity,
      "Opportunity"
    );
    const platformEventUpdateRequest: any = {
      Opportunity_Id__c: opportunity[0].Id,
      Admission_Status__c: "Enrolled",
      Opp_Stage__c: opportunity[0].StageName,
    };

    await this.log(
      event,
      `post platform event initiated`,
      loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      platformEventUpdateRequest,
      {},
      "Opportunity"
    );

    // Sync opportunity updates
    const response = await this.syncInGus(platformEventUpdateRequest);
    await this.log(
      event,
      `publish event completed`,
      loggerEnum.Event.OPERATION_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      platformEventUpdateRequest,
      response
    );

    return {
      status: true,
      message: `sync opportunity details completed`,
    };
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
