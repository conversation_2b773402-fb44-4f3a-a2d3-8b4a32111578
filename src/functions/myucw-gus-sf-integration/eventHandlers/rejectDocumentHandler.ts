import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { getPicklistByField } from "src/common/getPickListValue";
import { decode } from "he";
import { LoggerEnum } from "@gus-eip/loggers";
import { UCWErrorHandler } from "../utils/errorHandler";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
export class RejectDocumentHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async syncOpportunity(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        sid,
        error,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInGus(event: any): Promise<any> {
    try {
      const publishResponse = await postData(
        `gus/publishrejecteddocumentevent`,
        event,
        this.correlationId
      );
      return publishResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing in GUS: ${error}`);
    }
  }
  async syncInGusOpportunity(event: any, updateById: string): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async fetchDocumentsByType(
    opportunityId: string,
    documentType: string
  ): Promise<any> {
    await this.log(
      { opportunityId, documentType },
      `fetch document by type initiated`,
      loggerEnum.Event.FETCH_DOCUMENT_BY_TYPE_INITIATED
    );
    let institutionOrder;
    if (
      documentType.startsWith("Degree transcripts") ||
      documentType.startsWith("Degree certificate") ||
      documentType.startsWith("High School Transcripts and Certificate")
    ) {
      [documentType, institutionOrder] = documentType.split("_");
    }
    const encodedType = encodeURIComponent(documentType);
    try {
      let files = await getData(
        `gus/getopportunityfilesbydocumenttype/${opportunityId}/${encodedType}`,
        this.correlationId,
        process.env.UCW_KEY
      );
      if (
        (documentType.startsWith("Degree transcripts") ||
          documentType.startsWith("Degree certificate") ||
          documentType.startsWith("High School Transcripts and Certificate")) &&
        institutionOrder
      ) {
        await this.log(
          opportunityId,
          "get education initiated",
          loggerEnum.Event.GET_EDU_HISTORY_INITIATED,
          loggerEnum.Component.MY_UCW
        );
        const educationHistory = await getData(
          `gus/geteducationhistorybyoppid/${opportunityId}`,
          this.correlationId,
          process.env.UCW_KEY
        );
        await this.log(
          opportunityId,
          "get education history completed",
          loggerEnum.Event.FETCH_EDU_HISTORY_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          {},
          educationHistory
        );
        const filteredEduHistory = educationHistory.find(
          (item) => item.Name === institutionOrder
        );
        if (filteredEduHistory) {
          files = {
            response: files.response.filter(
              (item) =>
                item.Related_Education_History__c === filteredEduHistory.Id &&
                item.DocumentType__c === documentType
            ),
          };
          return files;
        }
        return { response: [] };
      }
      return files;
    } catch (error) {
      await this.error(
        { opportunityId, encodedType },
        error,
        loggerEnum.Event.FETCH_DOCUMENTS_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      console.log("error", JSON.stringify(error));
      throw new Error(`Error fetching documents: ${error}`);
    }
  }
  async getGUSDocumentType(requirement_name: string): Promise<any> {
    const documentMapping = await getPicklistByField(
      "GUS_SF_MY_UCW",
      "requirement_name"
    );
    console.log("documentMapping", documentMapping, requirement_name);

    if (documentMapping) {
      return Object.keys(documentMapping).find(
        (key) => documentMapping[key] === requirement_name
      );
    } else {
      console.error("documentMapping or Picklist is undefined or null.");
      return null; // or throw an error if this should not happen
    }
  }

  async handleMessage(event: any): Promise<any> {
    this.usecase = event?.scenario;
    this.correlationId = event?.eventId;
    this.brand = "UCW";
    const { reasonName, sprogramID, GUS_appid, documentType, note } =
      event?.["payload"] || {};
    this.applicationFormId = GUS_appid || sprogramID;
    await this.log(
      event,
      `reject document handler initiated`,
      loggerEnum.Event.REJECT_DOCUMENT_INITIATED
    );
    if (!sprogramID) {
      await this.error(
        event,
        "sprogramID not found",
        loggerEnum.Event.SPROGRAMID_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`sprogramID not found`);
    }
    const gusDocumentType = await this.getGUSDocumentType(documentType);
    if (!gusDocumentType) {
      await this.error(
        event,
        "gusDocumentType not found for " + documentType,
        loggerEnum.Event.GUS_DOCUMENTTYPE_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`gusDocumentType not found for ${documentType}`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID initiated`,
      loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      {},
      "Opportunity"
    );
    const opportunity = await this.fetchGusSFDetails(sprogramID);
    if (!opportunity?.length) {
      await this.error(
        event,
        `No opportunity found for ${sprogramID}`,
        loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`No opportunity found for ${sprogramID}`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID completed`,
      loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      opportunity,
      "Opportunity"
    );
    // if (status === "status13") {
    //   const opportunityUpdateRequest: any = {
    //     StageName: "Closed Lost",
    //     Loss_Reason__c: "Visa",
    //   };
    //   await this.syncInGusOpportunity(
    //     opportunityUpdateRequest,
    //     opportunity[0].Id
    //   );
    // }

    if (opportunity[0].Stage_Update_Date__c) {
      await this.log(
        event,
        `sync opportunity details initiated`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        { Stage_Update_Date__c: null },
        {},
        "Opportunity"
      );
      await this.syncOpportunity(
        { Stage_Update_Date__c: null },
        opportunity[0].Id
      );
      await this.log(
        event,
        `sync opportunity details completed`,
        loggerEnum.Event.SYNC_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );
    }
    const documents = await this.fetchDocumentsByType(
      opportunity[0].Id,
      gusDocumentType
    );
    if (!documents?.response?.length) {
      const error = "No Accepted documents found for " + documentType;
      // Check if this is the specific ignorable error for UCW_REJECT_DOCUMENT
      if (UCWErrorHandler.isIgnorableError(error, this.usecase)) {
        await this.log(
          event,
          UCWErrorHandler.getIgnoredErrorLogMessage(error, this.usecase),
          loggerEnum.Event.DOCUMENTS_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        return {
          status: true,
          message: "Document rejection ignored - no accepted documents found"
        };
      }

      await this.error(
        event,
        error,
        loggerEnum.Event.DOCUMENTS_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`No document type found for ${documentType}`);
    }
    await this.log(
      event,
      `fetch opportunity file by document type completed`,
      loggerEnum.Event.FETCH_OPPORTUNITY_FILE_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      documents?.response,
      "OpportunityFile__c"
    );
    for (const document of documents.response) {
      const publishEventPayload = {
        Brand__c: this.brand,
        Document_Status__c: "Rejected",
        Opportunity_File_Id__c: document.Id,
        Opportunity_Id__c: opportunity[0].Id,
        Rejection_Comments__c: `${
          reasonName === undefined || reasonName === null
            ? undefined
            : decode(reasonName)
        } - ${note === undefined || note === null ? undefined : decode(note)}`,
      };
      const publishResponse = await this.syncInGus(publishEventPayload);
      await this.log(
        event,
        `publish rejected document event completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        publishEventPayload,
        publishResponse
      );
    }

    return {
      status: true,
      message: `sync opportunity files completed`,
    };
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
