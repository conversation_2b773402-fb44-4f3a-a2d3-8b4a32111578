import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
import { decode } from "he";
export class DocumentHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(sid, error, loggerEnum.Event.FETCH_OPPORTUNITY_FAILED, loggerEnum.Component.GUS_SALESFORCE);
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInGus(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(event, error, loggerEnum.Event.SYNC_OPPORTUNITY_FAILED, loggerEnum.Component.GUS_SALESFORCE);
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }

  async handleMessage(event: any): Promise<any> {
    this.usecase = event?.scenario;
    this.correlationId = event?.eventId;
    this.brand = "UCW";
    const {
      files = [],
      sprogramID,
      GUS_appid,
      CN_approval_notes,
    } = event?.["payload"] || {};
    this.applicationFormId = GUS_appid || sprogramID;
    await this.log(
      event,
      `document issued stage handler initiated`,
      loggerEnum.Event.DOCUMENT_ISSUED_INITIATED
    );
    if (!sprogramID) {
      await this.error(
        event,
        "sprogramID not found",
        loggerEnum.Event.SPROGRAMID_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`sprogramID not found`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID initiated`,
      loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      {},
      "Opportunity"
    );
    const opportunity = await this.fetchGusSFDetails(sprogramID);
    if (!opportunity?.length) {
      await this.error(
        event,
        `No opportunity found for ${sprogramID}`,
        loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`No opportunity found for ${sprogramID}`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID completed`,
      loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      opportunity,
      "Opportunity"
    );
    if (!["Visa", "Closed Won"].includes(opportunity[0]?.StageName)) {
      const opportunityUpdateRequest: any = {
        StageName: "Payment",
        Admissions_Condition__c: CN_approval_notes === undefined || CN_approval_notes === null
          ? undefined
          : decode(CN_approval_notes),
        DateMDA__c: new Date().toISOString(),
      };
      if (opportunity[0].Stage_Update_Date__c) {
        opportunityUpdateRequest["Stage_Update_Date__c"] = null;
      }
      await this.log(
        event,
        `sync opportunity details initiated`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityUpdateRequest,
        {},
        "Opportunity"
      );

      await this.syncInGus(opportunityUpdateRequest, opportunity[0].Id);
      await this.log(
        event,
        `sync opportunity details completed`,
        files.length > 0 ? loggerEnum.Event.SYNC_OPPORTUNITY_COMPLETED : loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );
    }
    if (files.length > 0) {
      const documentPromises = files.map((file) => {
        const documentUpdateRequest = {
          DocumentType__c: "Letter of Acceptance",
          ApplicationId__c: GUS_appid,
          Name: file?.filename,
          FilePath__c: file?.link,
          Opportunity__c: opportunity[0].Id,
          FullUrl__c: file?.link,
          OriginalValue__c: file?.filename,
          S3FileName__c: file?.link,
          BucketName__c: process.env.stage === 'dev' ? "gusstage" : 'ucwcadocuments',
          DocumentSource__c: "UCW",
          Status__c: "Accepted",
        };
        this.log(
          event,
          `Syncing document for file: ${file?.filename}`,
          loggerEnum.Event.SYNC_DOCUMENT_REQUEST,
          loggerEnum.Component.GUS_SALESFORCE,
          documentUpdateRequest,
          "OpportunityFile__c"
        );
        // Sync document update
        return this.syncGusDocument(documentUpdateRequest);
      });

      // Await all document syncs
      await Promise.all(documentPromises);
      await this.log(
        event,
        `sync document details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        "OpportunityFile__c"
      );
    }
    return {
      status: true,
      message: `sync opportunity details completed`
    }
  }
  async syncGusDocument(documentUpdateRequest) {
    try {
      await postData(
        `gus/opportunityfile`,
        documentUpdateRequest,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        documentUpdateRequest,
        error,
        loggerEnum.Event.SYNC_DOCUMENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      // throw new Error(`Error syncing document details: ${error}`);
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
