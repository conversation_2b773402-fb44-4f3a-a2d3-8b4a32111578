import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { decode } from "he";
import { LoggerEnum } from "@gus-eip/loggers";
import { getPicklistByField } from "src/common/getPickListValue";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
export class FollowUpTaskHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        sid,
        error,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInGus(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UCW_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    this.usecase = event?.scenario;
    this.correlationId = event?.eventId;
    this.brand = "UCW";
    const { GUS_appid, Incmpl_notes, sprogramID, Adm_mba_review } =
      event?.["payload"] || {};
    this.applicationFormId = GUS_appid || sprogramID;
    await this.log(
      event,
      `follow up task handler initiated`,
      loggerEnum.Event.FOLLOW_UP_TASK_INITIATED
    );
    if (!(sprogramID && (Adm_mba_review || Incmpl_notes))) {
      await this.error(
        event,
        "Required field not found",
        loggerEnum.Event.REQUIRED_FIELD_NOT_FOUND,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`Required field not found`);
    }
    let condition;
    if (Adm_mba_review) {
      const optionMapping = await getPicklistByField(
        "GUS_SF_MY_UCW",
        "Adm_mba_review"
      );
      condition = optionMapping?.[Adm_mba_review] || null;
      if (condition === undefined || condition === null) {
        await this.error(
          event,
          "mba review option not found",
          loggerEnum.Event.REQUIRED_FIELD_NOT_FOUND,
          loggerEnum.Component.MY_UCW
        );
        throw new Error(`mba review option not found`);
      }
    } else if (Incmpl_notes) {
      condition = Incmpl_notes;
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID initiated`,
      loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      {},
      "Opportunity"
    );
    const opportunity = await this.fetchGusSFDetails(sprogramID);
    if (!opportunity?.length) {
      await this.error(
        event,
        `No opportunity found for ${sprogramID}`,
        loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`No opportunity found for ${sprogramID}`);
    }
    await this.log(
      event,
      `fetch opportunity by sprogramID completed`,
      loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE,
      sprogramID,
      opportunity,
      "Opportunity"
    );
    const decodedNotes =
      condition === undefined || condition === null
        ? undefined
        : decode(condition);
    const opportunityUpdateRequest: any = {
      Admissions_Condition__c: decodedNotes,
      Admission_Condition__c: decodedNotes,
      AdmissionsStage__c: "Further clarification required",
    };
    if (opportunity[0].Stage_Update_Date__c) {
      opportunityUpdateRequest["Stage_Update_Date__c"] = null;
    }
    await this.log(
      event,
      `sync opportunity details initiated`,
      loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
      loggerEnum.Component.GUS_SALESFORCE,
      opportunityUpdateRequest,
      {},
      "Opportunity"
    );
    await this.syncInGus(opportunityUpdateRequest, opportunity[0].Id);
    await this.log(
      event,
      `sync opportunity details completed`,
      loggerEnum.Event.OPERATION_COMPLETED,
      loggerEnum.Component.GUS_SALESFORCE
    );

    return {
      status: true,
      message: `sync opportunity details completed`,
    };
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
