import { OfferHandler } from "./eventHandlers/offerHandler";
import { VisaHandler } from "./eventHandlers/visaHandler";
import { DocumentHandler } from "./eventHandlers/documentIssuedHandler";
import { EnrolledHandler } from "./eventHandlers/enrollmentHandler";
import { OpportunityClosureHandler } from "./eventHandlers/opportunityClosureHandler";
import { FollowUpTaskHandler } from "./eventHandlers/followUpTaskHandler";
import { RejectDocumentHandler } from "./eventHandlers/rejectDocumentHandler";
import { ApplicationDifferedHandler } from "./eventHandlers/applicationDifferedHandler";
export class EventHandlerFactory {
  static getHandler(scenario): GusEventsHandler {
    switch (scenario) {
      case "UCW_OFFERED":
        return new OfferHandler();
      case "UCW_DOCUMENT_ISSUED":
        return new DocumentHandler();
      case "UCW_VISA_APPROVED":
        return new VisaHandler();
      case "UCW_ENROLLED":
        return new EnrolledHandler();
      case "UCW_FOLLOWUP_TASK":
      case "UCW_FOLLOWUP_MBA_PENDING":
        return new FollowUpTaskHandler();
      case "UCW_REJECT_APPLICATION":
      case "UCW_WITHDRAW_OFFER":
      case "UCW_WITHDRAW_APPLICATION":
      case "UCW_REJECT_APPLICATION_VS":
        return new OpportunityClosureHandler();
      case "UCW_REJECT_DOCUMENT":
        return new RejectDocumentHandler();
      case "UCW_APPLICATION_DEFERRED":
        return new ApplicationDifferedHandler();
      default:
        console.log("No handler found for event type: " + scenario);
        break;
    }
  }
}
