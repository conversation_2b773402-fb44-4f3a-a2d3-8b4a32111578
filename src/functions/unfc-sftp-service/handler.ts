import { S3Service } from "../../common/s3Service";
import { SftpConnector } from "../../connectors/sftpConnector";

interface SftpPayload {
  s3FilePath: string;
  ellusionFilePath: string;
  method: "GET_DOC" | "POST_DOC";
}

const s3Service = new S3Service();

export const unfcSftpHandler = async (payload: SftpPayload): Promise<{ message: string }> => {
  console.log("Starting SFTP handler with payload:", { 
    s3FilePath: payload.s3FilePath, 
    ellusionFilePath: payload.ellusionFilePath, 
    method: payload.method 
  });

  const { s3FilePath, ellusionFilePath, method } = payload;
  const sftpConnector = new SftpConnector({
    host: process.env.UNFC_SFTP_HOST || "unfcdata.blob.core.windows.net",
    port: parseInt(process.env.UNFC_SFTP_PORT || "22"),
    username: process.env.UNFC_SFTP_USERNAME || "unfcdata.unfc-teamia.unfcteamia",
    password: process.env.UNFC_SFTP_PASSWORD || "8jB950Gj6jnxgL9gWZCIAJAd31R1vxIN",
  });

  try {
    console.log("Attempting to connect to SFTP server...");
    await sftpConnector.connect();
    console.log("Successfully connected to SFTP server");

    if (method === "GET_DOC") {
      console.log("Processing GET_DOC request - listing files from SFTP and getting most recent file");
      
      // List files in the directory
      const files = await sftpConnector.listFiles(ellusionFilePath);
      console.log("Files found in directory:", files);

      if (!files || files.length === 0) {
        throw new Error(`No files found in directory: ${ellusionFilePath}`);
      }

      // Get the most recent file's content
      const mostRecentFile = files.reduce((latest, current) => {
        return current.modifyTime > latest.modifyTime ? current : latest;
      });
      
      const fileContent = await sftpConnector.getFile(`${ellusionFilePath}/${mostRecentFile.name}`);
      console.log("Successfully downloaded most recent file from SFTP:", {
        fileName: mostRecentFile.name,
        fileSize: fileContent.length,
        lastModified: mostRecentFile.modifyTime
      });

      console.log("Uploading file to S3 bucket:", process.env.REVIEW_CENTER_BUCKET_NAME);
      await s3Service.uploadFile(
        process.env.REVIEW_CENTER_BUCKET_NAME,
        s3FilePath,
        fileContent,
        process.env.S3_EIP_CROSS_ACCOUNT_ACCESS_ROLE_ARN
      );
      console.log("Successfully uploaded file to S3");
      
      return {
        message: `Successfully downloaded most recent file (${mostRecentFile.name}) from SFTP and uploaded to S3 at ${s3FilePath}`,
      };
    } else {
      console.log("Processing POST_DOC request - downloading from S3 and uploading to SFTP");
      // Get file from S3 and upload to SFTP
      console.log("Downloading file from S3 bucket:", process.env.REVIEW_CENTER_BUCKET_NAME);
      const fileContent = await s3Service.getBase64File(
        process.env.REVIEW_CENTER_BUCKET_NAME,
        s3FilePath,
        process.env.S3_EIP_CROSS_ACCOUNT_ACCESS_ROLE_ARN
      );
      console.log("Successfully downloaded file from S3");

      console.log("Uploading file to SFTP path:", ellusionFilePath);
      await sftpConnector.uploadFile(ellusionFilePath, Buffer.from(fileContent, 'base64'));
      console.log("Successfully uploaded file to SFTP");
      
      return {
        message: `Successfully downloaded file from S3 and uploaded to SFTP at ${ellusionFilePath}`,
      };
    }
  } catch (error) {
    console.error("Error in SFTP handler", { 
      error, 
      payload,
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  } finally {
    console.log("Disconnecting from SFTP server...");
    await sftpConnector.disconnect();
    console.log("Successfully disconnected from SFTP server");
  }
}; 