import { unfcSftp<PERSON><PERSON><PERSON> } from "./handler";

export const unfcSftpServiceHandler = async (
  event: any
): Promise<any> => {
  try {
    console.log("UNFC SFTP Service started", { event });
    
    if (!event.body) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: "Request body is required" }),
      };
    }

    const payload = JSON.parse(event.body);
    const { s3FilePath, ellusionFilePath, method } = payload;

    if (!s3FilePath || !ellusionFilePath || !method) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: "s3FilePath, ellusionFilePath, and method are required",
        }),
      };
    }

    if (!["GET_DOC", "POST_DOC"].includes(method)) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: "method must be either GET_DOC or POST_DOC",
        }),
      };
    }

    const result = await unfcSftp<PERSON>andler(payload);

    return {
      statusCode: 200,
      body: JSON.stringify(result),
    };
  } catch (error) {
    console.error("Error in UNFC SFTP Service", { error });
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      }),
    };
  }
}; 