import { handlerPath } from "@libs/handler-resolver";

export const unfcSftpService = {
  handler: `${handlerPath(__dirname)}/unfcSftpService.unfcSftpServiceHandler`,
  name: "unfc-sftp-service-${self:provider.stage}",
  vpc: {
    securityGroupIds: ["sg-083ecddff7d511d6b"],
    subnetIds: [
      "subnet-0017a5a0c7217a524",
      "subnet-0a13cacdf1be3c00c",
    ],
  },
  timeout: 180,
  memorySize: 256,
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  }
}; 