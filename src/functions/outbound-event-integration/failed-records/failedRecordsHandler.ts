/**
 * @fileoverview Failed Record Retry Lambda Handler
 * 
 * Production-ready Lambda handler for processing failed records across multiple brands.
 * Uses dynamic brand discovery and follows UE CampusNet/MyUCW patterns for retry logic.
 * 
 * Features:
 * - Dynamic brand discovery from database via getAllBrands()
 * - Multi-brand concurrent processing
 * - Comprehensive error handling and monitoring
 * - Production-ready logging and metrics
 * - Two-tier storage pattern support
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { FailedRecordsRetryService } from './services/failedRecordsRetryService';

/**
 * Main retry handler for failed records across all brands.
 * 
 * This function orchestrates the retry process by:
 * 1. Dynamically discovering brands from database
 * 2. Processing each brand's failed records concurrently
 * 3. Using brand-specific retry strategies (Strategy pattern)
 * 4. Providing comprehensive result reporting
 * 
 * @returns Promise<string> - Summary of retry operations
 */
export const retryFailedRecords = async (): Promise<string> => {
  console.log("🚀 Starting failed records retry process...");
  
  // Get table name from environment variable
  const tableName = process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME || 'gus-eip-failed-records';
  const retryService = new FailedRecordsRetryService(tableName);
  
  try {
    const results = await retryService.retryFailedRecordsForAllBrands();
    
    // Generate summary report
    const summary = {
      totalBrands: results.length,
      successful: results.filter(r => r.status === 'success').length,
      errors: results.filter(r => r.status === 'error').length,
      skipped: results.filter(r => r.status === 'skipped').length,
      totalRecordsProcessed: results.reduce((sum, r) => sum + r.recordsProcessed, 0)
    };
    
    const summaryMessage = `Processed ${summary.totalBrands} brands: ${summary.successful} successful, ${summary.errors} errors, ${summary.skipped} skipped, ${summary.totalRecordsProcessed} total records processed`;
    
    console.log("✅ Failed records retry process completed:", summaryMessage);
    return summaryMessage;
  } catch (error) {
    console.error("❌ Failed records retry process failed:", error);
    throw error;
  }
};
