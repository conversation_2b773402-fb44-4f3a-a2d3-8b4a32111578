/**
 * @fileoverview Failed Records Retry Service
 *
 * Production-ready service for retrying failed event records across multiple brands.
 * Follows the exact patterns used in UE CampusNet and MyUCW integrations with
 * proper OOP design principles and industry-standard error handling.
 *
 * Key features:
 * - Dynamic brand discovery from database
 * - Two-tier storage pattern: partition records + detail records
 * - Brand-specific retry logic with proper abstraction
 * - Comprehensive error handling and monitoring
 * - Production-ready logging and metrics
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 */

import { DynamoDBService } from "src/common/dynamodbService";
import { SnsService } from "src/common/snsService";
import {
  brandConfigManager,
  IBrandConfig,
} from "../../shared/services/brandConfigManager";

/**
 * Interface for retry operation results
 */
export interface IRetryResult {
  brand: string;
  integrationPrefix: string;
  status: "success" | "error" | "skipped";
  recordsProcessed: number;
  message: string;
  error?: Error;
}

/**
 * Interface for retry statistics
 */
export interface IRetryStatistics {
  integrationPrefix: string;
  totalFailedRecords: number;
  eligibleForRetry: number;
}

/**
 * Production-ready Failed Records Retry Service
 *
 * This service orchestrates retry operations across all brands with proper
 * error handling, monitoring, and following industry best practices.
 */
export class FailedRecordsRetryService {
  private readonly dbService: DynamoDBService;
  private readonly snsService: SnsService;
  private readonly tableName: string;

  /**
   * Constructor for FailedRecordsRetryService
   *
   * @param tableName - DynamoDB table name for failed records
   */
  constructor(tableName: string) {
    this.dbService = new DynamoDBService();
    this.snsService = new SnsService();
    this.tableName = tableName;
  }

  /**
   * Retries failed records for all brands dynamically discovered from database
   *
   * @returns Promise<IRetryResult[]> Array of retry results per brand
   */
  async retryFailedRecordsForAllBrands(): Promise<IRetryResult[]> {
    console.log("🚀 Starting failed records retry process for all brands...");
    const startTime = Date.now();

    try {
      // Dynamic brand discovery with configs in one operation
      const brandConfigMap = await brandConfigManager.getAllBrandsWithConfigs();
      const brands = Array.from(brandConfigMap.keys());

      if (brands.length === 0) {
        console.log("📭 No brands found in database - nothing to process");
        return [];
      }

      console.log(
        `📋 Found ${brands.length} brands to process:`,
        brands.map((b) => b.toLowerCase())
      );

      const results: IRetryResult[] = [];
      const retryPromises = brands.map((brand) =>
        this.retryForSingleBrand(brand, brandConfigMap.get(brand)!)
      );

      // Process all brands concurrently but handle errors individually
      const settledResults = await Promise.allSettled(retryPromises);

      settledResults.forEach((result, index) => {
        const brand = brands[index];
        if (result.status === "fulfilled") {
          results.push(result.value);
        } else {
          results.push({
            brand,
            integrationPrefix:
              brandConfigMap.get(brand)?.integrationPrefix || "UNKNOWN",
            status: "error",
            recordsProcessed: 0,
            message: `Failed to process brand: ${
              result.reason?.message || "Unknown error"
            }`,
            error: result.reason,
          });
        }
      });

      const duration = Date.now() - startTime;
      console.log(`✅ Completed retry process for all brands in ${duration}ms`);
      this.logRetryResults(results);

      return results;
    } catch (error) {
      console.error("❌ Fatal error in multi-brand retry process:", error);
      throw error;
    }
  }

  /**
   * Retries failed records for a single brand
   *
   * @param brand - Brand code
   * @param brandConfig - Pre-loaded brand configuration
   * @returns Promise<IRetryResult> Retry result for the brand
   */
  private async retryForSingleBrand(
    brand: string,
    brandConfig: IBrandConfig
  ): Promise<IRetryResult> {
    const integrationPrefix = brandConfig.integrationPrefix;

    if (!integrationPrefix) {
      return {
        brand,
        integrationPrefix: "UNKNOWN",
        status: "skipped",
        recordsProcessed: 0,
        message: `No integration prefix configured for brand ${brand}`,
      };
    }

    try {
      console.log(
        `🔄 Processing failed records for brand: ${brand} (${integrationPrefix})`
      );

      const recordsProcessed = await this.retryFailedRecordsForIntegration(
        integrationPrefix,
        brandConfig
      );

      return {
        brand,
        integrationPrefix,
        status: "success",
        recordsProcessed,
        message: `Successfully processed ${recordsProcessed} records`,
      };
    } catch (error) {
      console.error(`❌ Error processing brand ${brand}:`, error);
      return {
        brand,
        integrationPrefix,
        status: "error",
        recordsProcessed: 0,
        message: error.message,
        error: error as Error,
      };
    }
  }

  /**
   * Retries failed records for a specific integration following UE CampusNet/MyUCW pattern
   *
   * @param integrationPrefix - Integration prefix (e.g., "UE_CAMPUSNET_GUS_SF")
   * @param brandConfig - Brand configuration
   * @returns Promise<number> Number of records processed
   */
  private async retryFailedRecordsForIntegration(
    integrationPrefix: string,
    brandConfig: IBrandConfig
  ): Promise<number> {
    // Step 1: Query partition records (PK = integrationPrefix)
    const partitionRecords = await this.getPartitionRecords(integrationPrefix);

    if (partitionRecords.length === 0) {
      console.log(`📭 No partition records found for ${integrationPrefix}`);
      return 0;
    }

    console.log(
      `📋 Found ${partitionRecords.length} partition records for ${integrationPrefix}`
    );

    let totalProcessed = 0;

    // Step 2: Process each eligible partition record
    for (const partitionRecord of partitionRecords) {
      if (this.isEligibleForRetry(partitionRecord)) {
        const processed = await this.processPartitionRecord(
          partitionRecord,
          brandConfig.outboundTopicArn,
          integrationPrefix
        );
        totalProcessed += processed;
      } else {
        console.log(
          `⏭️ Skipping partition record ${partitionRecord.PK}#${partitionRecord.SK} - not eligible for retry (retryCount: ${partitionRecord.retryCount})`
        );
      }
    }

    return totalProcessed;
  }

  /**
   * Gets partition records for an integration prefix
   */
  private async getPartitionRecords(integrationPrefix: string): Promise<any[]> {
    const params = {
      TableName: this.tableName,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": integrationPrefix,
      },
    };

    const response = await this.dbService.queryObjects(params);
    return response.Items || [];
  }

  /**
   * Checks if a partition record is eligible for retry (follows existing database schema)
   */
  private isEligibleForRetry(partitionRecord: any): boolean {
    // Only process records with "Failed" status and retry count <= 3
    return (
      partitionRecord.status === "Failed" &&
      (partitionRecord.retryCount <= 3 || !partitionRecord.retryCount)
    );
  }

  /**
   * Processes a single partition record and its detail records
   */
  private async processPartitionRecord(
    partitionRecord: any,
    outboundTopicArn: string,
    integrationPrefix: string
  ): Promise<number> {
    console.log(
      `🔄 Processing partition record: ${partitionRecord.PK}#${partitionRecord.SK}`
    );

    // Step 1: Get detail records
    const detailRecords = await this.getDetailRecords(partitionRecord);

    if (detailRecords.length === 0) {
      console.log(
        `📭 No detail records found for ${partitionRecord.PK}#${partitionRecord.SK}`
      );
      return 0;
    }

    console.log(`📋 Found ${detailRecords.length} detail records to republish`);

    // Step 2: Republish detail records
    let successCount = 0;
    for (const detailRecord of detailRecords) {
      try {
        await this.republishDetailRecord(
          detailRecord,
          outboundTopicArn,
          integrationPrefix
        );
        successCount++;
      } catch (error) {
        console.error(
          `❌ Failed to republish detail record ${detailRecord.SK}:`,
          error
        );
        // Continue processing other records
      }
    }

    // Step 3: Update partition record retry count
    await this.updatePartitionRetryCount(partitionRecord);

    console.log(
      `✅ Successfully republished ${successCount}/${detailRecords.length} detail records`
    );
    return successCount;
  }

  /**
   * Gets detail records for a partition record
   */
  private async getDetailRecords(partitionRecord: any): Promise<any[]> {
    const params = {
      TableName: this.tableName,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": `${partitionRecord.PK}#${partitionRecord.SK}`,
      },
    };

    const response = await this.dbService.queryObjects(params);
    return response.Items || [];
  }

  /**
   * Republishes a detail record using simple direct approach
   */
  private async republishDetailRecord(
    detailRecord: any,
    outboundTopicArn: string,
    integrationPrefix: string
  ): Promise<void> {
    const eventBody = JSON.parse(detailRecord.body);
    const eventMessage = JSON.parse(eventBody.Message);

    console.log(`📤 Republishing event message for record ${detailRecord.SK}`);

    // Extract application ID based on integration type (simplified approach)
    const applicationId = this.extractApplicationId(
      eventMessage,
      integrationPrefix
    );

    // Mark as failed for retry
    eventMessage.status = "Failed";

    await this.snsService.publishMessages(
      eventMessage,
      applicationId,
      outboundTopicArn
    );
  }

  /**
   * Extracts application ID from event message based on integration type
   */
  private extractApplicationId(
    eventMessage: any,
    integrationPrefix: string
  ): string {
    if (integrationPrefix.includes("UE_CAMPUSNET")) {
      return eventMessage.data?.ExternalId || eventMessage.uuid;
    } else if (integrationPrefix.includes("MYUCW")) {
      return (
        eventMessage.payload?.GUS_appid ||
        eventMessage.payload?.sprogramID ||
        eventMessage.uuid
      );
    } else {
      // Default for outbound events and other integrations
      return (
        eventMessage.payload?.gusApplicationId ||
        eventMessage.correlationId ||
        eventMessage.uuid
      );
    }
  }

  /**
   * Updates the retry count for a partition record
   */
  private async updatePartitionRetryCount(partitionRecord: any): Promise<void> {
    const currentRetryCount = partitionRecord.retryCount || 0;
    const newRetryCount = currentRetryCount + 1;

    await this.dbService.updateObject(
      this.tableName,
      {
        PK: partitionRecord.PK,
        SK: partitionRecord.SK,
      },
      {
        retryCount: newRetryCount,
        updatedAt: new Date().toISOString(),
        lastRetryAt: new Date().toISOString(),
      }
    );

    console.log(
      `📊 Updated retry count for ${partitionRecord.PK}#${partitionRecord.SK}: ${currentRetryCount} → ${newRetryCount}`
    );
  }

  /**
   * Gets retry statistics for a specific integration
   */
  async getRetryStatistics(
    integrationPrefix: string
  ): Promise<IRetryStatistics> {
    const partitionRecords = await this.getPartitionRecords(integrationPrefix);

    const eligibleRecords = partitionRecords.filter((r) =>
      this.isEligibleForRetry(r)
    );

    return {
      integrationPrefix,
      totalFailedRecords: partitionRecords.length,
      eligibleForRetry: eligibleRecords.length,
    };
  }

  /**
   * Gets retry statistics for all integrations
   */
  async getAllRetryStatistics(): Promise<IRetryStatistics[]> {
    const brandConfigMap = await brandConfigManager.getAllBrandsWithConfigs();
    const statistics: IRetryStatistics[] = [];

    for (const [, config] of brandConfigMap.entries()) {
      const integrationPrefix = config.integrationPrefix;
      if (integrationPrefix) {
        try {
          const stats = await this.getRetryStatistics(integrationPrefix);
          statistics.push(stats);
        } catch (error) {
          console.error(
            `Error getting statistics for ${integrationPrefix}:`,
            error
          );
        }
      }
    }

    return statistics;
  }

  /**
   * Logs retry results for monitoring and debugging
   */
  private logRetryResults(results: IRetryResult[]): void {
    const summary = {
      totalBrands: results.length,
      successful: results.filter((r) => r.status === "success").length,
      errors: results.filter((r) => r.status === "error").length,
      skipped: results.filter((r) => r.status === "skipped").length,
      totalRecordsProcessed: results.reduce(
        (sum, r) => sum + r.recordsProcessed,
        0
      ),
    };

    console.log("📊 Retry Results Summary:", summary);

    results.forEach((result) => {
      const emoji =
        result.status === "success"
          ? "✅"
          : result.status === "error"
          ? "❌"
          : "⏭️";
      console.log(
        `${emoji} ${result.brand} (${result.integrationPrefix}): ${result.message}`
      );
    });
  }
}
