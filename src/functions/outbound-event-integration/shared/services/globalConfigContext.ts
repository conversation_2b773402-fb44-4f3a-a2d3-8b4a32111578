/**
 * @fileoverview Global Configuration Context
 *
 * This service provides a global configuration context that loads brand configuration
 * once per Lambda execution and serves it throughout the application lifecycle.
 * This eliminates duplicate database calls and improves performance.
 *
 * Features:
 * - Single database call per Lambda execution
 * - Global configuration access
 * - Brand fallback logic
 * - Runtime configuration caching
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IBrandConfig, brandConfigManager } from "./brandConfigManager";
import { IEventMessage } from "../types/interfaces";
import { BrandDetectionUtil } from "../utils/BrandDetectionUtil";
import { InternalLogger } from "../utils/InternalLogger";

/**
 * Global Configuration Context
 */
export class GlobalConfigContext {
  private static instance: GlobalConfigContext;
  private brandConfig: IBrandConfig | null = null;
  private isInitialized: boolean = false;
  private initializationPromise: Promise<void> | null = null;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): GlobalConfigContext {
    if (!GlobalConfigContext.instance) {
      GlobalConfigContext.instance = new GlobalConfigContext();
    }
    return GlobalConfigContext.instance;
  }

  /**
   * Initialize the global configuration context
   * This should be called once at the start of Lambda execution
   */
  async initialize(message: IEventMessage): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      await this.initializationPromise;
      return;
    }

    this.initializationPromise = this.doInitialize(message);
    await this.initializationPromise;
  }

  /**
   * Actual initialization logic
   */
  private async doInitialize(message: IEventMessage): Promise<void> {
    try {
      // Determine brand from message or environment
      const brand = this.determineBrand(message);

      // Load brand configuration once
      this.brandConfig = await brandConfigManager.getBrandConfig(brand);

      InternalLogger.info(`Global config context initialized for brand: ${brand}`);
      this.isInitialized = true;
    } catch (error) {
      InternalLogger.error("Failed to initialize global config context:", error);
      throw error;
    }
  }

  /**
   * Determine brand from message with centralized detection
   */
  private determineBrand(message: IEventMessage): string {
    try {
      return BrandDetectionUtil.detectBrandFromMessage(message);
    } catch (error) {
      throw new Error(
        "Brand not found in message or payload. Ensure brand is set correctly."
      );
    }
  }

  /**
   * Get the current brand configuration
   */
  getBrandConfig(): IBrandConfig {
    if (!this.isInitialized || !this.brandConfig) {
      throw new Error(
        "Global config context not initialized. Call initialize() first."
      );
    }
    return this.brandConfig;
  }

  /**
   * Get the current brand code
   */
  getBrand(): string {
    return this.getBrandConfig().pk;
  }

  /**
   * Get API key for the current brand
   */
  getApiKey(): string {
    return this.getBrandConfig().apiKey;
  }

  /**
   * Get inbound queue URL for acknowledgements
   */
  getInboundQueueUrl(): string {
    return this.getBrandConfig().inboundQueueUrl;
  }

  /**
   * Get failed records table name
   */
  getFailedRecordsTableName(): string {
    return this.getBrandConfig().failedRecordsTableName;
  }

  /**
   * Get integration prefix
   */
  getIntegrationPrefix(): string {
    return this.getBrandConfig().integrationPrefix;
  }

  /**
   * Get outbound topic ARN
   */
  getOutboundTopicArn(): string {
    return this.getBrandConfig().outboundTopicArn;
  }

  /**
   * Check if context is initialized
   */
  isContextInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Reset the context (useful for testing)
   */
  reset(): void {
    this.brandConfig = null;
    this.isInitialized = false;
    this.initializationPromise = null;
  }

  /**
   * Enrich message with brand information if missing
   */
  enrichMessageWithBrand(message: IEventMessage): IEventMessage {
    if (!message.brand) {
      message.brand = this.getBrand();
    }
    return message;
  }
}

/**
 * Singleton instance for global access
 */
export const globalConfigContext = GlobalConfigContext.getInstance();
