/**
 * @fileoverview Multi-Brand CloudWatch Logger Service
 *
 * This service provides brand-specific logging capabilities for multi-brand
 * integrations. It automatically detects the brand from event context and
 * routes logs to the appropriate CloudWatch log group following industry standards.
 *
 * Features:
 * - Simple console API compatibility (log, info, warn, error, debug, trace)
 * - Brand-specific CloudWatch log groups
 * - Automatic log group creation
 * - Low latency direct logging
 * - Fallback to default log group
 *
 * Usage:
 * ```typescript
 * import { logger } from './multiBrandCloudWatchLogger';
 *
 * // Simple 2-argument logging
 * logger.log('General message', data);
 * logger.info('Info message', data);
 * logger.warn('Warning message', data);
 * logger.error('Error message', error);
 * logger.debug('Debug message', data);
 * logger.trace('Trace message', data);
 *
 * // Legacy compatibility (optional 3rd/4th args)
 * logger.logInfo('Process started', 'SERVICE_NAME', 'PROCESS_START', metadata);
 * ```
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { CloudWatchLogs } from "aws-sdk";
import { IEventRecord } from "../types/interfaces";
import { BrandDetectionUtil } from "../utils/BrandDetectionUtil";

export interface IBrandContext {
  brand: string;
  correlationId?: string;
  scenario?: string;
  timestamp?: string;
}

export interface ILogEntry {
  level: "LOG" | "INFO" | "ERROR" | "WARN" | "DEBUG" | "TRACE";
  message: string;
  brand: string;
  correlationId?: string;
  scenario?: string;
  timestamp: string;
  component?: string;
  event?: string;
  data?: any;
}

export class MultiBrandCloudWatchLogger {
  private cloudWatchLogs: CloudWatchLogs;
  private environment: string;
  private defaultBrand: string;
  private createdLogGroups: Set<string> = new Set();
  private currentBrandContext?: IBrandContext;

  constructor(
    environment: string = process.env.STAGE || "dev",
    defaultBrand: string = "DEFAULT"
  ) {
    this.cloudWatchLogs = new CloudWatchLogs({
      region: process.env.AWS_REGION || "eu-west-1",
    });
    this.environment = environment;
    this.defaultBrand = defaultBrand;
  }

  /**
   * Sets the brand context for subsequent logs
   */
  setBrandContext(context: IBrandContext): void {
    this.currentBrandContext = context;
  }

  /**
   * Extracts brand context from event record using centralized detection
   */
  extractBrandContext(record: IEventRecord): IBrandContext {
    try {
      const body = JSON.parse(record.body);
      let message: any;

      // Handle SNS wrapped messages
      if (body.Type === "Notification" && body.Message) {
        message = JSON.parse(body.Message);
      } else {
        message = body;
      }

      // Use centralized brand detection
      const brand = BrandDetectionUtil.detectBrandFromRecord(
        record,
        this.defaultBrand
      );

      return {
        brand,
        correlationId:
          message.correlationId || message.uuid || record.messageId,
        scenario: message.scenario || message.name,
        timestamp: message.timestamp || new Date().toISOString(),
      };
    } catch (error) {
      // Only use console.error for critical logger failures
      console.error("LOGGER_ERROR: Failed to extract brand context:", error);
      return {
        brand: this.defaultBrand,
        correlationId: record.messageId,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Creates log group if it doesn't exist
   */
  private async ensureLogGroupExists(brand: string): Promise<string> {
    // Format: /aws/lambda/{brand}-gus-sf-integration-{env}
    const logGroupName = `/aws/lambda/${brand.toLowerCase()}-gus-sf-integration-${
      this.environment
    }`;

    if (this.createdLogGroups.has(logGroupName)) {
      return logGroupName;
    }

    try {
      // Check if log group exists
      const describeResp = await this.cloudWatchLogs
        .describeLogGroups({
          logGroupNamePrefix: logGroupName,
        })
        .promise();

      const exists = describeResp.logGroups?.some(
        (g) => g.logGroupName === logGroupName
      );

      if (exists) {
        this.createdLogGroups.add(logGroupName);
        return logGroupName;
      }
    } catch (error) {
      // Only use console.error for critical logger failures
      console.error("LOGGER_ERROR: Failed to describe log groups:", error);
    }

    // Create the log group if it doesn't exist
    try {
      await this.cloudWatchLogs
        .createLogGroup({
          logGroupName,
          tags: {
            Brand: brand,
            Service: "gus-sf-integration",
            Environment: this.environment,
            Team: "EIP",
          },
        })
        .promise();

      // Set retention policy
      await this.cloudWatchLogs
        .putRetentionPolicy({
          logGroupName,
          retentionInDays: process.env.stage === "prod" ? 30 : 7, // 30 days prod
        })
        .promise();

      this.createdLogGroups.add(logGroupName);
      // Note: Successfully created log group for brand
      return logGroupName;
    } catch (createError) {
      // Only use console.error for critical logger failures
      console.error(
        `LOGGER_ERROR: Failed to create log group ${logGroupName}:`,
        createError
      );
      // Fallback to default log group
      return `/aws/lambda/${this.defaultBrand.toLowerCase()}-gus-sf-integration-${
        this.environment
      }`;
    }
  }

  /**
   * Console-compatible log method (equivalent to console.log)
   */
  log(message: string, data?: any): void {
    this.doLog("LOG", message, data);
  }

  /**
   * Console-compatible info method (equivalent to console.info)
   */
  info(message: string, data?: any): void {
    this.doLog("INFO", message, data);
  }

  /**
   * Console-compatible warn method (equivalent to console.warn)
   */
  warn(message: string, data?: any): void {
    this.doLog("WARN", message, data);
  }

  /**
   * Console-compatible error method (equivalent to console.error)
   */
  error(message: string, data?: any): void {
    this.doLog("ERROR", message, data);
  }

  /**
   * Console-compatible debug method (equivalent to console.debug)
   */
  debug(message: string, data?: any): void {
    this.doLog("DEBUG", message, data);
  }

  /**
   * Console-compatible trace method (equivalent to console.trace)
   */
  trace(message: string, data?: any): void {
    this.doLog("TRACE", message, data);
  }

  /**
   * Legacy compatibility - async version of log methods
   */
  async logInfo(
    message: string,
    component?: string,
    event?: string,
    data?: any
  ): Promise<void> {
    await this.doLogAsync("INFO", message, data, component, event);
  }

  /**
   * Legacy compatibility - async version of error methods
   */
  async logError(
    message: string,
    component?: string,
    event?: string,
    error?: any
  ): Promise<void> {
    await this.doLogAsync("ERROR", message, error, component, event);
  }

  /**
   * Legacy compatibility - async version of warn methods
   */
  async logWarn(
    message: string,
    component?: string,
    event?: string,
    data?: any
  ): Promise<void> {
    await this.doLogAsync("WARN", message, data, component, event);
  }

  /**
   * Internal logging method - synchronous for better performance
   */
  private doLog(
    level: "LOG" | "INFO" | "ERROR" | "WARN" | "DEBUG" | "TRACE",
    message: string,
    data?: any
  ): void {
    const brand = this.currentBrandContext?.brand || this.defaultBrand;

    const logEntry: ILogEntry = {
      level,
      message,
      brand,
      correlationId: this.currentBrandContext?.correlationId,
      scenario: this.currentBrandContext?.scenario,
      timestamp: new Date().toISOString(),
      data,
    };

    // Send to CloudWatch directly (fire and forget for low latency)
    this.sendToCloudWatch(logEntry).catch((error) => {
      // Only use console.error for CloudWatch failures to avoid infinite loops
      console.error(
        `LOGGER_ERROR: Failed to send log to CloudWatch for brand ${brand}:`,
        error
      );
    });
  }

  /**
   * Internal logging method - asynchronous for legacy compatibility
   */
  private async doLogAsync(
    level: "LOG" | "INFO" | "ERROR" | "WARN" | "DEBUG" | "TRACE",
    message: string,
    data?: any,
    component?: string,
    event?: string
  ): Promise<void> {
    const brand = this.currentBrandContext?.brand || this.defaultBrand;

    const logEntry: ILogEntry = {
      level,
      message,
      brand,
      correlationId: this.currentBrandContext?.correlationId,
      scenario: this.currentBrandContext?.scenario,
      timestamp: new Date().toISOString(),
      component,
      event,
      data,
    };

    // Send to CloudWatch
    try {
      await this.sendToCloudWatch(logEntry);
    } catch (error) {
      // Only use console.error for CloudWatch failures to avoid infinite loops
      console.error(
        `LOGGER_ERROR: Failed to send log to CloudWatch for brand ${brand}:`,
        error
      );
    }
  }

  /**
   * Sends log entry to CloudWatch directly
   */
  private async sendToCloudWatch(logEntry: ILogEntry): Promise<void> {
    try {
      const logGroupName = await this.ensureLogGroupExists(logEntry.brand);
      const logStreamName = this.getLogStreamName();

      // Ensure log stream exists
      try {
        await this.cloudWatchLogs
          .createLogStream({
            logGroupName,
            logStreamName,
          })
          .promise();
      } catch (error) {
        // Log stream might already exist, ignore error
      }

      // Send log event
      await this.cloudWatchLogs
        .putLogEvents({
          logGroupName,
          logStreamName,
          logEvents: [
            {
              timestamp: new Date(logEntry.timestamp).getTime(),
              message: JSON.stringify(logEntry, null, 2),
            },
          ],
        })
        .promise();
    } catch (error) {
      // Only use console.error for critical logger failures
      console.error("LOGGER_ERROR: Failed to send log to CloudWatch:", error);
    }
  }

  /**
   * Generates a log stream name in the format of
   * `${year}/${month}/${day}/[${version}]${randomHash}`
   */
  private getLogStreamName(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");

    // Always use $LATEST for most Lambda functions
    const version = "$LATEST";

    // Generate a random 32-character hex string (to mimic Lambda container hash)
    const randomHash = Array.from({ length: 32 }, () =>
      Math.floor(Math.random() * 16).toString(16)
    ).join("");

    return `${year}/${month}/${day}/[${version}]${randomHash}`;
  }

  /**
   * Logs event processing start
   */
  async logEventStart(
    record: IEventRecord,
    context: IBrandContext
  ): Promise<void> {
    this.setBrandContext(context);

    await this.logInfo(
      `Processing event for brand: ${context.brand}`,
      "EVENT_PROCESSOR",
      "EVENT_STARTED",
      {
        messageId: record.messageId,
        correlationId: context.correlationId,
        scenario: context.scenario,
      }
    );
  }

  /**
   * Logs event processing result
   */
  async logEventResult(record: IEventRecord, result: any): Promise<void> {
    const context =
      this.currentBrandContext || this.extractBrandContext(record);

    const level = result.status ? "INFO" : "ERROR";
    const message = result.status
      ? `Event processed successfully for brand: ${context.brand}`
      : `Event processing failed for brand: ${context.brand}`;

    await this.doLogAsync(
      level,
      message,
      {
        messageId: record.messageId,
        correlationId: context.correlationId,
        scenario: context.scenario,
        result: result,
      },
      "EVENT_PROCESSOR",
      "EVENT_COMPLETED"
    );
  }
}

// Singleton instance for multi-brand logging
export const logger = new MultiBrandCloudWatchLogger();
