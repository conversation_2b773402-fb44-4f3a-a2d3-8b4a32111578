/**
 * @fileoverview Brand Configuration Management
 *
 * This module provides brand-specific configuration management for multi-brand
 * integrations. It handles loading, caching, and providing brand-specific
 * settings for event processing and failed record handling.
 *
 * Features:
 * - DynamoDB-based brand configuration storage
 * - In-memory caching for performance
 * - Fallback to default configurations
 * - Separate configs for event processing vs failed records
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { DynamoDB } from "aws-sdk";
import { IEventConfig } from "../types/interfaces";
import { DynamoDBService } from "src/common/dynamodbService";
import { CacheManager } from "../utils/CacheManager";
import { InternalLogger } from "../utils/InternalLogger";

/**
 * Brand-specific configuration
 */
export interface IBrandConfig {
  pk: string; // Primary key (brand identifier)
  inboundQueueArn: string;
  inboundQueueUrl: string; // For acknowledgements to partner system
  outboundQueueArn: string; // For future use
  outboundTopicArn: string; // For future use
  failedRecordsTableName: string; // Shared table
  integrationPrefix: string;
  apiKey: string; // Brand-specific API key
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Default fallback configuration
 */
const DEFAULT_BRAND_CONFIG: IBrandConfig = {
  pk: "ARD",
  inboundQueueArn: "",
  inboundQueueUrl: "",
  outboundQueueArn: "",
  outboundTopicArn: "",
  failedRecordsTableName:
    process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME ||
    `gus-eip-brand-configurations-dev-${process.env.stage || "dev"}`,
  integrationPrefix: "ARD_GUS_SF",
  apiKey: "",
};

/**
 * Brand Configuration Manager
 */
export class BrandConfigManager {
  private configTableName: string;
  private cache = CacheManager.getBrandConfigCache();
  private dynamoDbService: DynamoDBService;

  constructor() {
    this.configTableName =
      process.env.GUS_EIP_INTEGRATION_BRAND_CONFIG_TABLE_NAME ||
      `gus-eip-brand-configurations-${process.env.stage || "dev"}`;
    this.dynamoDbService = new DynamoDBService();
  }

  /**
   * Get brand-specific configuration
   */
  async getBrandConfig(brand: string): Promise<IBrandConfig> {
    const normalizedBrand = brand.toUpperCase();

    // Use centralized cache
    return await this.cache.getOrSet(normalizedBrand, async () => {
      try {
        // Load from DynamoDB
        const config = await this.loadBrandConfigFromDB(normalizedBrand);
        return config;
      } catch (error) {
        InternalLogger.error(
          `Failed to load brand config for ${normalizedBrand}:`,
          error
        );

        // Return default config with brand-specific naming
        const fallbackConfig = {
          ...DEFAULT_BRAND_CONFIG,
          pk: normalizedBrand,
          apiKey: `${normalizedBrand.toLowerCase()}-fallback-api-key`,
        };

        return fallbackConfig;
      }
    });
  }

  /**
   * Load brand configuration from DynamoDB
   */
  private async loadBrandConfigFromDB(brand: string): Promise<IBrandConfig> {
    const keys: DynamoDB.DocumentClient.Key = {
      PK: brand,
    };

    const result = await this.dynamoDbService.getObject(
      this.configTableName,
      keys
    );

    if (!result.Item) {
      throw new Error(`Brand configuration not found for: ${brand}`);
    }

    return BrandConfigManager.toBrandConfig(result.Item);
  }

  /**
   * Clear cache (useful for testing or config updates)
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Explicit mapping to interface, handling missing/default fields
   * @param {any} item - DynamoDB item from brand configuration table
   * @returns {IBrandConfig} Mapped brand configuration
   */
  private static toBrandConfig(item: any): IBrandConfig {
    return {
      pk: item.PK,
      inboundQueueArn: item.inboundQueueArn,
      inboundQueueUrl: item.inboundQueueUrl,
      outboundQueueArn: item.outboundQueueArn,
      outboundTopicArn: item.outboundTopicArn,
      failedRecordsTableName: item.failedRecordsTableName,
      integrationPrefix:
        item.outboundIntegrationPrefix || "DEFAULT_INTEGRATION",
      apiKey: item.apikey,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    };
  }

  /**
   * Get event processing config for a brand (for backward compatibility)
   */
  async getEventProcessingConfig(brand: string): Promise<IEventConfig> {
    const brandConfig = await this.getBrandConfig(brand);

    return {
      failedRecordsTableName: brandConfig.failedRecordsTableName,
      outboundTopicArn: brandConfig.outboundQueueArn, // Using outbound queue for now
      integrationPrefix: brandConfig.integrationPrefix,
      retryConfig: {
        maxRetries: 3,
        retryDelayMs: 1000,
        exponentialBackoff: true,
      },
    };
  }

  /**
   * Pre-load configurations for multiple brands
   */
  async preloadBrands(brands: string[]): Promise<void> {
    const promises = brands.map((brand) => this.getBrandConfig(brand));
    await Promise.all(promises);
  }

  /**
   * Gets all brands with their configurations in one operation.
   * This is more efficient than calling getAllBrands() then getBrandConfig() for each.
   *
   * @returns {Promise<Map<string, IBrandConfig>>} Map of brand code to configuration
   */
  async getAllBrandsWithConfigs(): Promise<Map<string, IBrandConfig>> {
    try {

      const result = await this.dynamoDbService.scanTable(this.configTableName);
      const brandConfigMap = new Map<string, IBrandConfig>();

      if (!result.Items || result.Items.length === 0) {
        throw new Error("No brand configurations found in database");
      }

      result.Items.forEach((item) => {
        const config = BrandConfigManager.toBrandConfig(item);
        if (config.pk && config.pk !== "DEFAULT") {
          // Cache the config while we have it
          this.cache.set(config.pk, config);
          brandConfigMap.set(config.pk, config);
        }
      });

      InternalLogger.info(
        `Found ${brandConfigMap.size} brand configurations in database:`,
        Array.from(brandConfigMap.keys())
      );
      return brandConfigMap;
    } catch (error) {
      InternalLogger.error(
        "Error fetching brand configurations from database:",
        error
      );
      return new Map<string, IBrandConfig>();
    }
  }
}

// Singleton instance
export const brandConfigManager = new BrandConfigManager();
