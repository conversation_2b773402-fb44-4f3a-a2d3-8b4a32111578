/**
 * @fileoverview Base Classes for Event Processing
 *
 * This module contains abstract base classes that provide common functionality
 * for event handlers and processors in the Outbound Event Integration Service.
 *
 * The base classes provide:
 * - Common logging functionality
 * - Input validation patterns
 * - Error handling patterns
 * - Consistent interfaces for inheritance
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import {
  IEventHandler,
  IEventMessage,
  IHandlerResponse,
  IEventLogger,
  IEventProcessor,
  IEventRecord,
  IProcessingResult,
  IEventValidator,
  IValidationResult,
} from "../types/interfaces";

/**
 * Abstract base class for all event handlers.
 *
 * This class provides common functionality that all event handlers need,
 * including logging, error handling, and input validation.
 *
 * All concrete event handlers should extend this class to ensure
 * consistent behavior and interfaces.
 */
export abstract class BaseEventHandler implements IEventHandler {
  protected logger: IEventLogger;

  constructor(logger?: IEventLogger) {
    this.logger = logger;
  }

  public setLogger(logger: IEventLogger): void {
    this.logger = logger;
  }

  public abstract handle(message: IEventMessage): Promise<IHandlerResponse>;

  protected async logInfo(
    message: string,
    event: string,
    component: string,
    metadata?: any
  ): Promise<void> {
    if (this.logger) {
      await this.logger.log(null, message, event, component, metadata);
    }
  }

  protected async logError(
    message: string,
    event: string,
    component: string,
    metadata?: any
  ): Promise<void> {
    if (this.logger) {
      await this.logger.error(null, message, event, component, metadata);
    }
  }

  protected validateInput(message: IEventMessage): void {
    if (!message) {
      throw new Error("Event message is required");
    }
    if (!message.scenario) {
      throw new Error("Event scenario is required");
    }
  }
}

export abstract class BaseEventProcessor implements IEventProcessor {
  protected abstract validator: IEventValidator;
  protected abstract handlerRegistry: any;
  protected abstract contextExtractor: any;

  public async processEvents(event: {
    Records: IEventRecord[];
  }): Promise<IProcessingResult[]> {
    const results: IProcessingResult[] = [];

    console.log(`Processing ${event.Records.length} records`);

    for (const record of event.Records) {
      try {
        const result = await this.processRecord(record);
        results.push(result);
      } catch (error) {
        console.error(`Error processing record ${record.messageId}:`, error);
        results.push({
          status: false,
          error: error.message || "Unknown error",
          messageId: record.messageId,
        });
      }
    }

    return results;
  }

  public abstract processRecord(
    record: IEventRecord
  ): Promise<IProcessingResult>;

  protected parseRecord(record: IEventRecord): IEventMessage {
    try {
      const eventBody = JSON.parse(record.body);
      const message: IEventMessage = JSON.parse(eventBody.Message);
      message.eventId = record.messageId;
      return message;
    } catch (error) {
      throw new Error(`Failed to parse record: ${error.message}`);
    }
  }

  protected createSuccessResult(
    handlerResponse: IHandlerResponse,
    messageId?: string
  ): IProcessingResult {
    return {
      status: handlerResponse.status,
      data: handlerResponse.data,
      error: handlerResponse.error,
      messageId,
    };
  }

  protected createErrorResult(
    error: string,
    messageId?: string
  ): IProcessingResult {
    return {
      status: false,
      error,
      messageId,
    };
  }
}

export abstract class BaseEventValidator implements IEventValidator {
  public abstract validate(message: IEventMessage): IValidationResult;

  protected createValidationResult(
    isValid: boolean,
    errors: any[] = []
  ): IValidationResult {
    return {
      isValid,
      errors,
    };
  }

  protected validateRequiredField(value: any, fieldName: string): any {
    if (!value) {
      return {
        field: fieldName,
        message: `${fieldName} is required`,
      };
    }
    return null;
  }
}
