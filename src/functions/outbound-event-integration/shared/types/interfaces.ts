/**
 * @fileoverview Type Definitions and Interfaces
 * 
 * This module contains all TypeScript interfaces and type definitions
 * used throughout the Outbound Event Integration Service.
 * 
 * The interfaces are organized into:
 * - Core event processing interfaces
 * - Handler and processor interfaces
 * - Configuration and context interfaces
 * - Validation and result interfaces
 * - Legacy compatibility types
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// =============================================================================
// CORE EVENT INTERFACES
// =============================================================================

/**
 * Represents an event message containing scenario-specific data.
 * This is the primary data structure for events flowing through the system.
 */
export interface IEventMessage {
  eventId?: string;
  scenario: string;
  uuid?: string;
  status?: 'Success' | 'Failed' | 'Pending';
  payload: {
    gusApplicationId?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * Represents a record of an event as it is stored in the messaging system.
 * Contains the raw body of the message and any associated metadata.
 */
export interface IEventRecord {
  messageId: string;
  body: string;
  messageGroupId?: string;
  attributes?: Record<string, any>;
  eventSourceARN?: string;
  eventSource?: string;
  awsRegion?: string;
  receiptHandle?: string;
  md5OfBody?: string;
  messageAttributes?: Record<string, any>;
}

/**
 * Represents the body of an event message, typically containing
 * the message content and metadata like Topic ARN and Message ID.
 */
export interface IEventBody {
  Message: string;
  TopicArn?: string;
  MessageId?: string;
}

// =============================================================================
// HANDLER AND PROCESSOR INTERFACES
// =============================================================================

/**
 * Represents the result of processing an event, indicating success or failure
 * and providing additional data or error information as needed.
 */
export interface IProcessingResult {
  status: boolean;
  error?: string;
  missingFields?: string[];
  messageId?: string;
  data?: any;
  validationErrors?: IValidationError[];
}

/**
 * Represents the response from an event handler, including the status of the
 * handling operation and any associated data or error information.
 */
export interface IHandlerResponse {
  status: boolean;
  data?: any;
  error?: string;
  validationErrors?: IValidationError[];
}

/**
 * Interface for event handlers, which are responsible for processing
 * event messages. Handlers must implement the handle method to process
 * incoming messages and the setLogger method to receive a logger instance.
 */
export interface IEventHandler {
  handle(message: IEventMessage): Promise<IHandlerResponse>;
  setLogger(logger: IEventLogger): void;
}

// =============================================================================
// LOGGER INTERFACE
// =============================================================================

/**
 * Interface for logging events and errors within the event processing system.
 * Implementations of this interface should handle the actual logging mechanism,
 * such as sending logs to a logging service or writing to a file.
 */
export interface IEventLogger {
  log(record: any, message: string, event: string, component: string, metadata?: any): Promise<void>;
  error(record: any, message: string, event: string, component?: string, metadata?: any): Promise<void>;
}

// =============================================================================
// VALIDATION INTERFACES
// =============================================================================

/**
 * Interface for validating event messages. Validators must implement the
 * validate method, which checks the message against defined rules and
 * returns a validation result.
 */
export interface IEventValidator {
  validate(message: IEventMessage): IValidationResult;
}

/**
 * Represents the result of validating an event message, including
 * a flag indicating validity and a list of validation errors if any.
 */
export interface IValidationResult {
  isValid: boolean;
  errors: IValidationError[];
}

/**
 * Represents a validation error, including the field that caused the error
 * and a message describing the error.
 */
export interface IValidationError {
  field: string;
  message: string;
}

// =============================================================================
// PROCESSOR INTERFACE
// =============================================================================

/**
 * Interface for processing events, typically by delegating to the
 * appropriate handler based on the event's scenario.
 */
export interface IEventProcessor {
  processEvents(event: { Records: IEventRecord[] }): Promise<IProcessingResult[]>;
  processRecord(record: IEventRecord): Promise<IProcessingResult>;
}

// =============================================================================
// REGISTRY INTERFACE
// =============================================================================

/**
 * Interface for registering and retrieving event handlers.
 * The registry maintains a mapping of scenarios to their corresponding handlers,
 * allowing for dynamic discovery and invocation of handlers based on the event scenario.
 */
export interface IEventHandlerRegistry {
  registerHandler(scenario: string, handler: IEventHandler): void;
  getHandler(scenario: string): IEventHandler | null;
  hasHandler(scenario: string): boolean;
  getRegisteredScenarios(): string[];
  getHandlerCount(): number;
  removeHandler(scenario: string): boolean;
  clear(): void;
}

// =============================================================================
// CONTEXT AND CONFIGURATION INTERFACES
// =============================================================================

/**
 * Represents the context in which an event is processed, including
 * identifiers for tracing and logging, as well as the event scenario
 * and associated logger.
 */
export interface IEventContext {
  applicationFormId: string;
  correlationId: string;
  brand: string;
  scenario: string;
  logger: IEventLogger;
}

/**
 * Configuration settings for the event processing system, including
 * table names, topic ARNs, integration prefixes, and retry settings.
 */
export interface IEventConfig {
  failedRecordsTableName: string;
  outboundTopicArn: string;
  integrationPrefix: string;
  retryConfig: {
    maxRetries: number;
    retryDelayMs: number;
    exponentialBackoff: boolean;
  };
}

// =============================================================================
// FAILED RECORD INTERFACE
// =============================================================================

/**
 * Represents a record of a failed event, including information
 * about the failure and the original event data. Failed records
 * are stored for later analysis or reprocessing.
 * 
 * There are two types of failed records:
 * 1. Partition records (PK without #): Track overall retry status for a messageGroupId
 * 2. Detail records (PK with #): Store the actual event data for processing
 */
export interface IFailedRecord {
  PK: string; // Format: "INTEGRATION_PREFIX" or "INTEGRATION_PREFIX#messageGroupId"
  SK: string; // messageGroupId for partition records, uuid/messageId for detail records
  
  // Common fields
  status: 'Failed' | 'Processing' | 'Completed';
  retryCount: number;
  createdAt: string;
  updatedAt?: string;
  
  // Detail record fields (when PK contains #)
  attributes?: Record<string, any>;
  body?: string;
  awsRegion?: string;
  eventSource?: string;
  eventSourceARN?: string;
  md5OfBody?: string;
  messageAttributes?: Record<string, any>;
  messageId?: string;
  receiptHandle?: string;
}

// =============================================================================
// LEGACY COMPATIBILITY TYPES
// =============================================================================

/**
 * Legacy type aliases for compatibility with existing code.
 * These types are deprecated and will be removed in future versions.
 */
export type SQSRecord = IEventRecord;
export type EventBody = IEventBody;
export type PlatformEventMessage = IEventMessage;
export type ProcessingResult = IProcessingResult;
export type HandlerResponse = IHandlerResponse;
export type EventHandler = IEventHandler;
export type LoggerContext = IEventLogger;
export type ServiceConfig = IEventConfig;
export type ValidationError = IValidationError;
export type ContextExtraction = IEventContext;
export type FailedRecord = IFailedRecord;
