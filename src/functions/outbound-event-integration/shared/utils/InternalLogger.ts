/**
 * @fileoverview Internal Logger for System Services
 *
 * This is a simple internal logger used by core system services like
 * BrandConfigManager and GlobalConfigContext to avoid circular dependencies
 * with the main MultiBrandCloudWatchLogger.
 *
 * This logger should only be used for system-level logging and not for
 * business logic logging.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

export class InternalLogger {
  private static formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const prefix = `[SYSTEM] [${timestamp}] ${level}:`;
    
    if (data) {
      return `${prefix} ${message} - ${JSON.stringify(data)}`;
    }
    return `${prefix} ${message}`;
  }

  /**
   * Log info messages for system operations
   */
  static info(message: string, data?: any): void {
    // Use direct console for system messages to avoid circular dependencies
    console.info(this.formatMessage('INFO', message, data));
  }

  /**
   * Log error messages for system operations
   */
  static error(message: string, data?: any): void {
    // Use direct console for system messages to avoid circular dependencies
    console.error(this.formatMessage('ERROR', message, data));
  }

  /**
   * Log warning messages for system operations
   */
  static warn(message: string, data?: any): void {
    // Use direct console for system messages to avoid circular dependencies
    console.warn(this.formatMessage('WARN', message, data));
  }

  /**
   * Log debug messages for system operations
   */
  static debug(message: string, data?: any): void {
    // Use direct console for system messages to avoid circular dependencies
    console.debug(this.formatMessage('DEBUG', message, data));
  }
}
