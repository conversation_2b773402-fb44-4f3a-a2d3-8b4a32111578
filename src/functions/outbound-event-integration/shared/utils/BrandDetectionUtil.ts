/**
 * @fileoverview Brand Detection Utility
 *
 * This utility provides centralized brand detection logic to eliminate
 * duplicate code across services. It consolidates brand detection from
 * various sources into a single, testable utility.
 *
 * Features:
 * - Centralized brand detection logic
 * - Multiple detection strategies
 * - Fallback mechanisms
 * - Consistent brand normalization
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IEventMessage, IEventRecord } from "../types/interfaces";
import { InternalLogger } from "./InternalLogger";

/**
 * Brand Detection Utility
 */
export class BrandDetectionUtil {
  /**
   * Detects brand from event message with priority-based detection
   */
  static detectBrandFromMessage(message: IEventMessage): string {
    // Priority 1: Direct brand field in message
    if (message.brand) {
      return message.brand.toUpperCase();
    }

    // Priority 2: Brand in payload
    if (message.payload?.brand) {
      return message.payload.brand.toUpperCase();
    }

    // Priority 3: Brand in nested payload structures
    if (message.payload?.data?.brand) {
      return message.payload.data.brand.toUpperCase();
    }

    throw new Error(
      "Brand not found in message or payload. Ensure brand is set correctly."
    );
  }

  /**
   * Detects brand from event record (SQS/SNS) with multiple strategies
   */
  static detectBrandFromRecord(record: IEventRecord, defaultBrand: string = "DEFAULT"): string {
    try {
      const body = JSON.parse(record.body);
      let message: any;

      // Handle SNS wrapped messages
      if (body.Type === "Notification" && body.Message) {
        message = JSON.parse(body.Message);
      } else {
        message = body;
      }

      // Try message-based detection first
      try {
        return this.detectBrandFromMessage(message);
      } catch {
        // Continue to other detection methods
      }

      // Extract from SQS ARN (brand-specific queues)
      if (record.eventSourceARN) {
        const brand = this.extractBrandFromArn(record.eventSourceARN);
        if (brand) {
          return brand;
        }
      }

      // Extract from topic ARN in SNS message
      if (message.TopicArn) {
        const brand = this.extractBrandFromArn(message.TopicArn);
        if (brand) {
          return brand;
        }
      }

      // Fallback to default
      return defaultBrand.toUpperCase();
    } catch (error) {
      InternalLogger.error("Failed to detect brand from record:", error);
      return defaultBrand.toUpperCase();
    }
  }

  /**
   * Extracts brand from AWS ARN patterns
   */
  private static extractBrandFromArn(arn: string): string | null {
    // Match patterns like:
    // - arn:aws:sqs:region:account:DEV-ABC-INTEGRATION-QUEUE.fifo
    // - arn:aws:sns:region:account:PROD-XYZ-OUTBOUND-TOPIC
    const patterns = [
      /(?:DEV|PROD)-([A-Z]+)-.*-(?:INTEGRATION|OUTBOUND)/i,
      /([A-Z]+)-.*-(?:INTEGRATION|OUTBOUND)/i,
    ];

    for (const pattern of patterns) {
      const match = arn.match(pattern);
      if (match) {
        return match[1].toUpperCase();
      }
    }

    return null;
  }

  /**
   * Normalizes brand code to uppercase
   */
  static normalizeBrand(brand: string): string {
    return brand.trim().toUpperCase();
  }

  /**
   * Validates brand code format
   */
  static isValidBrand(brand: string): boolean {
    // Brand codes should be 2-5 uppercase letters
    return /^[A-Z]{2,5}$/.test(brand);
  }
}
