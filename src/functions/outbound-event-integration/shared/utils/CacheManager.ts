/**
 * @fileoverview Centralized Caching Utility
 *
 * This utility provides centralized caching capabilities to eliminate
 * duplicate caching logic across services. It supports TTL-based caching
 * and provides a consistent interface for cache operations.
 *
 * Features:
 * - TTL-based caching with automatic expiry
 * - Generic type support
 * - Cache statistics and monitoring
 * - Automatic cleanup of expired entries
 * - Memory-efficient implementation
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

/**
 * Cache entry with TTL support
 */
interface ICacheEntry<T> {
  value: T;
  expiry: number;
  createdAt: number;
  accessCount: number;
}

/**
 * Cache statistics
 */
interface ICacheStats {
  hitCount: number;
  missCount: number;
  totalEntries: number;
  expiredEntries: number;
  hitRate: number;
}

/**
 * Generic TTL Cache Implementation
 */
export class TTLCache<T> {
  private cache: Map<string, ICacheEntry<T>> = new Map();
  private ttl: number;
  private maxEntries: number;
  private stats: ICacheStats;

  constructor(ttlMs: number = 5 * 60 * 1000, maxEntries: number = 1000) {
    this.ttl = ttlMs;
    this.maxEntries = maxEntries;
    this.stats = {
      hitCount: 0,
      missCount: 0,
      totalEntries: 0,
      expiredEntries: 0,
      hitRate: 0,
    };
  }

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.missCount++;
      this.updateHitRate();
      return null;
    }

    // Check if entry has expired
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      this.stats.expiredEntries++;
      this.stats.missCount++;
      this.updateHitRate();
      return null;
    }

    // Update access count
    entry.accessCount++;
    this.stats.hitCount++;
    this.updateHitRate();
    
    return entry.value;
  }

  /**
   * Set value in cache
   */
  set(key: string, value: T): void {
    const now = Date.now();
    const entry: ICacheEntry<T> = {
      value,
      expiry: now + this.ttl,
      createdAt: now,
      accessCount: 0,
    };

    // Remove oldest entry if cache is full
    if (this.cache.size >= this.maxEntries) {
      this.evictOldest();
    }

    this.cache.set(key, entry);
    this.stats.totalEntries++;
  }

  /**
   * Check if key exists and is valid
   */
  has(key: string): boolean {
    return this.get(key) !== null;
  }

  /**
   * Delete entry from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.resetStats();
  }

  /**
   * Get cache statistics
   */
  getStats(): ICacheStats {
    return { ...this.stats };
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Get all keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Clean up expired entries
   */
  cleanup(): number {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    this.stats.expiredEntries += expiredCount;
    return expiredCount;
  }

  /**
   * Get or set pattern - common caching pattern
   */
  async getOrSet(key: string, factory: () => Promise<T>): Promise<T> {
    const cached = this.get(key);
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    this.set(key, value);
    return value;
  }

  /**
   * Evict oldest entry (LRU-like behavior)
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.createdAt < oldestTime) {
        oldestTime = entry.createdAt;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Update hit rate calculation
   */
  private updateHitRate(): void {
    const total = this.stats.hitCount + this.stats.missCount;
    this.stats.hitRate = total > 0 ? (this.stats.hitCount / total) * 100 : 0;
  }

  /**
   * Reset statistics
   */
  private resetStats(): void {
    this.stats = {
      hitCount: 0,
      missCount: 0,
      totalEntries: 0,
      expiredEntries: 0,
      hitRate: 0,
    };
  }
}

/**
 * Singleton caches for different use cases
 */
export class CacheManager {
  private static brandConfigCache: TTLCache<any> = new TTLCache(5 * 60 * 1000); // 5 minutes
  private static logGroupCache: TTLCache<string> = new TTLCache(30 * 60 * 1000); // 30 minutes
  private static generalCache: TTLCache<any> = new TTLCache(10 * 60 * 1000); // 10 minutes

  /**
   * Get brand configuration cache
   */
  static getBrandConfigCache(): TTLCache<any> {
    return this.brandConfigCache;
  }

  /**
   * Get log group cache
   */
  static getLogGroupCache(): TTLCache<string> {
    return this.logGroupCache;
  }

  /**
   * Get general purpose cache
   */
  static getGeneralCache(): TTLCache<any> {
    return this.generalCache;
  }

  /**
   * Clean up all caches
   */
  static cleanupAll(): void {
    this.brandConfigCache.cleanup();
    this.logGroupCache.cleanup();
    this.generalCache.cleanup();
  }

  /**
   * Get combined cache statistics
   */
  static getAllStats(): Record<string, ICacheStats> {
    return {
      brandConfig: this.brandConfigCache.getStats(),
      logGroup: this.logGroupCache.getStats(),
      general: this.generalCache.getStats(),
    };
  }
}
