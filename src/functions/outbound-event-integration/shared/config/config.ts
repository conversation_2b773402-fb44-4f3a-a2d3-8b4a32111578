/**
 * @fileoverview Configuration Constants and Settings
 *
 * This module contains all configuration constants, logger events, and
 * default settings for the Outbound Event Integration Service.
 *
 * It provides:
 * - Default configuration for event processing
 * - Logger events and components for consistent logging
 * - Environment-specific settings
 * - Retry policies and timeouts
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IEventConfig } from "../types/interfaces";
import { LoggerEnum } from "@gus-eip/loggers";

/**
 * Base configuration used as fallback when brand-specific config is not available
 */
export const BASE_CONFIG: IEventConfig = {
  failedRecordsTableName:
    process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME ||
    "gus-eip-failed-records",
  outboundTopicArn: process.env.DEFAULT_OUTBOUND_TOPIC_ARN || "",
  integrationPrefix: "BASE_INTEGRATION",
  retryConfig: {
    maxRetries: parseInt(process.env.MAX_RETRIES || "3"),
    retryDelayMs: parseInt(process.env.RETRY_DELAY_MS || "1000"),
    exponentialBackoff: process.env.EXPONENTIAL_BACKOFF === "true",
  },
};

/**
 * Default config for backward compatibility
 * @deprecated Use BrandConfigManager.getBrandConfig() instead
 */
export const DEFAULT_CONFIG: IEventConfig = BASE_CONFIG;

/**
 * Logger events for consistent logging across the system.
 * Using LoggerEnum to get standardized events and components.
 */
// Initialize LoggerEnum to get available events and components
const loggerEnum = new LoggerEnum();

// Export the LoggerEnum instance for direct use
export { loggerEnum };

// Export entire Event and Component objects from LoggerEnum - no hardcoding!
export const LOGGER_EVENTS = loggerEnum.Event;
export const LOGGER_COMPONENTS = loggerEnum.Component;

export const PROCESSING_STATUS = {
  SUCCESS: "Success",
  FAILED: "Failed",
  PENDING: "Pending",
} as const;
