/**
 * @fileoverview Main Lambda Entry Point for the Outbound Event Integration Service
 *
 * This module provides the Lambda function configurations for:
 * 1. Processing outbound events from external systems to target integrations
 * 2. Retrying failed records on a scheduled basis
 *
 * The service handles event processing in a fault-tolerant manner with:
 * - Automatic retry mechanisms for failed records
 * - Comprehensive logging and monitoring
 * - Support for multiple event scenarios and handlers
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { handlerPath } from "@libs/handler-resolver";

/**
 * Lambda function configuration for processing outbound events.
 *
 * This function is triggered by SQS messages and processes events
 * from external systems (like student information systems) to
 * target integrations (like Salesforce).
 *
 * Features:
 * - Handles FIFO SQS queue messages
 * - Processes events with appropriate handlers based on scenario
 * - Stores failed records for retry processing
 * - Comprehensive logging and error handling
 */
export const outboundEventIntegration = {
  // Handler for processing outbound events from external systems
  handler: `${handlerPath(
    __dirname
  )}/outbound-events/outboundEventHandler.handler`,
  name: "outbound-event-integration-${self:provider.stage}",
  events: [
    {
      sqs: {
        arn: "arn:aws:sqs:eu-west-1:933713876074:DEV-ARD-GUS-SF-INTEGRATION-QUEUE.fifo",
      },
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 180,
  memorySize: 512,
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};

/**
 * Lambda function configuration for retrying failed records.
 *
 * This function is triggered on a schedule (every 60 minutes) to:
 * - Retrieve failed records from the DynamoDB table
 * - Retry processing for records that haven't exceeded max retries
 * - Update retry counts and status accordingly
 * - Mark records as exhausted if max retries exceeded
 *
 * Features:
 * - Scheduled execution for automatic retry processing
 * - Configurable retry limits and exponential backoff
 * - Comprehensive logging of retry attempts
 * - Automatic cleanup of exhausted records
 */
export const outboundEventIntegrationFailedRecordProcessor = {
  // Handler for retrying failed records on a schedule
  handler: `${handlerPath(
    __dirname
  )}/failed-records/failedRecordsHandler.retryFailedRecords`,
  name: "outbound-event-failed-record-processor-${self:provider.stage}",
  events: [
    {
      schedule: "rate(60 minutes)",
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 180,
  memorySize: 512,
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};
