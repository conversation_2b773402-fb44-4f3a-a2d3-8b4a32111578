/**
 * Dynamic Test for Outbound Event Integration
 *
 * Tests any scenario with dynamic payload injection and validation testing
 *
 * Usage:
 * - Test positive flow: ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts ENROLMENT_STAGE
 * - Test validation failure: ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts ENROLMENT_STAGE invalid
 * - Available scenarios: OFFER_STAGE, PAYMENT_RECEIVED, REJECT_DOCUMENT, VISA_STATUS_UPDATE, ENROLMENT_STAGE, CLOSED_LOST_STAGE
 */

/* Test Positive flowa scanarios:
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts ENROLMENT_STAGE
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts OFFER_STAGE
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts PAYMENT_RECEIVED
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts REJECT_DOCUMENT
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts VISA_STATUS_UPDATE
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts CLOSED_LOST_STAGE
  */
/* Test Invalid flow scenarios:
 ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts ENROLMENT_STAGE invalid
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts OFFER_STAGE invalid
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts PAYMENT_RECEIVED invalid
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts REJECT_DOCUMENT invalid
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts VISA_STATUS_UPDATE invalid
ts-node --transpile-only src\functions\outbound-event-integration\test\simpleTest.ts CLOSED_LOST_STAGE invalid
  */

import { processOutboundEvents } from "../outbound-events/outboundEventHandler";
import { logger } from "../shared/services/multiBrandCloudWatchLogger";
import * as dotenv from "dotenv";
import * as fs from "fs";
import * as path from "path";

dotenv.config(); // Load environment variables from .env file

// Get command line arguments
const scenario = process.argv[2]?.toUpperCase() || "ENROLMENT_STAGE";
const testType = process.argv[3]?.toLowerCase() || "valid"; // 'valid' or 'invalid'

// Available scenarios
const AVAILABLE_SCENARIOS = [
  "OFFER_STAGE",
  "PAYMENT_RECEIVED",
  "REJECT_DOCUMENT",
  "VISA_STATUS_UPDATE",
  "ENROLMENT_STAGE",
  "CLOSED_LOST_STAGE",
];

/**
 * Load test payload from JSON file
 */
function loadTestPayload(scenario: string, testType: string): any {
  const testDir = path.join(__dirname, "events");
  const filename =
    testType === "valid"
      ? `${scenario.toLowerCase()}-valid.json`
      : `${scenario.toLowerCase()}-invalid.json`;

  const filePath = path.join(testDir, filename);

  try {
    const fileContent = fs.readFileSync(filePath, "utf8");
    return JSON.parse(fileContent);
  } catch (error) {
    console.error(`❌ Failed to load test file: ${filePath}`);
    console.error(`   Error: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Create SQS event structure
 */
function createSQSEvent(payload: any): any {
  return {
    Records: [
      {
        messageId: `test-message-${Date.now()}`,
        receiptHandle: `AQEBAi9rELbcjbdEInRwo/test-receipt-handle-${Date.now()}`,
        body: JSON.stringify({
          Type: "Notification",
          MessageId: `test-sns-message-${Date.now()}`,
          SequenceNumber: "10000000000000046001",
          TopicArn:
            "arn:aws:sns:eu-west-1:933713876074:DEV-GUS-SF-OUTBOUND-TOPIC.fifo",
          Message: JSON.stringify(payload),
          Timestamp: new Date().toISOString(),
          UnsubscribeURL:
            "https://sns.eu-west-1.amazonaws.com/?Action=Unsubscribe&test",
          MessageAttributes: {
            source: { Type: "String", Value: "GUS" },
          },
        }),
        attributes: {
          ApproximateReceiveCount: "1",
          SentTimestamp: Date.now().toString(),
          SequenceNumber: "18886402107438319617",
          MessageGroupId: `test-group-${Date.now()}`,
          SenderId: "AIDAWYJAWPFU7SUQGUJC6",
          MessageDeduplicationId: `test-dedup-${Date.now()}`,
          ApproximateFirstReceiveTimestamp: Date.now().toString(),
        },
        messageAttributes: {},
        md5OfBody: "test-md5-hash",
        eventSource: "aws:sqs",
        eventSourceARN:
          "arn:aws:sqs:eu-west-1:933713876074:DEV-ARD-GUS-SF-INTEGRATION-QUEUE.fifo",
        awsRegion: "eu-west-1",
      },
    ],
  };
}

async function runTest() {
  console.log("🧪 Dynamic Test - Outbound Event Integration");
  console.log("=".repeat(50));

  // Validate scenario
  if (!AVAILABLE_SCENARIOS.includes(scenario)) {
    console.error(`❌ Invalid scenario: ${scenario}`);
    console.error(`   Available scenarios: ${AVAILABLE_SCENARIOS.join(", ")}`);
    process.exit(1);
  }

  console.log(`\n📋 Testing Scenario: ${scenario}`);
  console.log(`🧪 Test Type: ${testType.toUpperCase()}`);

  // Load test payload
  const testPayload = loadTestPayload(scenario, testType);

  console.log("\n📄 Test Payload:");
  console.log(JSON.stringify(testPayload, null, 2));

  // Create SQS event
  const mockSQSEvent = createSQSEvent(testPayload);

  console.log("\n🏷️  Brand-Aware Logging:");
  const brand = testPayload.brand || "GUS";
  console.log(`- Brand: ${brand}`);
  console.log(
    `- Logs will be sent to: /aws/lambda/${brand.toLowerCase()}-gus-sf-integration-dev`
  );

  console.log("\n⚡ Processing event through LambdaHandler...");

  // Set up brand context for logging
  const mockEventRecord = {
    messageId: mockSQSEvent.Records[0].messageId,
    body: mockSQSEvent.Records[0].body,
    eventSourceARN: mockSQSEvent.Records[0].eventSourceARN,
  };

  // Extract brand context and set it
  const brandContext = logger.extractBrandContext(mockEventRecord);
  logger.setBrandContext(brandContext);

  console.log(`\n🏢 Detected Brand: ${brandContext.brand}`);
  console.log(
    `📊 Log Group: /aws/lambda/${brandContext.brand.toLowerCase()}-gus-sf-integration-dev`
  );

  try {
    const startTime = Date.now();
    const results = await processOutboundEvents(mockSQSEvent);
    const duration = Date.now() - startTime;

    console.log(`\n✅ Processing completed in ${duration}ms`);
    console.log("\n📊 Results:");

    results.forEach((result, index) => {
      console.log(`\nRecord ${index + 1}:`);
      console.log(`  Status: ${result.status ? "✅ SUCCESS" : "❌ FAILED"}`);
      console.log(`  Message ID: ${result.messageId}`);
      console.log(`  Brand: ${brandContext.brand}`);
      console.log(`  Scenario: ${scenario}`);

      if (result.error) {
        console.log(`  Error: ${result.error}`);
        if (result.missingFields && result.missingFields.length > 0) {
          console.log(`  Missing Fields: ${result.missingFields.join(", ")}`);
        }
      }

      if (result.data) {
        console.log(`  Data: ${JSON.stringify(result.data, null, 2)}`);
      }
    });

    // Test-specific insights
    if (testType === "invalid") {
      console.log("\n🔍 Validation Testing:");
      console.log(
        "- This test validates that the system properly rejects invalid data"
      );
      console.log(
        "- Check that validation errors are properly caught and logged"
      );
    } else {
      console.log("\n🎯 Positive Flow Testing:");
      console.log("- This test validates the happy path for the scenario");
      console.log("- Check that the event is processed successfully");
    }

    console.log("\n📝 CloudWatch Logging:");
    console.log(
      `- Check CloudWatch log group: /aws/lambda/${brandContext.brand.toLowerCase()}-gus-sf-integration-dev`
    );
    console.log("- Structured logs with brand context are being sent there");
    console.log("\n🎉 Test completed!");
  } catch (error) {
    console.error("\n💥 Test failed with error:");
    console.error(error);
  }
}

// Show usage if no arguments provided
if (process.argv.length < 3) {
  console.log("📖 Usage Examples:");
  console.log("");
  console.log("  Test positive flow:");
  console.log(
    "    ts-node --transpile-only src\\functions\\outbound-event-integration\\test\\simpleTest.ts ENROLMENT_STAGE"
  );
  console.log("");
  console.log("  Test validation failure:");
  console.log(
    "    ts-node --transpile-only src\\functions\\outbound-event-integration\\test\\simpleTest.ts ENROLMENT_STAGE invalid"
  );
  console.log("");
  console.log("  Available scenarios:");
  AVAILABLE_SCENARIOS.forEach((s) => console.log(`    - ${s}`));
  console.log("");
  console.log("  Test types: valid, invalid");
  console.log("");
}

// Run the test
runTest();
