/**
 * @fileoverview File Metadata Interface
 * 
 * This interface defines the structure for file metadata used across the document management system.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

/**
 * Supported file sources - extensible for future file sources
 */
export type FileSource = 
  | "AWS_S3" 
  | "HTTP_DOWNLOADABLE_LINK"
  | "FTP"
  | "SFTP"
  | "LOCAL_FILE"
  | "DATABASE_BLOB"
  | "EXTERNAL_API";

/**
 * Interface for file metadata
 */
export interface IFileMetadata {
  documentType: string;
  source: FileSource;
  filename: string;
  filePath: string;
  originalUrl?: string;
  
  // Additional metadata for future extensibility
  fileSize?: number;
  mimeType?: string;
  checksum?: string;
  metadata?: Record<string, any>;
}
