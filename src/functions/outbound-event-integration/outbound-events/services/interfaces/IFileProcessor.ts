/**
 * @fileoverview File Processor Interface
 * 
 * This interface defines the contract for file processing strategies.
 * Following the Strategy pattern to support different file sources and processing methods.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IFileMetadata } from './IFileMetadata';

/**
 * Context interface for file processing
 */
export interface IFileProcessingContext {
  bucketName: string;
  s3Service: any; // S3Service type
  documentType: string;
  applicationId: string;
  correlationId?: string;
}

/**
 * Result interface for file processing
 */
export interface IFileProcessingResult {
  s3Path: string;
  fullUrl: string;
  success: boolean;
  error?: string;
}

/**
 * Strategy interface for file processing
 * Each file source type should implement this interface
 */
export interface IFileProcessor {
  /**
   * Determines if this processor can handle the given file
   * @param file - File metadata to check
   * @returns True if processor can handle this file type
   */
  canProcess(file: IFileMetadata): boolean;

  /**
   * Process the file according to its source type
   * @param file - File metadata
   * @param context - Processing context with required services and data
   * @returns Promise with processing result
   */
  processFile(file: IFileMetadata, context: IFileProcessingContext): Promise<IFileProcessingResult>;

  /**
   * Get the processor type/name for logging and identification
   * @returns Processor type identifier
   */
  getProcessorType(): string;
}
