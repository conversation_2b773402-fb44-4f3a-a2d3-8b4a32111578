/**
 * @fileoverview Document Sync Interfaces
 * 
 * This interface defines the structure for document synchronization with GUS Salesforce.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

/**
 * Interface for document sync request to GUS SF
 */
export interface IDocumentSyncRequest {
  DocumentType__c: string;
  LetterType__c?: string;
  ApplicationId__c: string;
  Name: string;
  FilePath__c: string;
  Opportunity__c: string;
  FullUrl__c: string;
  OriginalValue__c: string;
  S3FileName__c: string;
  BucketName__c: string;
  DocumentSource__c: string;
  Status__c: string;
}

/**
 * Interface for document processing result
 */
export interface IDocumentProcessingResult {
  success: boolean;
  documentId?: string;
  s3Path?: string;
  error?: string;
}
