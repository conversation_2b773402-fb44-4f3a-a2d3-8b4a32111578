/**
 * @fileoverview GUS Salesforce Integration Service
 *
 * This service provides centralized access to GUS Salesforce operations for all event handlers.
 * It handles API calls, logging, error management, and provides a consistent interface
 * for fetching opportunity details and other GUS SF operations.
 *
 * Key features:
 * - Centralized GUS SF API access
 * - Comprehensive logging with CloudWatch integration
 * - Error handling and retry logic
 * - Brand-aware operations
 * - Industry-standard patterns
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { getData, postData } from "src/connectors/eip-connector";
import { IEventMessage, IEventLogger } from "../../shared/types/interfaces";
import { LOGGER_EVENTS, LOGGER_COMPONENTS } from "../../shared/config/config";
import { globalConfigContext } from "../../shared/services/globalConfigContext";

/**
 * Interface for GUS SF operation results
 */
export interface IGusSfOperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  correlationId: string;
}

/**
 * Interface for GUS SF operation options
 */
export interface IGusSfOperationOptions {
  correlationId: string;
  scenario: string;
  brand: string;
  applicationFormId: string;
  timeout?: number;
  retryAttempts?: number;
}

/**
 * Centralized service for GUS Salesforce operations
 */
export class GusSalesforceService {
  constructor() {
    // Service initialized
  }

  /**
   * Fetches opportunity details from GUS SF by application form ID
   *
   * @param applicationFormId - The application form ID to fetch opportunity for
   * @param options - Operation options including correlation ID, scenario, brand
   * @param logger - Event logger for logging operations
   * @returns Promise containing the opportunity details or error
   */
  async fetchOpportunityDetails(
    applicationFormId: string,
    options: IGusSfOperationOptions,
    logger: IEventLogger
  ): Promise<IGusSfOperationResult> {
    const { correlationId, scenario } = options;

    try {
      // Log operation initiated
      await logger.log(
        { applicationFormId },
        `GUS SF fetch opportunity initiated for application: ${applicationFormId}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      // Get brand-specific API key from global config context
      const apiKey = globalConfigContext.getApiKey();

      // Construct API endpoint
      const endpoint = `gus/opportunityId/${applicationFormId}?scenario=${scenario}`;

      // Make API call
      const opportunityData = await getData(endpoint, correlationId, apiKey);

      // Validate response
      if (!opportunityData?.Id) {
        throw new Error(
          `No opportunity found for application form ID: ${applicationFormId}`
        );
      }

      // Log operation completed
      await logger.log(
        { applicationFormId, opportunityId: opportunityData.Id },
        `GUS SF fetch opportunity completed for application: ${applicationFormId}`,
        LOGGER_EVENTS.OPERATION_COMPLETED,
        LOGGER_COMPONENTS.GUS_SALESFORCE
      );

      return {
        success: true,
        data: opportunityData,
        correlationId,
      };
    } catch (error) {
      // Log operation failed
      await logger.error(
        { applicationFormId },
        `GUS SF fetch opportunity failed for application: ${applicationFormId} - ${error.message}`,
        LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      return {
        success: false,
        error: error.message || "Unknown error occurred",
        correlationId,
      };
    }
  }

  /**
   * Updates opportunity details in GUS SF
   *
   * @param opportunityId - The opportunity ID to update
   * @param updateData - The data to update
   * @param options - Operation options
   * @param logger - Event logger for logging operations
   * @returns Promise containing the update result
   */
  async updateOpportunityDetails(
    opportunityId: string,
    updateData: any,
    options: IGusSfOperationOptions,
    logger: IEventLogger
  ): Promise<IGusSfOperationResult> {
    const { correlationId, scenario, applicationFormId } = options;

    try {

      // Get brand-specific API key from global config context
      const apiKey = globalConfigContext.getApiKey();

      // Construct API endpoint
      const endpoint = `gus/updateOpportunity/${opportunityId}?scenario=${scenario}`;

      // Make API call
      const updateResult = await postData(
        endpoint,
        updateData,
        correlationId,
        apiKey
      );

      // Log operation completed
      await logger.log(
        { applicationFormId, opportunityId },
        `GUS SF update opportunity completed for opportunity: ${opportunityId}`,
        LOGGER_EVENTS.OPERATION_COMPLETED,
        LOGGER_COMPONENTS.GUS_SALESFORCE
      );

      return {
        success: true,
        data: updateResult,
        correlationId,
      };
    } catch (error) {
      // Log operation failed
      await logger.error(
        { applicationFormId, opportunityId },
        `GUS SF update opportunity failed for opportunity: ${opportunityId} - ${error.message}`,
        LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      return {
        success: false,
        error: error.message || "Unknown error occurred",
        correlationId,
      };
    }
  }

  /**
   * Convenience method to fetch opportunity details from event message
   *
   * @param message - The event message containing application details
   * @param logger - Event logger for logging operations
   * @returns Promise containing the opportunity details
   */
  async fetchOpportunityDetailsFromMessage(
    message: IEventMessage,
    logger: IEventLogger
  ): Promise<IGusSfOperationResult> {
    const applicationFormId =
      message.payload?.gusApplicationId ||
      message.payload?.externalApplicationId;

    if (!applicationFormId) {
      throw new Error("No application form ID found in message payload");
    }

    const options: IGusSfOperationOptions = {
      correlationId: message.correlationId,
      scenario: message.scenario,
      brand: message.brand || globalConfigContext.getBrand(),
      applicationFormId: applicationFormId,
    };

    return this.fetchOpportunityDetails(applicationFormId, options, logger);
  }
}

/**
 * Singleton instance of the GUS Salesforce service
 */
export const gusSalesforceService = new GusSalesforceService();
