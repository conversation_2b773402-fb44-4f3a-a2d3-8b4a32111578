/**
 * @fileoverview HTTP Downloadable Link File Processor
 * 
 * This processor handles files that need to be downloaded from HTTP URLs
 * and then uploaded to S3. It implements the IFileProcessor interface.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IFileProcessor, IFileProcessingContext, IFileProcessingResult } from '../interfaces/IFileProcessor';
import { IFileMetadata } from '../interfaces/IFileMetadata';
import axios from 'axios';
import * as path from 'path';
import { logger } from '../../../shared/services/multiBrandCloudWatchLogger';

/**
 * HTTP Downloadable Link File Processor
 * Downloads files from HTTP URLs and uploads them to S3
 */
export class HttpFileProcessor implements IFileProcessor {
  
  /**
   * Check if this processor can handle the given file
   * @param file - File metadata to check
   * @returns True if file source is HTTP_DOWNLOADABLE_LINK
   */
  canProcess(file: IFileMetadata): boolean {
    return file.source === "HTTP_DOWNLOADABLE_LINK";
  }

  /**
   * Process HTTP file - download and upload to S3
   * @param file - File metadata
   * @param context - Processing context with S3 service and bucket info
   * @returns Processing result with S3 path after upload
   */
  async processFile(file: IFileMetadata, context: IFileProcessingContext): Promise<IFileProcessingResult> {
    logger.log(`Processing HTTP file: ${file.filename} from URL: ${file.filePath}`);
    
    try {
      // Download file from HTTP URL
      const response = await axios.get(file.filePath, {
        responseType: "arraybuffer",
        timeout: 30000, // 30 second timeout
      });

      // Generate structured S3 path
      const fileExtension = this.getFileExtension(file.filename, file.filePath);
      const cleanFilename = this.sanitizeFilename(file.filename);
      const s3Path = `${context.applicationId}/${context.documentType}/${cleanFilename}${fileExtension}`;

      // Upload to S3
      logger.log(`Uploading file to S3: ${s3Path}`);
      await context.s3Service.uploadFile(
        context.bucketName,
        s3Path,
        Buffer.from(response.data),
        process.env.S3_ROLE_ARN || "" // Add role ARN from environment
      );

      logger.log(`HTTP file downloaded and uploaded to S3: ${s3Path}`);
      return {
        s3Path,
        fullUrl: s3Path,
        success: true
      };
    } catch (error) {
      logger.error(`Error processing HTTP file: ${error.message}`);
      return {
        s3Path: '',
        fullUrl: '',
        success: false,
        error: `Failed to download and upload file: ${error.message}`
      };
    }
  }

  /**
   * Get processor type identifier
   * @returns Processor type
   */
  getProcessorType(): string {
    return "HTTP_PROCESSOR";
  }

  /**
   * Utility methods
   */
  private sanitizeFilename(filename: string): string {
    // Remove file extension and sanitize
    const name = path.parse(filename).name;
    return name.replace(/[^a-zA-Z0-9._-]/g, "_");
  }

  private getFileExtension(filename: string, url?: string): string {
    let extension = path.extname(filename);

    if (!extension && url) {
      extension = path.extname(new URL(url).pathname);
    }

    return extension || ".pdf"; // Default to PDF
  }
}
