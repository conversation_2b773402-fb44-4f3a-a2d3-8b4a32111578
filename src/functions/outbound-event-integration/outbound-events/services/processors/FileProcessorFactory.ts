/**
 * @fileoverview File Processor Factory
 * 
 * Factory class for creating and managing file processors.
 * This implements the Factory pattern to provide appropriate processors
 * based on file source types.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IFileProcessor } from '../interfaces/IFileProcessor';
import { IFileMetadata } from '../interfaces/IFileMetadata';
import { S3FileProcessor } from './S3FileProcessor';
import { HttpFileProcessor } from './HttpFileProcessor';

/**
 * Factory for creating file processors
 * Follows the Factory pattern and Open/Closed principle
 */
export class FileProcessorFactory {
  private static processors: IFileProcessor[] = [
    new S3FileProcessor(),
    new HttpFileProcessor(),
    // Future processors can be added here:
    // new FtpFileProcessor(),
    // new SftpFileProcessor(),
    // new LocalFileProcessor(),
    // new DatabaseBlobProcessor(),
    // new ExternalApiProcessor(),
  ];

  /**
   * Get appropriate processor for the given file
   * @param file - File metadata
   * @returns Processor that can handle the file
   * @throws Error if no processor found for the file type
   */
  static getProcessor(file: IFileMetadata): IFileProcessor {
    const processor = this.processors.find(p => p.canProcess(file));
    
    if (!processor) {
      throw new Error(`No processor found for file source: ${file.source}`);
    }

    return processor;
  }

  /**
   * Register a new processor
   * @param processor - New processor to register
   */
  static registerProcessor(processor: IFileProcessor): void {
    this.processors.push(processor);
  }

  /**
   * Get all registered processors
   * @returns Array of all processors
   */
  static getAllProcessors(): IFileProcessor[] {
    return [...this.processors];
  }

  /**
   * Get supported file sources
   * @returns Array of supported file source types
   */
  static getSupportedSources(): string[] {
    return this.processors.map(p => p.getProcessorType());
  }
}
