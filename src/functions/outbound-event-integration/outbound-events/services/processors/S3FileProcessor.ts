/**
 * @fileoverview AWS S3 File Processor
 * 
 * This processor handles files that are already stored in AWS S3.
 * It implements the IFileProcessor interface for S3 file sources.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IFileProcessor, IFileProcessingContext, IFileProcessingResult } from '../interfaces/IFileProcessor';
import { IFileMetadata } from '../interfaces/IFileMetadata';
import { logger } from '../../../shared/services/multiBrandCloudWatchLogger';

/**
 * AWS S3 File Processor
 * Handles files that are already stored in S3 - no additional processing needed
 */
export class S3FileProcessor implements IFileProcessor {
  
  /**
   * Check if this processor can handle the given file
   * @param file - File metadata to check
   * @returns True if file source is AWS_S3
   */
  canProcess(file: IFileMetadata): boolean {
    return file.source === "AWS_S3";
  }

  /**
   * Process S3 file - simply return the existing S3 path
   * @param file - File metadata
   * @param _context - Processing context (not used for S3 files)
   * @returns Processing result with S3 path
   */
  async processFile(file: IFileMetadata, _context: IFileProcessingContext): Promise<IFileProcessingResult> {
    logger.log(`Processing S3 file: ${file.filename} from path: ${file.filePath}`);
    
    try {
      // For S3 files, the filePath is already the S3 path
      // No additional processing needed
      return {
        s3Path: file.filePath,
        fullUrl: file.filePath,
        success: true
      };
    } catch (error) {
      logger.error(`Error processing S3 file: ${error.message}`);
      return {
        s3Path: '',
        fullUrl: '',
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get processor type identifier
   * @returns Processor type
   */
  getProcessorType(): string {
    return "S3_PROCESSOR";
  }
}
