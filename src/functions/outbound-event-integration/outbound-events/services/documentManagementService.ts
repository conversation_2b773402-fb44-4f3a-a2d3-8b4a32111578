/**
 * @fileoverview Centralized Document Management Service
 *
 * This service handles document processing for multiple handlers including offer, payment,
 * and visa handlers. It supports documents from different sources (AWS S3, HTTP downloadable links)
 * and provides a unified interface for document synchronization with GUS Salesforce.
 *
 * Key features:
 * - Support for AWS_S3 and HTTP_DOWNLOADABLE_LINK sources
 * - Automatic file download and S3 upload for HTTP sources
 * - Structured S3 path organization: pdf/opportunityId/applicationId/filename
 * - GUS Salesforce document synchronization
 * - Error handling and logging
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { S3Service } from "src/common/s3Service";
import { postData } from "src/connectors/eip-connector";
import { IFileMetadata } from "./interfaces/IFileMetadata";
import { IDocumentSyncRequest, IDocumentProcessingResult } from "./interfaces/IDocumentSync";
import { IFileProcessingContext } from "./interfaces/IFileProcessor";
import { FileProcessorFactory } from "./processors/FileProcessorFactory";

/**
 * Centralized document management service
 */
export class DocumentManagementService {
  private s3Service: S3Service;
  private bucketName: string;

  constructor() {
    this.s3Service = new S3Service();
    this.bucketName = process.env.REVIEW_CENTER_BUCKET_NAME || `reviewcenter-${process.env.stage === "prod" ? "" : "stage"}`;
  }

  /**
   * Process a file based on its source type using Strategy pattern
   * @param file - File metadata
   * @param documentType - Document type
   * @param applicationId - Application ID
   * @returns Processing result with S3 path
   */
  async processFile(
    file: IFileMetadata,
    documentType: string,
    applicationId: string
  ): Promise<{ s3Path: string; fullUrl: string }> {
    console.log(
      `Processing file: ${file.filename} from source: ${file.source}`
    );

    try {
      // Get appropriate processor using Factory pattern
      const processor = FileProcessorFactory.getProcessor(file);
      
      // Create processing context
      const context: IFileProcessingContext = {
        bucketName: this.bucketName,
        s3Service: this.s3Service,
        documentType,
        applicationId,
      };

      // Process file using the selected strategy
      const result = await processor.processFile(file, context);
      
      if (!result.success) {
        throw new Error(result.error || 'File processing failed');
      }

      return {
        s3Path: result.s3Path,
        fullUrl: result.fullUrl
      };
    } catch (error) {
      console.error(`Error processing file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Sync document to GUS Salesforce
   * @param documentData - Document sync request data
   * @param correlationId - Correlation ID for logging
   * @param apiKey - API key for GUS SF
   * @returns Sync result
   */
  async syncDocumentToGUS(
    documentData: IDocumentSyncRequest,
    correlationId: string,
    apiKey: string
  ): Promise<IDocumentProcessingResult> {
    try {
      console.log(`Syncing document to GUS SF: ${documentData.Name}`);

      const response = await postData(
        "gus/opportunityfile",
        documentData,
        correlationId,
        apiKey
      );

      return {
        success: true,
        documentId: response?.id || response?.Id,
        s3Path: documentData.S3FileName__c,
      };
    } catch (error) {
      console.error(`Error syncing document to GUS SF: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create document sync request for GUS SF
   * @param file - File metadata
   * @param s3Path - S3 path of the file
   * @param fullUrl - Full URL of the file
   * @param opportunityId - SF Opportunity ID
   * @param applicationId - Application ID
   * @param documentSource - Source system name
   * @returns Document sync request
   */
  createDocumentSyncRequest(
    file: IFileMetadata,
    s3Path: string,
    fullUrl: string,
    opportunityId: string,
    applicationId: string,
    documentSource: string
  ): IDocumentSyncRequest {
    return {
      DocumentType__c: file.documentType,
      LetterType__c: file.documentType, // Same as document type for offers
      ApplicationId__c: applicationId,
      Name: file.filename,
      FilePath__c: s3Path,
      Opportunity__c: opportunityId,
      FullUrl__c: fullUrl,
      OriginalValue__c: file.filename,
      S3FileName__c: s3Path,
      BucketName__c: this.bucketName,
      DocumentSource__c: documentSource,
      Status__c: "Accepted",
    };
  }

  /**
   * Process multiple files in parallel
   * @param files - Array of file metadata
   * @param opportunityId - SF Opportunity ID
   * @param applicationId - Application ID
   * @param correlationId - Correlation ID for logging
   * @param apiKey - API key for GUS SF
   * @param documentSource - Source system name
   * @returns Array of processing results
   */
  async processFiles(
    files: IFileMetadata[],
    opportunityId: string,
    applicationId: string,
    correlationId: string,
    apiKey: string,
    documentSource: string
  ): Promise<IDocumentProcessingResult[]> {
    console.log(
      `Processing ${files.length} files for opportunity: ${opportunityId}`
    );

    const results = await Promise.allSettled(
      files.map(async (file) => {
        try {
          // Process file (download if needed, get S3 path)
          const { s3Path, fullUrl } = await this.processFile(
            file,
            opportunityId,
            applicationId
          );

          // Create sync request
          const syncRequest = this.createDocumentSyncRequest(
            file,
            s3Path,
            fullUrl,
            opportunityId,
            applicationId,
            documentSource
          );

          // Sync to GUS SF
          const syncResult = await this.syncDocumentToGUS(
            syncRequest,
            correlationId,
            apiKey
          );

          return {
            ...syncResult,
            s3Path,
          };
        } catch (error) {
          console.error(
            `Error processing file ${file.filename}: ${error.message}`
          );
          return {
            success: false,
            error: error.message,
          };
        }
      })
    );

    // Convert PromiseSettledResult to IDocumentProcessingResult
    return results.map((result, index) => {
      if (result.status === "fulfilled") {
        return result.value;
      } else {
        return {
          success: false,
          error: `Failed to process file ${files[index]?.filename}: ${result.reason}`,
        };
      }
    });
  }
}

/**
 * Singleton instance for global use
 */
export const documentManagementService = new DocumentManagementService();
