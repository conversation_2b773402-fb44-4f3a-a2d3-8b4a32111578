/**
 * @fileoverview Centralized Acknowledgement Service
 * 
 * Production-ready service for sending acknowledgements to partner systems
 * after processing events. Supports both success and failure acknowledgements
 * with comprehensive error handling and proper OOP design.
 * 
 * Key features:
 * - Dynamic brand configuration loading
 * - Support for validation errors (no DynamoDB storage)
 * - Support for system errors (with DynamoDB storage)
 * - FIFO queue support with proper message grouping
 * - Flexible message formats (string, string[], object[])
 * - UUID generation for message group IDs when needed
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { sendToSQS } from "src/common/sqsService";
import { IBrandConfig } from "../../shared/services/brandConfigManager";
import { globalConfigContext } from "../../shared/services/globalConfigContext";
import { v4 as uuidv4 } from 'uuid';

/**
 * Acknowledgement payload structure
 */
export interface IAckPayload {
  correlationId: string;
  brand: string;
  scenario: string;
  ackFor: string;
  timestamp: string;
  status: 'success' | 'failure';
  message: string | string[] | object[];
  gusApplicationId: string;
  externalApplicationId?: string;
}

/**
 * Acknowledgement context for processing
 */
export interface IAckContext {
  correlationId: string;
  brand: string;
  scenario: string;
  gusApplicationId?: string;
  externalApplicationId?: string;
  originalMessage?: any;
}

/**
 * Error types for different acknowledgement scenarios
 */
export enum AckErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',     // No DynamoDB storage needed
  SYSTEM_ERROR = 'SYSTEM_ERROR',             // Store in DynamoDB
  PROCESSING_ERROR = 'PROCESSING_ERROR'      // Store in DynamoDB
}

/**
 * Abstract base class for acknowledgement strategies
 */
abstract class AckStrategy {
  /**
   * Send acknowledgement to partner system
   */
  abstract sendAcknowledgement(
    payload: IAckPayload, 
    brandConfig: IBrandConfig
  ): Promise<void>;

  /**
   * Determine if error should be stored in DynamoDB
   */
  abstract shouldStoreError(errorType: AckErrorType): boolean;

  /**
   * Send message to SQS
   */
  protected async sendToQueue(
    queueUrl: string,
    message: string,
    messageGroupId: string,
    deduplicationId: string
  ): Promise<void> {
    await sendToSQS(queueUrl, message, messageGroupId, deduplicationId);
  }
}

/**
 * Success acknowledgement strategy
 */
class SuccessAckStrategy extends AckStrategy {
  async sendAcknowledgement(payload: IAckPayload, brandConfig: IBrandConfig): Promise<void> {
    const messageGroupId = payload.gusApplicationId || uuidv4();
    
    console.log(`📤 Sending success acknowledgement for ${payload.scenario} to ${payload.brand}`);
    
    await this.sendToQueue(
      brandConfig.inboundQueueUrl,
      JSON.stringify(payload),
      messageGroupId,
      payload.correlationId // deduplication ID
    );
  }

  shouldStoreError(): boolean {
    return false; // Success doesn't store errors
  }
}

/**
 * Failure acknowledgement strategy
 */
class FailureAckStrategy extends AckStrategy {
  private errorType: AckErrorType;

  constructor(errorType: AckErrorType) {
    super();
    this.errorType = errorType;
  }

  async sendAcknowledgement(payload: IAckPayload, brandConfig: IBrandConfig): Promise<void> {
    const messageGroupId = payload.gusApplicationId || uuidv4();
    
    console.log(`❌ Sending failure acknowledgement for ${payload.scenario} to ${payload.brand} (${this.errorType})`);
    
    await this.sendToQueue(
      brandConfig.inboundQueueUrl,
      JSON.stringify(payload),
      messageGroupId,
      payload.correlationId // deduplication ID
    );
  }

  shouldStoreError(errorType: AckErrorType): boolean {
    // Only validation errors are not stored in DynamoDB
    return errorType !== AckErrorType.VALIDATION_ERROR;
  }
}

/**
 * Factory for creating acknowledgement strategies
 */
class AckStrategyFactory {
  static createStrategy(status: 'success' | 'failure', errorType?: AckErrorType): AckStrategy {
    if (status === 'success') {
      return new SuccessAckStrategy();
    } else {
      return new FailureAckStrategy(errorType || AckErrorType.SYSTEM_ERROR);
    }
  }
}

/**
 * Centralized Acknowledgement Service
 * 
 * Orchestrates sending acknowledgements to partner systems with proper
 * error handling, brand configuration, and storage decisions.
 */
export class AcknowledgementService {
  private brandConfigCache: Map<string, IBrandConfig> = new Map();

  /**
   * Send success acknowledgement
   */
  async sendSuccessAck(context: IAckContext, message: string = "Application updated successfully"): Promise<void> {
    const payload = this.buildAckPayload(context, 'success', message);
    const strategy = AckStrategyFactory.createStrategy('success');
    
    await this.processAcknowledgement(payload, strategy);
  }

  /**
   * Send validation error acknowledgement (no DynamoDB storage)
   */
  async sendValidationErrorAck(
    context: IAckContext, 
    validationErrors: string | string[] | object[]
  ): Promise<void> {
    const payload = this.buildAckPayload(context, 'failure', validationErrors);
    const strategy = AckStrategyFactory.createStrategy('failure', AckErrorType.VALIDATION_ERROR);
    
    console.log(`🔍 Validation error - not storing in DynamoDB for ${context.brand}:${context.scenario}`);
    await this.processAcknowledgement(payload, strategy);
  }

  /**
   * Send system error acknowledgement (with DynamoDB storage)
   */
  async sendSystemErrorAck(
    context: IAckContext, 
    error: Error | string,
    shouldStore: boolean = true
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    const payload = this.buildAckPayload(context, 'failure', errorMessage);
    const errorType = shouldStore ? AckErrorType.SYSTEM_ERROR : AckErrorType.VALIDATION_ERROR;
    const strategy = AckStrategyFactory.createStrategy('failure', errorType);
    
    if (shouldStore) {
      console.log(`💾 System error - will be stored in DynamoDB for retry: ${context.brand}:${context.scenario}`);
    } else {
      console.log(`🚫 System error - not storing in DynamoDB: ${context.brand}:${context.scenario}`);
    }
    
    await this.processAcknowledgement(payload, strategy);
  }

  /**
   * Send processing error acknowledgement (with DynamoDB storage)
   */
  async sendProcessingErrorAck(
    context: IAckContext, 
    error: Error | string
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    const payload = this.buildAckPayload(context, 'failure', errorMessage);
    const strategy = AckStrategyFactory.createStrategy('failure', AckErrorType.PROCESSING_ERROR);
    
    console.log(`⚠️ Processing error - will be stored in DynamoDB for retry: ${context.brand}:${context.scenario}`);
    await this.processAcknowledgement(payload, strategy);
  }

  /**
   * Check if error should be stored in DynamoDB based on type
   */
  shouldStoreInDynamoDB(errorType: AckErrorType): boolean {
    const strategy = AckStrategyFactory.createStrategy('failure', errorType);
    return strategy.shouldStoreError(errorType);
  }

  /**
   * Build acknowledgement payload
   */
  private buildAckPayload(
    context: IAckContext, 
    status: 'success' | 'failure', 
    message: string | string[] | object[]
  ): IAckPayload {
    return {
      correlationId: context.correlationId,
      brand: context.brand,
      scenario: "ACK",
      ackFor: context.scenario,
      timestamp: new Date().toISOString(),
      status,
      message,
      gusApplicationId: context.gusApplicationId || uuidv4(),
      externalApplicationId: context.externalApplicationId
    };
  }

  /**
   * Process acknowledgement with strategy pattern
   */
  private async processAcknowledgement(payload: IAckPayload, strategy: AckStrategy): Promise<void> {
    try {
      const brandConfig = await this.getBrandConfig(payload.brand);
      
      if (!brandConfig.inboundQueueUrl) {
        console.warn(`⚠️ No inbound queue URL configured for brand ${payload.brand} - skipping acknowledgement`);
        return;
      }

      await strategy.sendAcknowledgement(payload, brandConfig);
      
      console.log(`✅ Acknowledgement sent successfully for ${payload.brand}:${payload.ackFor} (${payload.status})`);
    } catch (error) {
      console.error(`❌ Failed to send acknowledgement for ${payload.brand}:${payload.ackFor}:`, error);
      throw error;
    }
  }

  /**
   * Get brand configuration with caching
   */
  private async getBrandConfig(brand: string): Promise<IBrandConfig> {
    if (this.brandConfigCache.has(brand)) {
      return this.brandConfigCache.get(brand)!;
    }

    const config = globalConfigContext.getBrandConfig();
    this.brandConfigCache.set(brand, config);
    return config;
  }

  /**
   * Clear brand config cache (useful for testing or config updates)
   */
  clearCache(): void {
    this.brandConfigCache.clear();
  }

  /**
   * Extract acknowledgement context from event message
   */
  static extractAckContext(eventMessage: any, brand: string): IAckContext {
    return {
      correlationId: eventMessage.correlationId || eventMessage.uuid || uuidv4(),
      brand: brand,
      scenario: eventMessage.scenario,
      gusApplicationId: eventMessage.payload?.gusApplicationId || '',
      externalApplicationId: eventMessage.payload?.externalApplicationId || '',
      originalMessage: eventMessage
    };
  }
}

// Singleton instance for global use
export const acknowledgementService = new AcknowledgementService();
