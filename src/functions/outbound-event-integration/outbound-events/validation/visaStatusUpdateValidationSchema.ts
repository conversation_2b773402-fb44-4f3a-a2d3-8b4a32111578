/**
 * @fileoverview Visa Status Update Scenario Validation Schema
 * 
 * This module defines the validation schema for visa status update events based on the 
 * field definitions provided. It specifies all required fields, their types, and 
 * validation rules for the VISA_STATUS_UPDATE scenario.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from '../core/scenarioValidator';

/**
 * Validation schema for visa status update events
 * Based on the field definitions provided in the image
 */
export const VisaStatusUpdateValidationSchema: IScenarioValidationSchema = {
  scenario: 'VISA_STATUS_UPDATE',
  description: 'Validation schema for visa status update events',
  rules: [
    {
      field: 'payload.visaType',
      type: 'string',
      required: true,
      description: 'Type of visa applied for',
      customValidator: (value: string) => {
        const validVisaTypes = ['STUDENT_VISA', 'WORK_VISA', 'TOURIST_VISA', 'BUSINESS_VISA', 'OTHER'];
        if (!validVisaTypes.includes(value)) {
          return {
            field: 'payload.visaType',
            message: `Visa type must be one of: ${validVisaTypes.join(', ')}`
          };
        }
        return null;
      }
    },
    {
      field: 'payload.visaStatus.code',
      type: 'string',
      required: true,
      description: 'Current status of visa application (code)',
      customValidator: (value: string) => {
        const validStatuses = ['PENDING', 'APPROVED', 'REJECTED', 'SUBMITTED', 'IN_REVIEW', 'CANCELLED'];
        if (!validStatuses.includes(value)) {
          return {
            field: 'payload.visaStatus.code',
            message: `Visa status code must be one of: ${validStatuses.join(', ')}`
          };
        }
        return null;
      }
    },
    {
      field: 'payload.visaStatus.text',
      type: 'string',
      required: false,
      description: 'Short visa status text'
    },
    {
      field: 'payload.visaStatus.description',
      type: 'string',
      required: false,
      description: 'Detailed visa status description'
    },
    {
      field: 'payload.visaStatus.visaDepositPaid',
      type: 'string',
      required: false,
      description: 'Visa deposit amount and currency'
    },
    {
      field: 'payload.visaStatus.issueDate',
      type: 'string',
      required: true,
      description: 'Date visa was issued',
      customValidator: (value: string) => {
        if (!value) return null;
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(value)) {
          return {
            field: 'payload.visaStatus.issueDate',
            message: 'Issue date must be in YYYY-MM-DD format'
          };
        }
        return null;
      }
    },
    {
      field: 'payload.visaStatus.expiryDate',
      type: 'string',
      required: true,
      description: 'Date visa expires',
      customValidator: (value: string) => {
        if (!value) return null;
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(value)) {
          return {
            field: 'payload.visaStatus.expiryDate',
            message: 'Expiry date must be in YYYY-MM-DD format'
          };
        }
        return null;
      }
    },
    {
      field: 'payload.files',
      type: 'custom',
      required: true,
      description: 'Files related to the visa process',
      customValidator: (value: any[]) => {
        if (!Array.isArray(value)) {
          return {
            field: 'payload.files',
            message: 'Files must be an array'
          };
        }
        
        // Validate each file in the array
        for (let i = 0; i < value.length; i++) {
          const file = value[i];
          
          // Check required file fields
          if (!file.documentType) {
            return {
              field: `payload.files[${i}].documentType`,
              message: 'Document type is required for each file'
            };
          }
          
          if (!file.source) {
            return {
              field: `payload.files[${i}].source`,
              message: 'Source is required for each file'
            };
          }
          
          if (!file.filename) {
            return {
              field: `payload.files[${i}].filename`,
              message: 'Filename is required for each file'
            };
          }
          
          if (!file.filePath) {
            return {
              field: `payload.files[${i}].filePath`,
              message: 'File path is required for each file'
            };
          }
          
          // Validate source type
          const validSources = ['AWS_S3', 'HTTP_DOWNLOADABLE_LINK'];
          if (!validSources.includes(file.source)) {
            return {
              field: `payload.files[${i}].source`,
              message: `File source must be one of: ${validSources.join(', ')}`
            };
          }
          
          // Validate document type for visa
          if (file.documentType !== 'Visa') {
            return {
              field: `payload.files[${i}].documentType`,
              message: `Document type for visa must be 'Visa'`
            };
          }
        }
        
        return null;
      }
    }
  ]
};

/**
 * Custom validation functions for visa-specific logic
 */
export class VisaValidationHelpers {
  /**
   * Validate that expiry date is after issue date
   */
  static validateDateOrder(issueDate: string, expiryDate: string): { field: string; message: string } | null {
    if (!issueDate || !expiryDate) return null;
    
    const issue = new Date(issueDate);
    const expiry = new Date(expiryDate);
    
    if (expiry <= issue) {
      return {
        field: 'payload.visaStatus.expiryDate',
        message: 'Visa expiry date must be after issue date'
      };
    }
    return null;
  }
  
  /**
   * Validate visa deposit format (amount + currency)
   */
  static validateVisaDeposit(deposit: string): boolean {
    if (!deposit) return true; // Optional field
    
    // Should be in format like "500USD", "1000GBP", etc.
    const depositRegex = /^\d+[A-Z]{3}$/;
    return depositRegex.test(deposit);
  }
  
  /**
   * Map visa status code to Salesforce status
   */
  static mapToSalesforceStatus(statusCode: string): string {
    const statusMapping: { [key: string]: string } = {
      'PENDING': 'Pending',
      'APPROVED': 'Approved',
      'REJECTED': 'Rejected',
      'SUBMITTED': 'Submitted',
      'IN_REVIEW': 'In Review',
      'CANCELLED': 'Cancelled'
    };
    
    return statusMapping[statusCode] || statusCode;
  }
}
