/**
 * @fileoverview Reject Document Scenario Validation Schema
 * 
 * This module defines the validation schema for reject document events based on the 
 * field definitions provided. It specifies all required fields, their types, and 
 * validation rules for the REJECT_DOCUMENT scenario.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from '../core/scenarioValidator';

/**
 * Validation schema for reject document events
 * Based on the field definitions provided in the image
 */
export const RejectDocumentValidationSchema: IScenarioValidationSchema = {
  scenario: 'REJECT_DOCUMENT',
  description: 'Validation schema for reject document events',
  rules: [
    {
      field: 'payload.files.documentType',
      type: 'string',
      required: true,
      description: 'Type of document being rejected',
      minLength: 1
    },
    {
      field: 'payload.files.filename',
      type: 'string',
      required: true,
      description: 'Name of the rejected file',
      minLength: 1
    },
    {
      field: 'payload.files.filePath',
      type: 'string',
      required: true,
      description: 'Path of the rejected file (S3 filename or filename for <PERSON>rden)',
      minLength: 1
    },
    {
      field: 'payload.files.status',
      type: 'string',
      required: false,
      description: 'Status of the document (should be "Rejected")',
      customValidator: (value: string) => {
        if (value && value !== 'Rejected') {
          return {
            field: 'payload.files.status',
            message: 'Document status must be "Rejected" for reject document scenario'
          };
        }
        return null;
      }
    },
    {
      field: 'payload.files.comment',
      type: 'string',
      required: true,
      description: 'Reason for document rejection',
      minLength: 1
    }
  ]
};

/**
 * Custom validation functions for reject document-specific logic
 */
export class RejectDocumentValidationHelpers {
  /**
   * Validate that comment is provided for rejection
   */
  static validateRejectionComment(comment: string): { field: string; message: string } | null {
    if (!comment || comment.trim() === '') {
      return {
        field: 'payload.files.comment',
        message: 'Rejection comment is required when rejecting a document'
      };
    }
    return null;
  }
  
  /**
   * Validate file path format based on brand
   */
  static validateFilePath(filePath: string, brand: string): boolean {
    if (brand === 'ARD') {
      // For Arden, filePath can be just filename
      return filePath && filePath.trim().length > 0;
    } else {
      // For other brands, expect S3 filename format
      return filePath && filePath.includes('/') && filePath.trim().length > 0;
    }
  }
  
  /**
   * Get appropriate field name for API call based on brand
   */
  static getFileIdentifierField(brand: string): string {
    return brand === 'ARD' ? 'fileName' : 's3FileName';
  }
}
