/**
 * @fileoverview Closed Lost Stage Validation Schema
 *
 * This module defines the validation schema for closed lost events.
 * It specifies all required fields, their types, and validation rules
 * for the CLOSED_LOST_STAGE scenario.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from "../core/scenarioValidator";

/**
 * Validation schema for closed lost stage events
 * Based on the requirements for application rejections/withdrawals
 */
export const ClosedLostValidationSchema: IScenarioValidationSchema = {
  scenario: "CLOSED_LOST_STAGE",
  description: "Validation schema for closed lost stage events",
  rules: [
    {
      field: "correlationId",
      type: "string",
      required: true,
      description: "Unique identifier for the closed lost event",
      minLength: 1,
      pattern:
        /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i,
    },
    {
      field: "scenario",
      type: "string",
      required: true,
      description: "Event scenario identifier",
      customValidator: (value: string) => {
        if (value !== "CLOSED_LOST_STAGE") {
          return {
            field: "scenario",
            message: "Scenario must be CLOSED_LOST_STAGE",
          };
        }
        return null;
      },
    },
    {
      field: "timestamp",
      type: "string",
      required: true,
      description: "ISO timestamp of the event",
      pattern: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/,
    },
    {
      field: "brand",
      type: "string",
      required: true,
      description: "Brand identifier for the application",
      minLength: 1,
    },
    {
      field: "payload.gusApplicationId",
      type: "string",
      required: true,
      description: "GUS application identifier",
      pattern:
        /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i,
    },
    {
      field: "payload.externalApplicationId",
      type: "string",
      required: true,
      description: "External system application identifier",
      minLength: 1,
    },
    {
      field: "payload.externalApplicationStatus",
      type: "string",
      required: true,
      description: "Status of the application in external system",
      customValidator: (value: string) => {
        const validStatuses = [
          "Rejected",
          "Withdrawn",
          "Deferred",
          "Cancelled",
        ];
        if (!validStatuses.includes(value)) {
          return {
            field: "payload.externalApplicationStatus",
            message: `Status must be one of: ${validStatuses.join(", ")}`,
          };
        }
        return null;
      },
    },
    {
      field: "payload.rejectionReason.code",
      type: "string",
      required: true,
      description: "Rejection reason code",
      customValidator: (value: string) => {
        const validCodes = [
          "DQ",
          "IF",
          "AI",
          "LM",
          "FI",
          "VI",
          "WD",
          "DF",
          "CN",
          "OT",
        ];
        if (!validCodes.includes(value)) {
          return {
            field: "payload.rejectionReason.code",
            message: `Rejection code must be one of: ${validCodes.join(", ")}`,
          };
        }
        return null;
      },
    },
    {
      field: "payload.rejectionReason.name",
      type: "string",
      required: true,
      description: "Human readable rejection reason name",
      minLength: 1,
    },
  ],
};
