/**
 * @fileoverview Enrollment Scenario Validation Schema
 * 
 * This module defines the validation schema for enrollment events based on the 
 * requirements table. It specifies all required fields, their types, and validation rules
 * for the ENROLMENT_STAGE scenario.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from '../core/scenarioValidator';
import { logger } from '../../shared/services/multiBrandCloudWatchLogger';

/**
 * Validation schema for enrollment events
 * Based on the requirements table provided
 */
export const EnrollmentValidationSchema: IScenarioValidationSchema = {
  scenario: 'ENROLMENT_STAGE',
  description: 'Validation schema for enrollment stage events',
  rules: [
    {
      field: 'correlationId',
      type: 'string',
      required: true,
      description: 'Unique identifier for the enrollment event',
      minLength: 1,
      pattern: /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i
    },
    {
      field: 'scenario',
      type: 'string',
      required: true,
      description: 'Type of event (ENROLMENT_STAGE)',
      customValidator: (value: string) => {
        if (value !== 'ENROLMENT_STAGE') {
          return {
            field: 'scenario',
            message: 'Scenario must be ENROLMENT_STAGE for enrollment events'
          };
        }
        return null;
      }
    },
    {
      field: 'timestamp',
      type: 'datetime',
      required: true,
      description: 'Time of enrollment processing (ISO 8601 format)'
    },
    {
      field: 'brand',
      type: 'string',
      required: true,
      description: 'Brand identifier for the application (e.g., ABC)',
      minLength: 2,
      maxLength: 10,
      pattern: /^[A-Z][A-Z0-9]*$/
    },
    {
      field: 'payload.externalApplicationId',
      type: 'string',
      required: true,
      description: 'External system application identifier',
      minLength: 1
    },
    {
      field: 'payload.gusApplicationId',
      type: 'string',
      required: true,
      description: 'Internal GUS application identifier',
      pattern: /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i
    },
    {
      field: 'payload.externalApplicationStatus',
      type: 'string',
      required: true,
      description: 'Current status in external system (Enrolled)',
      customValidator: (value: string) => {
        const validStatuses = ['Enrolled', 'Pending', 'Completed', 'Cancelled'];
        if (!validStatuses.includes(value)) {
          return {
            field: 'payload.externalApplicationStatus',
            message: `External application status must be one of: ${validStatuses.join(', ')}`
          };
        }
        return null;
      }
    }
  ]
};

/**
 * Custom validation functions for enrollment-specific logic
 */
export class EnrollmentValidationHelpers {
  /**
   * Validate that the enrollment timestamp is not in the future
   */
  static validateEnrollmentTimestamp(timestamp: string): boolean {
    const enrollmentDate = new Date(timestamp);
    const now = new Date();
    return enrollmentDate <= now;
  }

  /**
   * Validate that the application ID format matches expected pattern
   */
  static validateApplicationIdFormat(applicationId: string): boolean {
    // Should be numeric string for external system
    return /^\d+$/.test(applicationId);
  }

  /**
   * Validate enrollment status transition
   */
  static validateStatusTransition(currentStatus: string, newStatus: string): boolean {
    const validTransitions: { [key: string]: string[] } = {
      'Pending': ['Enrolled', 'Cancelled'],
      'Enrolled': ['Completed', 'Cancelled'],
      'Completed': [],
      'Cancelled': []
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }
}

/**
 * Register the enrollment validation schema
 */
export function registerEnrollmentValidation(): void {
  // This will be called during application startup to register the schema
  logger.log('Registering enrollment validation schema...');
}
