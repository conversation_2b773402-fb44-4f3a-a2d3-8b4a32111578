/**
 * @fileoverview Offer Scenario Validation Schema
 * 
 * This module defines the validation schema for offer events based on the 
 * field definitions provided. It specifies all required fields, their types, and 
 * validation rules for the OFFER_STAGE scenario.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from '../core/scenarioValidator';

/**
 * Validation schema for offer events
 * Based on the field definitions provided
 */
export const OfferValidationSchema: IScenarioValidationSchema = {
  scenario: 'OFFER_STAGE',
  description: 'Validation schema for offer stage events',
  rules: [
    {
      field: 'payload.externalApplicationStatus',
      type: 'string',
      required: true,
      description: 'Status of the application in the Institution system (Offer Made)',
      minLength: 1
    },
    {
      field: 'payload.offerType',
      type: 'string',
      required: true,
      description: 'Type of offer made to applicant (Conditional/Unconditional)',
      customValidator: (value: string) => {
        const validOfferTypes = ['Conditional', 'Unconditional'];
        if (!validOfferTypes.includes(value)) {
          return {
            field: 'payload.offerType',
            message: `Offer type must be one of: ${validOfferTypes.join(', ')}`
          };
        }
        return null;
      }
    },
    {
      field: 'payload.condition',
      type: 'string',
      required: false,
      description: 'Any condition set by the institution for conditional offers'
    },
    {
      field: 'payload.programDetails.code',
      type: 'string',
      required: false,
      description: 'Code of the Programme'
    },
    {
      field: 'payload.programDetails.name',
      type: 'string',
      required: false,
      description: 'Name of the Programme'
    },
    {
      field: 'payload.programDetails.startDate',
      type: 'string',
      required: false,
      description: 'StartDate of the Programme'
    },
    {
      field: 'payload.programDetails.campus',
      type: 'string',
      required: false,
      description: 'Campus selected by the Applicant for the programme'
    },
    {
      field: 'payload.financialDetails.tuitionFee',
      type: 'string',
      required: false,
      description: 'Tuitions fee for the Programme'
    },
    {
      field: 'payload.financialDetails.currency',
      type: 'string',
      required: false,
      description: 'Currency for the Tuition fee'
    },
    {
      field: 'payload.files',
      type: 'custom',
      required: true,
      description: 'Files related to the Programme offer process',
      customValidator: (value: any[]) => {
        if (!Array.isArray(value)) {
          return {
            field: 'payload.files',
            message: 'Files must be an array'
          };
        }
        
        // Validate each file in the array
        for (let i = 0; i < value.length; i++) {
          const file = value[i];
          
          // Check required file fields
          if (!file.documentType) {
            return {
              field: `payload.files[${i}].documentType`,
              message: 'Document type is required for each file'
            };
          }
          
          if (!file.source) {
            return {
              field: `payload.files[${i}].source`,
              message: 'Source is required for each file'
            };
          }
          
          if (!file.filename) {
            return {
              field: `payload.files[${i}].filename`,
              message: 'Filename is required for each file'
            };
          }
          
          if (!file.filePath) {
            return {
              field: `payload.files[${i}].filePath`,
              message: 'File path is required for each file'
            };
          }
          
          // Validate source type
          const validSources = ['AWS_S3', 'HTTP_DOWNLOADABLE_LINK'];
          if (!validSources.includes(file.source)) {
            return {
              field: `payload.files[${i}].source`,
              message: `File source must be one of: ${validSources.join(', ')}`
            };
          }
          
          // Validate document type for offer
          const validDocTypes = ['Offer letter', 'Conditional Offer Letter'];
          if (!validDocTypes.includes(file.documentType)) {
            return {
              field: `payload.files[${i}].documentType`,
              message: `Document type for offer must be one of: ${validDocTypes.join(', ')}`
            };
          }
        }
        
        return null;
      }
    }
  ]
};

/**
 * Custom validation functions for offer-specific logic
 */
export class OfferValidationHelpers {
  /**
   * Validate that conditional offers have a condition specified
   */
  static validateConditionalOffer(payload: any): { field: string; message: string } | null {
    if (payload.offerType === 'Conditional' && (!payload.condition || payload.condition.trim() === '')) {
      return {
        field: 'payload.condition',
        message: 'Condition is required for conditional offers'
      };
    }
    return null;
  }
  
  /**
   * Validate offer timestamp is not in the future
   */
  static validateOfferTimestamp(timestamp: string): boolean {
    const offerDate = new Date(timestamp);
    const now = new Date();
    return offerDate <= now;
  }
  
  /**
   * Validate program details consistency
   */
  static validateProgramDetails(programDetails: any): { field: string; message: string } | null {
    if (!programDetails) {
      return null; // Program details are optional
    }
    
    // If start date is provided, validate it's a valid date
    if (programDetails.startDate) {
      const startDate = new Date(programDetails.startDate);
      if (isNaN(startDate.getTime())) {
        return {
          field: 'payload.programDetails.startDate',
          message: 'Start date must be a valid date format'
        };
      }
    }
    
    return null;
  }
}
