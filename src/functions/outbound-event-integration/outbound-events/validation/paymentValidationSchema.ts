/**
 * @fileoverview Payment Scenario Validation Schema
 *
 * This module defines the validation schema for payment events based on the
 * field definitions provided. It specifies all required fields, their types, and
 * validation rules for the PAYMENT_RECEIVED scenario.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IScenarioValidationSchema } from "../core/scenarioValidator";

/**
 * Validation schema for payment events
 * Based on the field definitions provided in the image
 */
export const PaymentValidationSchema: IScenarioValidationSchema = {
  scenario: "PAYMENT_RECEIVED",
  description: "Validation schema for payment received events",
  rules: [
    {
      field: "payload.paymentDetails.amount",
      type: "number",
      required: true,
      description: "Payment amount",
    },
    {
      field: "payload.paymentDetails.currency",
      type: "string",
      required: true,
      description: "Payment currency code (e.g., GBP, USD)",
      minLength: 3,
      maxLength: 3,
    },
    {
      field: "payload.paymentDetails.type",
      type: "string",
      required: true,
      description: "Type of payment",
      customValidator: (value: string) => {
        const validPaymentTypes = [
          "Deposit",
          "Tuition Fee",
          "Application Fee",
          "Other",
        ];
        if (!validPaymentTypes.includes(value)) {
          return {
            field: "payload.paymentDetails.type",
            message: `Payment type must be one of: ${validPaymentTypes.join(
              ", "
            )}`,
          };
        }
        return null;
      },
    },
    {
      field: "payload.paymentDetails.status",
      type: "string",
      required: true,
      description: "Current status of payment",
      customValidator: (value: string) => {
        const validStatuses = [
          "Completed",
          "Pending",
          "Failed",
          "Cancelled",
          "Refunded",
        ];
        if (!validStatuses.includes(value)) {
          return {
            field: "payload.paymentDetails.status",
            message: `Payment status must be one of: ${validStatuses.join(
              ", "
            )}`,
          };
        }
        return null;
      },
    },
    {
      field: "payload.paymentDetails.reference",
      type: "string",
      required: false,
      description: "Payment reference number",
    },
    {
      field: "payload.paymentDetails.method",
      type: "string",
      required: false,
      description: "Payment method used",
      customValidator: (value: string) => {
        if (!value) return null; // Optional field
        const validMethods = [
          "Card",
          "Bank Transfer",
          "PayPal",
          "Check",
          "Cash",
          "Other",
        ];
        if (!validMethods.includes(value)) {
          return {
            field: "payload.paymentDetails.method",
            message: `Payment method must be one of: ${validMethods.join(
              ", "
            )}`,
          };
        }
        return null;
      },
    },
    {
      field: "payload.paymentDetails.date",
      type: "datetime",
      required: true,
      description: "Date payment was made",
    },
    /* Custom validation for files related to payment are removed 
    {
      field: 'payload.paymentDetails.files',
      type: 'custom',
      required: true,
      description: 'Files related to the payment process',
      customValidator: (value: any[]) => {
        if (!Array.isArray(value)) {
          return {
            field: 'payload.paymentDetails.files',
            message: 'Files must be an array'
          };
        }
        
        // Validate each file in the array
        for (let i = 0; i < value.length; i++) {
          const file = value[i];
          
          // Check required file fields
          if (!file.documentType) {
            return {
              field: `payload.paymentDetails.files[${i}].documentType`,
              message: 'Document type is required for each file'
            };
          }
          
          if (!file.source) {
            return {
              field: `payload.paymentDetails.files[${i}].source`,
              message: 'Source is required for each file'
            };
          }
          
          if (!file.filename) {
            return {
              field: `payload.paymentDetails.files[${i}].filename`,
              message: 'Filename is required for each file'
            };
          }
          
          if (!file.filePath) {
            return {
              field: `payload.paymentDetails.files[${i}].filePath`,
              message: 'File path is required for each file'
            };
          }
          
          // Validate source type
          const validSources = ['AWS_S3', 'HTTP_DOWNLOADABLE_LINK'];
          if (!validSources.includes(file.source)) {
            return {
              field: `payload.paymentDetails.files[${i}].source`,
              message: `File source must be one of: ${validSources.join(', ')}`
            };
          }
          
          // Validate document type for payments
          const validDocTypes = ['Letter of Acceptance', 'Conditional Letter of Acceptance', 'Payment Receipt', 'Invoice'];
          if (!validDocTypes.includes(file.documentType)) {
            return {
              field: `payload.paymentDetails.files[${i}].documentType`,
              message: `Document type for payment must be one of: ${validDocTypes.join(', ')}`
            };
          }
        }
        
        return null;
      }
    }
    */
  ],
};

/**
 * Custom validation functions for payment-specific logic
 */
export class PaymentValidationHelpers {
  /**
   * Validate payment amount is positive
   */
  static validatePaymentAmount(
    amount: number
  ): { field: string; message: string } | null {
    if (amount <= 0) {
      return {
        field: "payload.paymentDetails.amount",
        message: "Payment amount must be greater than 0",
      };
    }
    return null;
  }

  /**
   * Validate payment date is not in the future
   */
  static validatePaymentDate(dateString: string): boolean {
    const paymentDate = new Date(dateString);
    const now = new Date();
    return paymentDate <= now;
  }

  /**
   * Validate currency format (ISO 4217)
   */
  static validateCurrencyCode(currency: string): boolean {
    const validCurrencies = [
      "USD",
      "GBP",
      "EUR",
      "CAD",
      "AUD",
      "JPY",
      "CHF",
      "CNY",
      "INR",
    ];
    return validCurrencies.includes(currency.toUpperCase());
  }
}
