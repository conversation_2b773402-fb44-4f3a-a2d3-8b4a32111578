import { BaseGusSfHandler } from "./baseGusSfHandler";
import { RegisterEventHandler } from "../core/eventHandlerRegistry";
import { IEventMessage, IHandlerResponse } from "../../shared/types/interfaces";
import { LOGGER_EVENTS, LOGGER_COMPONENTS } from "../../shared/config/config";
import { scenarioValidator } from "../core/scenarioValidator";
import { OfferValidationSchema } from "../validation/offerValidationSchema";
import { documentManagementService } from "../services/documentManagementService";
import { globalConfigContext } from "../../shared/services/globalConfigContext";
import { IFileMetadata } from "../services/interfaces/IFileMetadata";

@RegisterEventHandler("OFFER_STAGE", "Handles offer stage events", "1.0.0")
export class OfferHandler extends BaseGusSfHandler {
  constructor() {
    super();
    // Register validation schema for this scenario
    scenarioValidator.registerSchema(OfferValidationSchema);
  }

  /**
   * Handles an offer message with validation and document processing
   * @param message the event message to be handled
   * @returns the result of the handle operation
   */
  async handle(message: IEventMessage): Promise<IHandlerResponse> {
    try {
      this.validateInput(message);

      // Perform scenario-specific validation
      const validationResult = scenarioValidator.validateScenario(
        message.scenario,
        message
      );
      if (!validationResult.isValid) {
        const validationErrors = validationResult.errors.map((error) => ({
          field: error.field,
          message: error.message,
        }));

        return {
          status: false,
          error: `Offer validation failed: ${validationResult.errors
            .map((e) => `${e.field}: ${e.message}`)
            .join(", ")}`,
          validationErrors,
        };
      }

      await this.logInfo(
        `Processing offer event for application: ${message.payload?.gusApplicationId}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      // Use the standard flow from BaseGusSfHandler
      const processResult = await this.processStandardFlow(message);

      // Process documents if files exist
      let documentResults = [];
      if (message.payload.files && message.payload.files.length > 0) {
        await this.logInfo(
          `Processing ${message.payload.files.length} offer documents`,
          LOGGER_EVENTS.DOCUMENT_ISSUED_INITIATED,
          LOGGER_COMPONENTS.GUS_EIP_SERVICE
        );

        documentResults = await this.processOfferDocuments(
          message.payload.files,
          processResult.opportunityId,
          message.payload.gusApplicationId,
          message.correlationId,
          message.brand
        );

        const failedDocuments = documentResults.filter(
          (result) => !result.success
        );
        if (failedDocuments.length > 0) {
          const errorMessages = failedDocuments
            .map((result) => result.error)
            .join("; ");

          await this.logError(
            `Some documents failed to process: ${errorMessages}`,
            LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED,
            LOGGER_COMPONENTS.GUS_EIP_SERVICE
          );
        }

        await this.logInfo(
          `Document processing completed: ${
            documentResults.filter((r) => r.success).length
          }/${documentResults.length} successful`,
          LOGGER_EVENTS.OPERATION_COMPLETED,
          LOGGER_COMPONENTS.GUS_EIP_SERVICE
        );
      }

      await this.logInfo(
        `Offer processing completed successfully for application: ${message.payload.gusApplicationId}`,
        LOGGER_EVENTS.OPERATION_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      return {
        status: true,
        data: {
          applicationFormId: message.payload.gusApplicationId,
          opportunityId: processResult.opportunityId,
          processedAt: new Date().toISOString(),
          scenario: message.scenario,
          documentsProcessed: documentResults.filter((r) => r.success).length,
          totalDocuments: documentResults.length,
          message: "Offer handler executed successfully",
        },
      };
    } catch (error) {
      await this.logError(
        `Error processing offer event: ${error.message}`,
        LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        error
      );

      return {
        status: false,
        error: error.message,
      };
    }
  }

  /**
   * Required implementation of abstract method from BaseGusSfHandler
   * Build opportunity update request based on payload
   */
  protected buildUpdatePayload(message: IEventMessage): Record<string, any> {
    const payload = message.payload;
    const updateRequest: Record<string, any> = {};

    // Static field for offer handler
    updateRequest.StageName = "Offer";

    // Map external application status to SF field
    if (payload?.externalApplicationStatus) {
      updateRequest.AdmissionsStage__c = payload.externalApplicationStatus;
    }

    // Map offer type with validation
    if (payload?.offerType) {
      updateRequest.OfferType__c = payload.offerType;
    }

    // Add condition for conditional offers
    if (payload?.offerType === "Conditional" && payload?.condition) {
      updateRequest.Admissions_Condition__c = payload.condition;
    }

    return updateRequest;
  }

  /**
   * Process offer documents using centralized document management service
   */
  private async processOfferDocuments(
    files: any[],
    opportunityId: string,
    applicationId: string,
    correlationId: string,
    brand: string
  ): Promise<any[]> {
    try {
      // Convert files to IFileMetadata format
      const fileMetadata: IFileMetadata[] = files.map((file) => ({
        documentType: file.documentType,
        source: file.source,
        filename: file.filename,
        filePath: file.filePath,
        originalUrl: file.source === "HTTP_DOWNLOADABLE_LINK" ? file.filePath : undefined,
      }));

      // Process each file based on its source type
      const results = [];
      
      for (const file of fileMetadata) {
        try {
          await this.logInfo(
            `Processing document: ${file.filename} from source: ${file.source}`,
            LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
            LOGGER_COMPONENTS.GUS_EIP_SERVICE
          );

          // Use document management service to process the file
          const { s3Path, fullUrl } = await documentManagementService.processFile(
            file,
            file.documentType,
            applicationId
          );

          // Create document sync request for GUS SF
          const syncRequest = documentManagementService.createDocumentSyncRequest(
            file,
            s3Path,
            fullUrl,
            opportunityId,
            applicationId,
            brand // Use brand as document source
          );

          // Sync document to GUS SF
          const apiKey = globalConfigContext.getApiKey();
          const syncResult = await documentManagementService.syncDocumentToGUS(
            syncRequest,
            correlationId,
            apiKey
          );

          if (syncResult.success) {
            await this.logInfo(
              `Document processed successfully: ${file.filename} -> ${s3Path}`,
              LOGGER_EVENTS.OPERATION_COMPLETED,
              LOGGER_COMPONENTS.GUS_EIP_SERVICE
            );

            results.push({
              success: true,
              documentId: syncResult.documentId,
              s3Path: syncResult.s3Path,
              filename: file.filename,
              documentType: file.documentType,
              source: file.source,
            });
          } else {
            await this.logError(
              `Document sync failed for ${file.filename}: ${syncResult.error}`,
              LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED,
              LOGGER_COMPONENTS.GUS_EIP_SERVICE
            );

            results.push({
              success: false,
              error: syncResult.error,
              filename: file.filename,
              documentType: file.documentType,
              source: file.source,
            });
          }
        } catch (error) {
          await this.logError(
            `Error processing document ${file.filename}: ${error.message}`,
            LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED,
            LOGGER_COMPONENTS.GUS_EIP_SERVICE,
            error
          );

          results.push({
            success: false,
            error: error.message,
            filename: file.filename,
            documentType: file.documentType,
            source: file.source,
          });
        }
      }

      await this.logInfo(
        `Document processing completed: ${results.filter(r => r.success).length}/${results.length} successful`,
        LOGGER_EVENTS.OPERATION_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE
      );

      return results;
    } catch (error) {
      throw new Error(`Failed to process offer documents: ${error.message}`);
    }
  }
}
