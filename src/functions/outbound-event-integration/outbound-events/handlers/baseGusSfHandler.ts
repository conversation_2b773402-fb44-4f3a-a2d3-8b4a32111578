/**
 * @fileoverview Base Handler with GUS SF Integration
 * 
 * This class extends the BaseEventHandler to provide centralized GUS Salesforce
 * operations for all event handlers. It provides common functionality for
 * fetching and updating opportunity details while maintaining proper logging
 * and error handling.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { BaseEventHandler } from "@functions/outbound-event-integration/shared/base/BaseClasses";
import { IEventMessage } from "@functions/outbound-event-integration/shared/types/interfaces";
import { gusSalesforceService, IGusSfOperationResult } from "../services/gusSalesforceService";
import { globalConfigContext } from "../../shared/services/globalConfigContext";

/**
 * Abstract base class for handlers that need GUS Salesforce integration
 */
export abstract class BaseGusSfHandler extends BaseEventHandler {
  
  /**
   * Fetches opportunity details for the given message
   * This method is available to all handlers that extend this class
   */
  protected async fetchOpportunityDetails(message: IEventMessage): Promise<IGusSfOperationResult> {
    // Ensure message has brand information
    const enrichedMessage = this.enrichMessageWithBrand(message);
    
    const result = await gusSalesforceService.fetchOpportunityDetailsFromMessage(enrichedMessage, this.logger);
    
    if (!result.success) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Updates opportunity details using the centralized service
   */
  protected async updateOpportunityDetails(
    opportunityId: string,
    updateData: any,
    message: IEventMessage
  ): Promise<IGusSfOperationResult> {
    // Ensure message has brand information
    const enrichedMessage = this.enrichMessageWithBrand(message);
    
    const result = await gusSalesforceService.updateOpportunityDetails(
      opportunityId,
      updateData,
      {
        correlationId: enrichedMessage.correlationId,
        scenario: enrichedMessage.scenario,
        brand: enrichedMessage.brand,
        applicationFormId: enrichedMessage.payload?.gusApplicationId
      },
      this.logger
    );

    if (!result.success) {
      throw new Error(result.error);
    }

    return result;
  }

  /**
   * Enriches message with brand information from global context if missing
   */
  protected enrichMessageWithBrand(message: IEventMessage): IEventMessage {
    if (!message.brand) {
      message.brand = globalConfigContext.getBrand();
    }
    return message;
  }

  /**
   * Helper method to safely extract application form ID from message
   */
  protected getApplicationFormId(message: IEventMessage): string {
    const applicationFormId = message.payload?.gusApplicationId;
    
    if (!applicationFormId) {
      throw new Error('No application form ID found in message payload');
    }
    
    return applicationFormId;
  }

  /**
   * Helper method to get current brand from global context
   */
  protected getCurrentBrand(): string {
    return globalConfigContext.getBrand();
  }

  /**
   * Helper method to get current API key from global context
   */
  protected getCurrentApiKey(): string {
    return globalConfigContext.getApiKey();
  }

  /**
   * Abstract method for handlers to build SF update payload
   * Each handler must implement this to define their SF mapping logic
   */
  protected abstract buildUpdatePayload(message: IEventMessage): Record<string, any>;

  /**
   * Template method for standard handler flow
   * Handlers can override specific steps while maintaining the overall flow
   */
  protected async processStandardFlow(message: IEventMessage): Promise<any> {
    // 1. Ensure message has brand information
    const enrichedMessage = this.enrichMessageWithBrand(message);

    // 2. Fetch opportunity details
    const opportunityResult = await this.fetchOpportunityDetails(enrichedMessage);

    // 3. Build the update payload using handler-specific logic
    const updatePayload = this.buildUpdatePayload(enrichedMessage);

    // 4. Skip update if no changes
    if (Object.keys(updatePayload).length === 0) {
      return {
        processed: true,
        skipped: true,
        reason: 'No data to update',
        opportunityId: opportunityResult.data?.Id
      };
    }

    // 5. Update opportunity
    const updateResult = await this.updateOpportunityDetails(
      opportunityResult.data.Id,
      updatePayload,
      enrichedMessage
    );

    // 6. Return success result
    return {
      processed: true,
      opportunityId: opportunityResult.data.Id,
      updatedFields: Object.keys(updatePayload),
      updateResult: updateResult.data,
      handlerName: this.constructor.name,
      brand: enrichedMessage.brand
    };
  }
}
