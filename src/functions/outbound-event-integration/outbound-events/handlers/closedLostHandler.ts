/**
 * @fileoverview Closed Lost Handler
 *
 * This module handles CLOSED_LOST_STAGE events for applications that have been
 * rejected, withdrawn, deferred, or cancelled. It updates the Salesforce
 * opportunity with the appropriate stage and loss reason.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import {
  LOGGER_EVENTS,
  LOGGER_COMPONENTS,
} from "@functions/outbound-event-integration/shared/config/config";
import {
  IEventMessage,
  IHandlerResponse,
  IValidationResult,
} from "@functions/outbound-event-integration/shared/types/interfaces";
import { RegisterEventHandler } from "../core/eventHandlerRegistry";
import { scenarioValidator } from "../core/scenarioValidator";
import { ClosedLostValidationSchema } from "../validation/closedLostValidationSchema";
import { BaseGusSfHandler } from "./baseGusSfHandler";

@RegisterEventHandler(
  "CLOSED_LOST_STAGE",
  "Handles closed lost stage events for rejected/withdrawn applications",
  "1.0.0"
)
export class ClosedLostHandler extends BaseGusSfHandler {
  constructor() {
    super();
    // Register the closed lost validation schema
    scenarioValidator.registerSchema(ClosedLostValidationSchema);
  }

  /**
   * Handles a closed lost message
   * @param message the event message to be handled
   * @returns the result of the handle operation
   */
  async handle(message: IEventMessage): Promise<IHandlerResponse> {
    try {
      // 1. Basic input validation
      this.validateInput(message);

      // 2. Scenario-specific validation
      const scenarioValidationResult = await this.validateScenario(message);
      if (!scenarioValidationResult.isValid) {
        return this.createValidationErrorResponse(scenarioValidationResult);
      }

      await this.logInfo(
        `Processing closed lost event for application: ${message.payload?.gusApplicationId}`,
        LOGGER_EVENTS.SYNC_IN_GUS_SF_INITIATED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      // 3. Use the standard processing flow
      const syncResult = await this.processStandardFlow(message);

      await this.logInfo(
        "Closed lost event processed successfully",
        LOGGER_EVENTS.OPERATION_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { scenario: message.scenario, correlationId: message.correlationId }
      );

      return {
        status: true,
        data: syncResult,
      };
    } catch (error) {
      await this.logError(
        `Closed lost processing failed: ${error.message}`,
        LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED, // Using closest available event for failure
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { error: error.message, stack: error.stack }
      );

      return {
        status: false,
        error: error.message,
      };
    }
  }

  /**
   * Validates the message against closed lost-specific schema
   */
  private async validateScenario(
    message: IEventMessage
  ): Promise<IValidationResult> {
    const validationResult = scenarioValidator.validateScenario(
      "CLOSED_LOST_STAGE",
      message
    );

    if (!validationResult.isValid) {
      await this.logError(
        `Closed lost scenario validation failed: ${validationResult.errors
          .map((e) => `${e.field}: ${e.message}`)
          .join(", ")}`,
        LOGGER_EVENTS.VALIDATION_FAILED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        {
          validationErrors: validationResult.errors,
          scenario: message.scenario,
        }
      );
    }

    return validationResult;
  }

  /**
   * Creates a validation error response
   */
  private createValidationErrorResponse(
    validationResult: IValidationResult
  ): IHandlerResponse {
    const errorMessage = `Closed lost validation failed: ${validationResult.errors
      .map((e) => `${e.field}: ${e.message}`)
      .join(", ")}`;

    return {
      status: false,
      error: errorMessage,
      validationErrors: validationResult.errors,
    };
  }

  /**
   * Builds the closed lost update payload from the event message
   * Override the base class method to provide closed lost-specific SF mapping
   */
  protected buildUpdatePayload(message: IEventMessage): Record<string, any> {
    const payload: Record<string, any> = {};

    // Set stage name to Closed Lost
    payload["StageName"] = "Closed Lost";

    // Map rejection reason name to loss reason
    if (message.payload?.rejectionReason?.name) {
      payload["Loss_Reason__c"] = message.payload.rejectionReason.name;
    }

    // Set admissions condition based on external application status
    if (message.payload?.externalApplicationStatus) {
      payload["AdmissionsStage__c"] =
        message.payload.externalApplicationStatus;
    }

    // Add application ID reference
    if (message.payload?.externalApplicationId) {
      payload["ApplicId__c"] = message.payload.externalApplicationId;
    }

    return payload;
  }
}
