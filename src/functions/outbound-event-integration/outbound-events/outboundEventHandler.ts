/**
 * @fileoverview Lambda Handler for Outbound Event Processing
 *
 * This module serves as the AWS Lambda entry point for processing outbound events
 * from external systems to target integrations. It orchestrates the event processing
 * workflow using brand-specific configuration and handles the Lambda runtime concerns.
 *
 * Responsibilities:
 * - AWS Lambda runtime integration
 * - SQS event parsing and validation
 * - Brand context extraction and configuration loading
 * - Orchestration of domain event processing
 * - Error handling and result aggregation
 * - CloudWatch logging and monitoring
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { OutboundEventIntegrationService } from "./outboundEventService";
import { globalConfigContext } from "../shared/services/globalConfigContext";
import { logger } from "../shared/services/multiBrandCloudWatchLogger";
import type { SQSRecord, ProcessingResult } from "../shared/types/interfaces";

/**
 * AWS Lambda handler for processing outbound events from external systems.
 *
 * This function serves as the main entry point for SQS-triggered event processing.
 * It receives a batch of SQS records and orchestrates their processing through the
 * domain layer with brand-specific configuration.
 *
 * Processing flow:
 * 1. Receives SQS event batch from AWS Lambda runtime
 * 2. Initializes global configuration context (single DB call)
 * 3. Processes each record using cached configuration
 * 4. Aggregates results and handles failures
 * 5. Returns processing results for Lambda runtime
 *
 * @param {Object} event - The SQS event containing records to process
 * @param {SQSRecord[]} event.Records - Array of SQS records to process
 * @returns {Promise<ProcessingResult[]>} Array of processing results for each record
 */
export const handler = async (event: {
  Records: SQSRecord[];
}): Promise<ProcessingResult[]> => {
  logger.info("Lambda handler invoked for outbound event processing");
  logger.info(`Processing ${event.Records.length} records`);

  const results: ProcessingResult[] = [];

  // Initialize global configuration context once per Lambda execution
  if (event.Records.length > 0) {
    try {
      // Parse first record to get brand context for initialization
      const firstRecord = event.Records[0];
      const messageBody = JSON.parse(firstRecord.body);
      const message = JSON.parse(messageBody.Message) || messageBody.Message
      // Initialize global config context (single DB call)
      await globalConfigContext.initialize(message);

      logger.info(
        `Global configuration initialized for brand: ${globalConfigContext.getBrand()}`
      );
    } catch (error) {
      logger.error(
        "Failed to initialize global configuration context:",
        error
      );

      // Return error for all records if initialization fails
      return event.Records.map((record) => ({
        messageId: record.messageId,
        status: false,
        error: "Failed to initialize global configuration context",
        data: null,
        missingFields: [],
      }));
    }
  }

  // Process each record with cached configuration
  for (const record of event.Records) {
    try {
      // Extract brand context and set up logging
      const brandContext = logger.extractBrandContext(record);
      logger.setBrandContext(brandContext);

      await logger.logEventStart(record, brandContext);

      // Use cached brand configuration instead of loading from DB
      const brandConfig = globalConfigContext.getBrandConfig();

      // Create brand-specific service instance using cached config
      const integrationService = new OutboundEventIntegrationService({
        failedRecordsTableName: brandConfig.failedRecordsTableName,
        outboundTopicArn: brandConfig.outboundTopicArn,
        integrationPrefix: brandConfig.integrationPrefix,
        retryConfig: {
          maxRetries: 3,
          retryDelayMs: 1000,
          exponentialBackoff: true,
        },
      });

      // Parse and enrich message with brand information
      const messageBody = JSON.parse(record.body);
      const enrichedMessage =
        globalConfigContext.enrichMessageWithBrand(messageBody);

      // Process the single record
      const result = await integrationService.processRecord({
        ...record,
        body: JSON.stringify(enrichedMessage),
      });

      // Log the result
      await logger.logEventResult(record, result);

      results.push(result);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error(`Failed to process record ${record.messageId}:`, error);

      // Create error result
      const errorResult: ProcessingResult = {
        messageId: record.messageId,
        status: false,
        error: errorMessage,
        data: null,
        missingFields: [],
      };

      results.push(errorResult);
    }
  }

  // Log summary
  const successCount = results.filter((r) => r.status).length;
  const failureCount = results.length - successCount;

  logger.info(
    `Processing completed: ${successCount} successful, ${failureCount} failed`
  );

  return results;
};

// Legacy export for backward compatibility
export const processOutboundEvents = handler;
