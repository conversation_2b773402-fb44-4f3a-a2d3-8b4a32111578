import { logger } from '../../shared/services/multiBrandCloudWatchLogger';
import { IEventHandlerRegistry, IEventHandler } from '../../shared/types/interfaces';

interface HandlerMetadata {
  scenario: string;
  handler: new () => IEventHandler;
  description?: string;
  version?: string;
}

export class EventHandlerRegistry implements IEventHandlerRegistry {
  private static instance: EventHandlerRegistry;
  private handlers: Map<string, IEventHandler> = new Map();
  private handlerMetadata: Map<string, HandlerMetadata> = new Map();

  private constructor() {
    logger.log('Event Handler Registry initialized');
  }

  public static getInstance(): EventHandlerRegistry {
    if (!EventHandlerRegistry.instance) {
      EventHandlerRegistry.instance = new EventHandlerRegistry();
    }
    return EventHandlerRegistry.instance;
  }

  public registerHandler(scenario: string, handler: IEventHandler): void {
    if (!scenario || !handler) {
      throw new Error('Scenario and handler are required for registration');
    }

    this.handlers.set(scenario, handler);
    
    logger.log(`Handler registered successfully. Total handlers: ${this.handlers.size}`);
  }

  public registerHandlerClass(metadata: HandlerMetadata): void {
    
    this.handlerMetadata.set(metadata.scenario, metadata);
    
    // Create instance and register
    const handlerInstance = new metadata.handler();
    this.registerHandler(metadata.scenario, handlerInstance);
  }

  public getHandler(scenario: string): IEventHandler | null {
    if (!scenario) {
      logger.warn('Scenario is required to get handler');
      return null;
    }

    const handler = this.handlers.get(scenario);
    
    if (!handler) {
      logger.warn(`No handler found for scenario: ${scenario}`);
      logger.log('Available scenarios:', Array.from(this.handlers.keys()));
      return null;
    }

    logger.log(`Handler found for scenario: ${scenario}`);
    return handler;
  }

  public hasHandler(scenario: string): boolean {
    return this.handlers.has(scenario);
  }

  public getRegisteredScenarios(): string[] {
    return Array.from(this.handlers.keys());
  }

  public getHandlerCount(): number {
    return this.handlers.size;
  }

  public removeHandler(scenario: string): boolean {
    logger.log(`Removing handler for scenario: ${scenario}`);
    const removed = this.handlers.delete(scenario);
    this.handlerMetadata.delete(scenario);
    
    if (removed) {
      logger.log(`Handler removed successfully. Remaining handlers: ${this.handlers.size}`);
    } else {
      logger.warn(`No handler found to remove for scenario: ${scenario}`);
    }
    
    return removed;
  }

  public clear(): void {
    logger.log('Clearing all handlers from registry');
    this.handlers.clear();
    this.handlerMetadata.clear();
  }

  public getRegistryInfo(): any {
    return {
      totalHandlers: this.handlers.size,
      scenarios: Array.from(this.handlers.keys()),
      metadata: Array.from(this.handlerMetadata.entries()).map(([scenario, meta]) => ({
        scenario,
        description: meta.description,
        version: meta.version
      }))
    };
  }
}

// Decorator for registering handlers
export function RegisterEventHandler(scenario: string, description?: string, version?: string) {
  return function <T extends new () => IEventHandler>(constructor: T) {
    const registry = EventHandlerRegistry.getInstance();
    
    registry.registerHandlerClass({
      scenario,
      handler: constructor,
      description,
      version
    });
    
    logger.log(`Handler class registered via decorator: ${scenario}`);
    
    return constructor;
  };
}

// Singleton instance getter
export const getEventHandlerRegistry = (): EventHandlerRegistry => {
  return EventHandlerRegistry.getInstance();
};
