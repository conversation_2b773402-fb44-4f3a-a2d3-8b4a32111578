import { BaseEventProcessor } from '../../shared/base/BaseClasses';
import { 
  IEventRecord, 
  IProcessingResult, 
  IEventMessage, 
  IEventConfig,
  IEventContext,
  IEventValidator,
  IEventHandlerRegistry
} from '../../shared/types/interfaces';
import { EventMessageValidator } from './eventMessageValidator';
import { EventContextExtractor } from './eventContextExtractor';
import { getEventHandlerRegistry } from './eventHandlerRegistry';
import { LOGGER_EVENTS, LOGGER_COMPONENTS, DEFAULT_CONFIG } from '../../shared/config/config';
import { checkApplicationIdExist } from 'src/common/checkFailedRecords';
import { storeFailedRecordsQueue } from 'src/common/storeFailedRecords';
import { acknowledgementService, AcknowledgementService } from '../services/acknowledgementService';

/**
 * Main event processor responsible for orchestrating the entire event processing pipeline.
 * This class handles the core logic for processing incoming events including validation,
 * context extraction, handler execution, and error management.
 * 
 * Key responsibilities:
 * - Parse and validate incoming event records
 * - Extract event context and create appropriate loggers
 * - Route events to registered handlers
 * - Handle errors and store failed records for retry
 * - Manage the complete event processing lifecycle
 */

export class EventProcessor extends BaseEventProcessor {
  protected validator: IEventValidator;
  protected handlerRegistry: IEventHandlerRegistry;
  protected contextExtractor: typeof EventContextExtractor;
  private readonly config: IEventConfig;

  constructor(config: IEventConfig = DEFAULT_CONFIG) {
    super();
    this.config = config;
    this.validator = new EventMessageValidator();
    this.handlerRegistry = getEventHandlerRegistry();
    this.contextExtractor = EventContextExtractor;
  }

  public async processRecord(record: IEventRecord): Promise<IProcessingResult> {
    const startTime = Date.now();

    try {
      // 1. Parse and extract context
      const message = this.parseRecord(record);
      const context = this.contextExtractor.extractContext(message, record);

      // 2. Log event initiated
      await this.logEventInitiated(context, record);

      // 3. Validate message
      const validationResult = this.validator.validate(message);
      if (!validationResult.isValid) {
        return await this.handleValidationError(validationResult, message, context);
      }

      // 4. Check for previous failures
      await this.validateNoPreviousFailuresExist(record, context, message);

      // 5. Process with appropriate handler
      const handlerResult = await this.processWithHandler(message, context);

      // 6. Handle validation errors from handler
      if (!handlerResult.status && handlerResult.validationErrors) {
        return this.createValidationErrorResult(handlerResult, record.messageId);
      }

      // 7. Log success and return result
      await context.logger.log(
        message,
        'Event processed successfully',
        LOGGER_EVENTS.OPERATION_COMPLETED,
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { 
          processingTimeMs: Date.now() - startTime,
          handlerResult: handlerResult.status 
        }
      );

      // Send success acknowledgement
      try {
        const ackContext = AcknowledgementService.extractAckContext(message, context.brand);
        await acknowledgementService.sendSuccessAck(ackContext);
      } catch (ackError) {
        // Don't fail the processing due to acknowledgement errors
      }

      return this.createSuccessResult(handlerResult, record.messageId);

    } catch (error) {
      return await this.handleProcessingError(error, record, Date.now() - startTime);
    }
  }

  /**
   * Logs the event initiation with relevant context information.
   * This creates an audit trail for tracking event processing.
   * 
   * @param context - Event context containing logger and metadata
   * @param record - Original event record for reference
   */
  private async logEventInitiated(context: IEventContext, record: IEventRecord): Promise<void> {
    await context.logger.log(
      record,
      'Event processing initiated',
      LOGGER_EVENTS.EVENT_INITIATED,
      LOGGER_COMPONENTS.GUS_EIP_SERVICE,
      {
        messageId: record.messageId,
        messageGroupId: record.messageGroupId,
        scenario: context.scenario
      }
    );
  }

  /**
   * Handles validation errors by logging and creating appropriate error response.
   * Provides detailed information about which fields failed validation.
   * 
   * @param validationResult - Result of message validation
   * @param message - Original event message
   * @param context - Event context for logging
   * @returns Error result with validation details
   */
  private async handleValidationError(
    validationResult: any,
    message: IEventMessage,
    context: IEventContext
  ): Promise<IProcessingResult> {
    const errorMessage = `Validation failed: ${validationResult.errors.map((e: any) => `${e.field}: ${e.message}`).join(', ')}`;

    await context.logger.error(
      message,
      errorMessage,
      LOGGER_EVENTS.VALIDATION_FAILED,
      LOGGER_COMPONENTS.GUS_EIP_SERVICE,
      { 
        validationErrors: validationResult.errors,
        scenario: message.scenario
      }
    );

    // Send validation error acknowledgement (no DynamoDB storage)
    try {
      const ackContext = AcknowledgementService.extractAckContext(message, context.brand);
      await acknowledgementService.sendValidationErrorAck(ackContext, validationResult.errors);
    } catch (ackError) {
      // Don't fail the processing due to acknowledgement errors
    }

    return {
      status: false,
      error: errorMessage,
      missingFields: validationResult.errors.map((err: any) => err.field),
      messageId: message.eventId,
    };
  }

  /**
   * Validates that no previous failures exist for the given application
   * 
   * This method checks if there are any existing failed records for the same 
   * application to prevent duplicate processing and maintain data integrity.
   * Prevents processing of subsequent messages if earlier ones failed.
   * This maintains message order integrity for FIFO queues.
   * 
   * @param record - Current event record
   * @param context - Event context
   * @param message - Parsed event message
   * @throws Error if previous failures are detected
   */
  private async validateNoPreviousFailuresExist(
    record: IEventRecord,
    context: IEventContext,
    message: IEventMessage
  ): Promise<void> {
    const existingFailedRecords = await checkApplicationIdExist(
      record,
      context.applicationFormId,
      this.config.failedRecordsTableName,
      this.config.integrationPrefix
    );

    if (existingFailedRecords !== 'No messages to process') {
      const errorMessage = `Previous failure detected for messageGroupId: ${record.messageGroupId}. Cannot proceed with processing to avoid duplicate operations.`;
      
      await context.logger.error(
        message,
        errorMessage,
        LOGGER_EVENTS.VALIDATION_FAILED, // Using closest available event
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { 
          messageGroupId: record.messageGroupId,
          applicationFormId: context.applicationFormId,
          existingFailedRecords
        }
      );

      throw new Error(errorMessage);
    }
  }

  /**
   * Processes the event with the appropriate handler.
   * Finds the correct handler based on event scenario and executes it.
   * 
   * @param message - Event message to process
   * @param context - Event context with logger
   * @returns Handler execution result
   * @throws Error if no handler found or handler execution fails
   */
  private async processWithHandler(
    message: IEventMessage,
    context: IEventContext
  ): Promise<any> {
    const handler = this.handlerRegistry.getHandler(message.scenario);

    if (!handler) {
      const errorMessage = `No handler found for scenario: ${message.scenario}`;
      const availableScenarios = this.handlerRegistry.getRegisteredScenarios();

      await context.logger.error(
        message,
        errorMessage,
        LOGGER_EVENTS.VALIDATION_FAILED, // Using closest available event
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { 
          requestedScenario: message.scenario,
          availableScenarios
        }
      );

      throw new Error(errorMessage);
    }

    // Set logger for the handler
    handler.setLogger(context.logger);

    // Execute handler
    const result = await handler.handle(message);

    return result;
  }

  /**
   * Handles processing errors by logging and storing failed records.
   * Attempts to extract context for proper error logging even when processing fails.
   * Stores failed records for retry processing.
   * 
   * @param error - The error that occurred during processing
   * @param record - Original event record
   * @param processingTimeMs - Time taken before error occurred
   * @returns Error result with failure details
   */
  private async handleProcessingError(
    error: any,
    record: IEventRecord,
    processingTimeMs: number
  ): Promise<IProcessingResult> {
    let applicationFormId = 'unknown';
    let context: IEventContext | null = null;

    try {
      const message = this.parseRecord(record);
      context = this.contextExtractor.extractContext(message, record);
      applicationFormId = context.applicationFormId;

      // Log error with context
      await context.logger.error(
        record,
        error.message || error,
        LOGGER_EVENTS.SALESFORCE_REQUEST_COMPLETED, // Using closest available event for processing failure
        LOGGER_COMPONENTS.GUS_EIP_SERVICE,
        { 
          error: error.message,
          stack: error.stack,
          processingTimeMs
        }
      );
    } catch (parseError) {
      // Failed to parse record for error handling
    }

    // Store failed record for retry
    try {
      await storeFailedRecordsQueue(
        applicationFormId,
        this.config.failedRecordsTableName,
        this.config.integrationPrefix,
        record
      );
    } catch (storeError) {
      // Failed to store failed record
    }

    // Send system error acknowledgement (with DynamoDB storage)
    if (context) {
      try {
        const message = this.parseRecord(record);
        const ackContext = AcknowledgementService.extractAckContext(message, context.brand);
        await acknowledgementService.sendSystemErrorAck(ackContext, error.message || 'Unknown system error');
      } catch (ackError) {
        // Don't fail the processing due to acknowledgement errors
      }
    }

    return this.createErrorResult(error.message || 'Unknown error', record.messageId);
  }

  /**
   * Parses an SQS record to extract the event message.
   * Handles the nested JSON structure typically found in SQS/SNS messages.
   * 
   * @param record - The SQS event record to parse
   * @returns The parsed event message
   * @throws Error if the record cannot be parsed
   */
  protected parseRecord(record: IEventRecord): IEventMessage {
    try {
      // Parse the outer SQS record body
      const eventBody = JSON.parse(record.body);
      
      // Parse the inner SNS message if it exists
      const platformEventMessage = typeof eventBody.Message === 'string' 
        ? JSON.parse(eventBody.Message) 
        : eventBody.Message || eventBody;
      
      // Add the record's messageId as eventId if not present
      if (!platformEventMessage.eventId) {
        platformEventMessage.eventId = record.messageId;
      }
      
      return platformEventMessage;
    } catch (error) {
      const parseError = `Failed to parse event record: ${error.message}`;
      throw new Error(parseError);
    }
  }

  /**
   * Creates a success result object for a processed event.
   * 
   * @param handlerResult - Result from the event handler
   * @param messageId - ID of the processed message
   * @returns Formatted success result
   */
  protected createSuccessResult(handlerResult: any, messageId: string): IProcessingResult {
    return {
      status: true,
      messageId: messageId,
      data: handlerResult.data || {},
    };
  }

  /**
   * Creates an error result object for a failed event.
   * 
   * @param errorMessage - Description of the error
   * @param messageId - ID of the failed message
   * @returns Formatted error result
   */
  protected createErrorResult(errorMessage: string, messageId: string): IProcessingResult {
    return {
      status: false,
      error: errorMessage,
      messageId: messageId,
    };
  }

  /**
   * Creates a validation error result from handler validation failures.
   * 
   * @param handlerResult - Result from the event handler containing validation errors
   * @param messageId - ID of the message that failed validation
   * @returns Formatted validation error result
   */
  protected createValidationErrorResult(handlerResult: any, messageId: string): IProcessingResult {
    const validationErrors = handlerResult.validationErrors || [];
    const missingFields = validationErrors.map((error: any) => error.field);
    
    return {
      status: false,
      error: handlerResult.error || 'Handler validation failed',
      messageId: messageId,
      missingFields: missingFields,
      validationErrors: validationErrors
    };
  }

  /**
   * Processes a single event record through the complete pipeline.
   * This is the main entry point for event processing.
   * 
   * @param record - The event record to process
   * @returns Processing result indicating success or failure
   */
  /**
   * Gets a copy of the current configuration.
   * Prevents external modification of internal config.
   * 
   * @returns Copy of the current event configuration
   */
  public getConfig(): IEventConfig {
    return { ...this.config };
  }

  /**
   * Gets the handler registry for inspection or testing.
   * Allows external access to registered handlers and scenarios.
   * 
   * @returns The current handler registry instance
   */
  public getHandlerRegistry(): IEventHandlerRegistry {
    return this.handlerRegistry;
  }

  /**
   * Gets the message validator for inspection or testing.
   * Allows external access to validation logic.
   * 
   * @returns The current message validator instance
   */
  public getValidator(): IEventValidator {
    return this.validator;
  }
}
