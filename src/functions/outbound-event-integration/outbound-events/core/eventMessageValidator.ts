import { BaseEventValidator } from '../../shared/base/BaseClasses';
import { IEventMessage, IValidationResult, IValidationError } from '../../shared/types/interfaces';
import { logger } from '../../shared/services/multiBrandCloudWatchLogger';

export class EventMessageValidator extends BaseEventValidator {
  private readonly requiredFields: string[] = ['scenario', 'brand', 'payload'];
  private readonly payloadRequiredFields: string[] = ['gusApplicationId'];

  public validate(message: IEventMessage): IValidationResult {
    logger.log('Validating event message:', {
      scenario: message.scenario,
      uuid: message.uuid,
      hasPayload: !!message.payload
    });

    const errors: IValidationError[] = [];

    // Validate message exists
    if (!message) {
      errors.push({
        field: 'message',
        message: 'Event message is required'
      });
      return this.createValidationResult(false, errors);
    }

    // Validate required fields
    this.validateRequiredFields(message, errors);
    
    // Validate payload if present
    if (message.payload) {
      this.validatePayloadFields(message.payload, errors);
    }

    // Validate scenario format
    this.validateScenarioFormat(message.scenario, errors);

    // Validate brand format
    this.validateBrandFormat(message.brand, errors);

    // Validate gusApplicationId format (UUID)
    if (message.payload?.gusApplicationId) {
      this.validateGusApplicationIdFormat(message.payload.gusApplicationId, errors);
    }

    const isValid = errors.length === 0;
    
    logger.log('Validation result:', {
      isValid,
      errorCount: errors.length,
      errors: errors.map(e => `${e.field}: ${e.message}`)
    });

    return this.createValidationResult(isValid, errors);
  }

  private validateRequiredFields(message: IEventMessage, errors: IValidationError[]): void {
    this.requiredFields.forEach(field => {
      const error = this.validateRequiredField(message[field], field);
      if (error) {
        errors.push(error);
      }
    });
  }

  private validatePayloadFields(payload: any, errors: IValidationError[]): void {
    if (typeof payload !== 'object') {
      errors.push({
        field: 'payload',
        message: 'Payload must be an object'
      });
      return;
    }

    this.payloadRequiredFields.forEach(field => {
      const error = this.validateRequiredField(payload[field], `payload.${field}`);
      if (error) {
        errors.push(error);
      }
    });
  }

  private validateScenarioFormat(scenario: string, errors: IValidationError[]): void {
    if (scenario && typeof scenario !== 'string') {
      errors.push({
        field: 'scenario',
        message: 'Scenario must be a string'
      });
      return;
    }

    if (scenario && scenario.length < 3) {
      errors.push({
        field: 'scenario',
        message: 'Scenario must be at least 3 characters long'
      });
    }

    if (scenario && !/^[A-Z_][A-Z0-9_]*$/.test(scenario)) {
      errors.push({
        field: 'scenario',
        message: 'Scenario must contain only uppercase letters, numbers, and underscores'
      });
    }
  }

  private validateBrandFormat(brand: string, errors: IValidationError[]): void {
    if (brand && typeof brand !== 'string') {
      errors.push({
        field: 'brand',
        message: 'Brand must be a string'
      });
      return;
    }

    if (brand && brand.length < 2) {
      errors.push({
        field: 'brand',
        message: 'Brand must be at least 2 characters long'
      });
    }

    if (brand && !/^[A-Z][A-Z0-9]*$/.test(brand)) {
      errors.push({
        field: 'brand',
        message: 'Brand must start with uppercase letter and contain only uppercase letters and numbers'
      });
    }
  }

  private validateGusApplicationIdFormat(gusApplicationId: string, errors: IValidationError[]): void {
    if (typeof gusApplicationId !== 'string') {
      errors.push({
        field: 'payload.gusApplicationId',
        message: 'GUS Application ID must be a string'
      });
      return;
    }

    if (gusApplicationId.length === 0) {
      errors.push({
        field: 'payload.gusApplicationId',
        message: 'GUS Application ID cannot be empty'
      });
      return;
    }

    // UUID v4 format validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(gusApplicationId)) {
      errors.push({
        field: 'payload.gusApplicationId',
        message: 'GUS Application ID must be a valid UUID v4 format'
      });
    }
  }

  public getRequiredFields(): string[] {
    return [...this.requiredFields];
  }

  public getPayloadRequiredFields(): string[] {
    return [...this.payloadRequiredFields];
  }
}
