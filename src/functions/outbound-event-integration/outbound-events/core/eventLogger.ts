import { IEventLogger } from '../../shared/types/interfaces';
import { LoggerEnum } from '@gus-eip/loggers';
import { LoggerService } from 'src/common/cloudwatchService';
import { logger } from '../../shared/services/multiBrandCloudWatchLogger';

export class EventLogger implements IEventLogger {
  private readonly loggerEnum: LoggerEnum;
  private readonly cloudWatchLogger: LoggerService;
  private readonly correlationId: string;
  private readonly applicationFormId: string;
  private readonly brand: string;
  private readonly scenario: string;

  constructor(
    correlationId: string,
    applicationFormId: string,
    brand: string = 'DEFAULT',
    scenario: string = 'UNKNOWN',
    loggerEnum?: LoggerEnum,
    cloudWatchLogger?: LoggerService
  ) {
    this.correlationId = correlationId;
    this.applicationFormId = applicationFormId;
    this.brand = brand;
    this.scenario = scenario;
    this.loggerEnum = loggerEnum || new LoggerEnum();
    this.cloudWatchLogger = cloudWatchLogger || new LoggerService();
  }

  async log(
    record: any,
    message: string,
    event: string,
    component: string,
    metadata?: any
  ): Promise<void> {
    try {
      // Console logging for development
      logger.log(`[${event}] ${message}`, {
        correlationId: this.correlationId,
        applicationFormId: this.applicationFormId,
        component,
        metadata
      });

      // CloudWatch logging for production
      await this.cloudWatchLogger.log(
        this.correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        component,
        event,
        this.scenario,
        record,
        metadata || {},
        message,
        this.brand,
        this.applicationFormId,
        'Application_Form_Id__c',
        this.applicationFormId,
        undefined,
        undefined,
        '',
        this.applicationFormId,
        undefined
      );
    } catch (error) {
      logger.error('Failed to log message:', error);
      logger.log(`[FALLBACK-${event}] ${message}`, {
        correlationId: this.correlationId,
        applicationFormId: this.applicationFormId,
        component,
        metadata,
        loggingError: error.message
      });
    }
  }

  async error(
    record: any,
    message: string,
    event: string,
    component?: string,
    metadata?: any
  ): Promise<void> {
    try {
      // Console error logging
      logger.error(`[ERROR-${event}] ${message}`, {
        correlationId: this.correlationId,
        applicationFormId: this.applicationFormId,
        component: component || 'UNKNOWN',
        metadata,
        stack: metadata?.stack
      });

      // CloudWatch error logging
      await this.cloudWatchLogger.log(
        this.correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        component || this.loggerEnum.Component.GUS_SALESFORCE,
        event,
        this.scenario,
        record,
        {
          ...metadata,
          errorType: 'PROCESSING_ERROR',
          severity: 'ERROR'
        },
        message,
        this.brand,
        this.applicationFormId,
        'Application_Form_Id__c',
        this.applicationFormId,
        undefined,
        undefined,
        '',
        this.applicationFormId,
        undefined
      );
    } catch (error) {
      logger.error('Failed to log error:', error);
      logger.error(`[FALLBACK-ERROR-${event}] ${message}`, {
        correlationId: this.correlationId,
        applicationFormId: this.applicationFormId,
        component: component || 'UNKNOWN',
        metadata,
        loggingError: error.message
      });
    }
  }

  public getCorrelationId(): string {
    return this.correlationId;
  }

  public getApplicationFormId(): string {
    return this.applicationFormId;
  }

  /**
   * Returns the brand of the event context.
   * @returns {string} The brand of the event context.
   */
  public getBrand(): string {
    return this.brand;
  }

  public getScenario(): string {
    return this.scenario;
  }
}
