import { logger } from "../../shared/services/multiBrandCloudWatchLogger";
import { IEventMessage, IEventRecord, IEventContext } from "../../shared/types/interfaces";
import { EventLogger } from "./eventLogger";

/**
 * Utility class for extracting event context from incoming messages.
 * This class is responsible for creating the appropriate logger and
 * extracting relevant metadata from event messages and records.
 * 
 * Key responsibilities:
 * - Extract application form ID from various message fields
 * - Determine brand information from message or environment
 * - Generate correlation IDs for tracking
 * - Create configured event loggers
 */
export class EventContextExtractor {
  /**
   * Extracts complete event context from message and record.
   * This is the main entry point for context creation.
   * 
   * @param message - Parsed event message
   * @param record - Original SQS record
   * @returns Complete event context with logger
   */
  public static extractContext(
    message: IEventMessage,
    record: IEventRecord
  ): IEventContext {
    const correlationId =
      message.eventId || record.messageId || this.generateCorrelationId();
    const applicationFormId = this.extractApplicationFormId(message);
    const brand = this.extractBrand(message);
    const scenario = message.scenario || "UNKNOWN_SCENARIO";

    const logger = new EventLogger(
      correlationId,
      applicationFormId,
      brand,
      scenario
    );

    return {
      applicationFormId,
      correlationId,
      brand,
      scenario,
      logger,
    };
  }

  /**
   * Extracts the application form ID from various possible locations in the message.
   * Tries multiple fields to ensure compatibility with different message formats.
   * 
   * @param message - Event message to extract from
   * @returns Application form ID or fallback value
   */
  private static extractApplicationFormId(message: IEventMessage): string {
    return (
      message.payload?.gusApplicationId ||
      "UNKNOWN_APP_ID"
    );
  }

  /**
   * Extracts brand information from message or environment.
   * Checks multiple sources to determine the appropriate brand.
   * 
   * @param message - Event message to extract from
   * @returns Brand identifier or default value
   */
  private static extractBrand(message: IEventMessage): string {
    // Extract brand from message or use environment variable
    return (
      message.brand ||
      message.payload?.brand ||
      "DEFAULT"
    );
  }

  /**
   * Generates a unique correlation ID for tracking.
   * Used when no correlation ID is provided in the message.
   * 
   * @returns Unique correlation identifier
   */
  private static generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Logs context information for debugging and monitoring.
   * Provides visibility into extracted context values.
   * 
   * @param context - Event context to log
   */
  public static logContextInfo(context: IEventContext): void {
    logger.log('Event context extracted:', {
      correlationId: context.correlationId,
      applicationFormId: context.applicationFormId,
      brand: context.brand,
      scenario: context.scenario
    });
  }
}
