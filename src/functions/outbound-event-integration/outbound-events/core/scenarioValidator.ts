/**
 * @fileoverview Scenario-Specific Validation System
 * 
 * This module provides a dynamic validation system that can be injected into event handlers
 * to perform scenario-specific validation based on predefined rules. Each scenario can have
 * its own validation schema with required fields, types, and custom validation logic.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { IValidationError, IValidationResult } from '../../shared/types/interfaces';
import { logger } from '../../shared/services/multiBrandCloudWatchLogger';

/**
 * Interface defining the structure of a validation rule
 */
export interface IValidationRule {
  field: string;
  type: 'string' | 'number' | 'boolean' | 'datetime' | 'custom';
  required: boolean;
  description?: string;
  customValidator?: (value: any) => IValidationError | null;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
}

/**
 * Interface defining a scenario validation schema
 */
export interface IScenarioValidationSchema {
  scenario: string;
  description: string;
  rules: IValidationRule[];
}

/**
 * Dynamic scenario validator that can validate messages based on scenario-specific rules
 */
export class ScenarioValidator {
  private validationSchemas: Map<string, IScenarioValidationSchema> = new Map();

  /**
   * Register a validation schema for a specific scenario
   */
  public registerSchema(schema: IScenarioValidationSchema): void {
    this.validationSchemas.set(schema.scenario, schema);
    logger.log(`Registered validation schema for scenario: ${schema.scenario}`);
  }

  /**
   * Validate a message against its scenario-specific schema
   */
  public validateScenario(scenario: string, message: any): IValidationResult {
    const schema = this.validationSchemas.get(scenario);
    
    if (!schema) {
      logger.warn(`No validation schema found for scenario: ${scenario}`);
      return {
        isValid: true,
        errors: []
      };
    }

    logger.log(`Validating message against schema for scenario: ${scenario}`);
    
    const errors: IValidationError[] = [];

    // Validate each rule in the schema
    for (const rule of schema.rules) {
      const error = this.validateRule(rule, message);
      if (error) {
        errors.push(error);
      }
    }

    const isValid = errors.length === 0;
    
    logger.log(`Scenario validation result for ${scenario}:`, {
      isValid,
      errorCount: errors.length,
      errors: errors.map(e => `${e.field}: ${e.message}`)
    });

    return {
      isValid,
      errors
    };
  }

  /**
   * Validate a single rule against the message
   */
  private validateRule(rule: IValidationRule, message: any): IValidationError | null {
    const value = this.getNestedValue(message, rule.field);

    // Check required field
    if (rule.required && (value === undefined || value === null || value === '')) {
      return {
        field: rule.field,
        message: `${rule.field} is required`
      };
    }

    // Skip validation if field is not required and not present
    if (!rule.required && (value === undefined || value === null)) {
      return null;
    }

    // Type validation
    const typeError = this.validateType(rule, value);
    if (typeError) {
      return typeError;
    }

    // Length validation for strings
    if (rule.type === 'string' && typeof value === 'string') {
      const lengthError = this.validateLength(rule, value);
      if (lengthError) {
        return lengthError;
      }
    }

    // Pattern validation
    if (rule.pattern && typeof value === 'string') {
      const patternError = this.validatePattern(rule, value);
      if (patternError) {
        return patternError;
      }
    }

    // Custom validation
    if (rule.customValidator) {
      const customError = rule.customValidator(value);
      if (customError) {
        return customError;
      }
    }

    return null;
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Validate field type
   */
  private validateType(rule: IValidationRule, value: any): IValidationError | null {
    switch (rule.type) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            field: rule.field,
            message: `${rule.field} must be a string`
          };
        }
        break;
      case 'number':
        if (typeof value !== 'number' && !Number.isFinite(Number(value))) {
          return {
            field: rule.field,
            message: `${rule.field} must be a number`
          };
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          return {
            field: rule.field,
            message: `${rule.field} must be a boolean`
          };
        }
        break;
      case 'datetime':
        if (typeof value !== 'string' || isNaN(Date.parse(value))) {
          return {
            field: rule.field,
            message: `${rule.field} must be a valid datetime in ISO 8601 format`
          };
        }
        break;
    }
    return null;
  }

  /**
   * Validate string length
   */
  private validateLength(rule: IValidationRule, value: string): IValidationError | null {
    if (rule.minLength && value.length < rule.minLength) {
      return {
        field: rule.field,
        message: `${rule.field} must be at least ${rule.minLength} characters long`
      };
    }
    if (rule.maxLength && value.length > rule.maxLength) {
      return {
        field: rule.field,
        message: `${rule.field} must be at most ${rule.maxLength} characters long`
      };
    }
    return null;
  }

  /**
   * Validate pattern
   */
  private validatePattern(rule: IValidationRule, value: string): IValidationError | null {
    if (rule.pattern && !rule.pattern.test(value)) {
      return {
        field: rule.field,
        message: `${rule.field} does not match the required pattern`
      };
    }
    return null;
  }

  /**
   * Get all registered schemas
   */
  public getRegisteredSchemas(): string[] {
    return Array.from(this.validationSchemas.keys());
  }

  /**
   * Get schema for a specific scenario
   */
  public getSchema(scenario: string): IScenarioValidationSchema | undefined {
    return this.validationSchemas.get(scenario);
  }
}

/**
 * Singleton instance of the scenario validator
 */
export const scenarioValidator = new ScenarioValidator();
