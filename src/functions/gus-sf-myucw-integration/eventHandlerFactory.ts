import { NewApplicationHandler } from "./eventHandlers/newApplicationHandler";
import { WithdrawApplicationHandler } from "./eventHandlers/withdrawApplicationHandler";
import { NewDocumentSyncHandler } from "./eventHandlers/newDocumentSyncHandler";
import { UpdateDocumentHandler } from './eventHandlers/updateDocumentHandler';
import { ClarificationSubmissionHandler } from './eventHandlers/clarificationSubmissionHandler';
export class EventHandlerFactory {
  static getHandler(scenario): EventHandler {
    switch (scenario) {
      case "GUS_NEW_APPLICATION":
        return new NewApplicationHandler();
      case "GUS_WITHDRAW_APPLICATION":
        return new WithdrawApplicationHandler();
      case "GUS_NEW_DOCUMENT":
        return new NewDocumentSyncHandler();
      case "GUS_DOCUMENT_UPDATE":
        return new UpdateDocumentHandler();
      case "GUS_CLARIFICATION_SUBMITTED":
        return new ClarificationSubmissionHandler();
      default:
        console.log("No handler found for event type: " + scenario);
        break;
    }
  }
}