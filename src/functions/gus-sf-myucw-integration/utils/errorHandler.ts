/**
 * Utility class for handling ignorable errors in GUS-SF-MyUCW integration handlers
 */
export class GUSErrorHandler {
  /**
   * Configuration mapping GUS scenarios to their ignorable error patterns
   */
  private static readonly IGNORABLE_ERRORS: Record<string, string[]> = {
    GUS_WITHDRAW_APPLICATION: [
      "Cannot withdraw post offer"
    ],
    GUS_CLARIFICATION_SUBMITTED: [
      "former_status_permission_denied",
      '"response_code": "3101"',
      "status_not_changed",
      '"response_code": "3004"'
    ],
    GUS_DOCUMENT_UPDATE: [
      "No required documents found for"
    ]
  };

  /**
   * Check if an error should be ignored for a specific GUS scenario
   * @param error - The error object or string
   * @param scenario - The GUS scenario (e.g., "GUS_WITHDRAW_APPLICATION", "GUS_CLARIFICATION_SUBMITTED", "GUS_DOCUMENT_UPDATE")
   * @returns boolean - true if the error should be ignored
   */
  public static isIgnorableError(error: any, scenario: string): boolean {
    const errorMessage = this.extractErrorMessage(error);
    const ignorablePatterns = this.IGNORABLE_ERRORS[scenario];
    
    if (!ignorablePatterns) {
      return false;
    }

    return ignorablePatterns.some(pattern => 
      errorMessage.includes(pattern)
    );
  }

  /**
   * Extract error message from various error formats
   * @param error - The error object, string, or other format
   * @returns string - The extracted error message
   */
  private static extractErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.response?.data) {
      return typeof error.response.data === 'string' 
        ? error.response.data 
        : JSON.stringify(error.response.data);
    }
    
    return JSON.stringify(error);
  }

  /**
   * Get a descriptive message for logging when an error is ignored
   * @param error - The error that was ignored
   * @param scenario - The GUS scenario
   * @returns string - A descriptive message for logging
   */
  public static getIgnoredErrorLogMessage(error: any, scenario: string): string {
    const errorMessage = this.extractErrorMessage(error);
    return `Ignoring expected error for ${scenario}: ${errorMessage}`;
  }
}
