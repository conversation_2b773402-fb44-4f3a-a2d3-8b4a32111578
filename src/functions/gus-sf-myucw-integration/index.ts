import { handlerPath } from '@libs/handler-resolver';

export const gusSfMyUCWIntegration = {
    handler: `${handlerPath(__dirname)}/gusSfMyUCWIntegrationService.handleSfRequests`,
    name: 'gus-sf-my-ucw-integration-${self:provider.stage}',
    events: [
        {
            sqs: {
                arn: '${self:provider.environment.GUS_SF_MY_UCW_INTEGRATION_SQS_QUEUE_ARN}'
            }
        }
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 180,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};

export const gusSfMyUCWFailedRecordProcessor = {
    handler: `${handlerPath(__dirname)}/gusSfMyUCWIntegrationService.handleFailedRecords`,
    name: 'gus-sf-my-ucw-failed-record-processor-${self:provider.stage}',
    events: [
        {
            schedule: 'rate(60 minutes)',
        },
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 60,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};