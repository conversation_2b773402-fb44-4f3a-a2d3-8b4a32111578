import { LoggerService } from "src/common/cloudwatchService";
import { putMYUCWData } from "src/connectors/myucw-connector";
const loggerService = new LoggerService();
import { getData } from "src/connectors/eip-connector";
import { LoggerEnum } from "@gus-eip/loggers";
import { EventHandler } from "../IEventHandler";
import { GUSErrorHandler } from "../utils/errorHandler";
const loggerEnum = new LoggerEnum();
export class WithdrawApplicationHandler implements EventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(opportunityId: string): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `fetch opportunity details by opportunityId initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityId,
        {},
        "Opportunity"
      );
      return await getData(
        `gus/getOpportunities/Id/${opportunityId}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        opportunityId,
        error,
        "FETCH_OPPORTUNITY_FAILED",
        "GUS_SF"
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncInMyUCW(event: any): Promise<any> {
    try {
      const statusResponse = await putMYUCWData("students/status", {
        sprogramID: event.sprogramID,
        status: event.status
      });

      const withdrawalResponse = await putMYUCWData("students", {
        sprogramID: event.sprogramID,
        App_withdr_reason: event.App_withdr_reason
      });

      return {
        statusResponse,
        withdrawalResponse
      };
    } catch (error) {
      await this.error(event, error, "SYNC_IN_MY_UCW_FAILED", "MY_UCW");
      throw new Error(`Error syncing in myucw: ${error}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.applicationFormId =
      platformEventMessage.payload.Application_Form_Id__c;
    this.brand = platformEventMessage.payload.BusinessUnitFilter__c;
    const sprogramID = platformEventMessage.payload.Applic_Id__c;
    const opportunityId = platformEventMessage.payload.Opportunity_Id__c;
    await this.log(
      platformEventMessage.payload,
      `Withdraw application initiated ${sprogramID}`,
      loggerEnum.Event.WITHDRAW_APPLICATION_INITIATED
    );
    try {
      if (sprogramID) {
        const opportunity = await this.fetchGusSFDetails(opportunityId);
        if (!opportunity?.length) {
          await this.error(
            event,
            `No opportunity found for ${opportunityId}`,
            "OPPORTUNITY_NOT_FOUND",
            "GUS_SF"
          );
          throw new Error(`No opportunity found for ${opportunityId}`);
        }
        await this.log(
          event,
          `fetch opportunity by opportunityId completed`,
          loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          opportunityId,
          opportunity,
          "Opportunity"
        );
        if (
          [
            "Application",
            "Documents Stage",
            "Admissions Stage",
            "Offer",
            "Payment",
            "Visa",
          ].includes(opportunity[0]?.StageName)
        ) {
          const stage = opportunity[0]?.StageName;
          const status =
            stage === "Admissions Stage"
              ? "status61"
              : stage === "Offer"
              ? "status66"
              : stage === "Payment"
              ? "status35"
              : "status61";

          await this.log(
            platformEventMessage.payload,
            `Sync in my ucw initiated ${sprogramID}`,
            loggerEnum.Event.SYNC_IN_MY_UCW_INITIATED,
            loggerEnum.Component.MY_UCW,
            {
              sprogramID,
              status,
              App_withdr_reason: opportunity[0]?.Closing_Comments__c,
            }
          );
          const response = await this.syncInMyUCW({
            sprogramID,
            status,
            App_withdr_reason: opportunity[0]?.Closing_Comments__c,
          });
          await this.log(
            platformEventMessage.payload,
            `Sync withdraw in my ucw completed ${sprogramID}`,
            loggerEnum.Event.OPERATION_COMPLETED,
            loggerEnum.Component.MY_UCW,
            {
              sprogramID,
              status,
              App_withdr_reason: opportunity[0]?.Closing_Comments__c,
            },
            response
          );
          return {
            statusCode: 200,
            body: JSON.stringify(response),
          };
        } else {
          // Check if this is the specific ignorable error for GUS_WITHDRAW_APPLICATION
          const error = "Cannot withdraw post offer";
          if (GUSErrorHandler.isIgnorableError(error, this.usecase)) {
            await this.log(
              platformEventMessage.payload,
              GUSErrorHandler.getIgnoredErrorLogMessage(error, this.usecase),
              loggerEnum.Event.WITHDRAW_APPLICATION_FAILED,
              loggerEnum.Component.MY_UCW
            );
            return {
              statusCode: 200,
              body: JSON.stringify({ message: "Withdraw request ignored - post offer stage" }),
            };
          }

          await this.error(
            platformEventMessage.payload,
            error,
            loggerEnum.Event.WITHDRAW_APPLICATION_FAILED,
            loggerEnum.Component.MY_UCW
          );
          throw new Error(error);
        }
      } else {
        await this.error(
          platformEventMessage.payload,
          "Invalid Program ID",
          loggerEnum.Event.WITHDRAW_APPLICATION_FAILED,
          loggerEnum.Component.MY_UCW
        );
        throw new Error("Invalid Program ID");
      }
    } catch (error) {
      await this.error(
        platformEventMessage,
        error.message ? error.message : error,
        loggerEnum.Event.WITHDRAW_APPLICATION_FAILED,
        loggerEnum.Component.MY_UCW
      );
      throw error.message ? error.message : error;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
