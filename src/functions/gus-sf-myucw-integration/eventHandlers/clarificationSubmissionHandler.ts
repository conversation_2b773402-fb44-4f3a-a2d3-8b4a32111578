import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { putMYUCWData } from "src/connectors/myucw-connector";
import { EventHandler } from "../IEventHandler";
import { GUS<PERSON>rrorHandler } from "../utils/errorHandler";
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
export class ClarificationSubmissionHandler implements EventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  fetchGusSFDetails(event: any, correlationId?: string): Promise<any> {
    throw new Error(`Yet to fetch`);
  }
  async syncInMyUCW(event: any): Promise<any> {
    try {
      return await putMYUCWData('students/status', event)
    } catch (error) {
      // Check if this is the specific ignorable error for GUS_CLARIFICATION_SUBMITTED
      if (GUSErrorHandler.isIgnorableError(error, this.usecase)) {
        await this.log(
          event,
          GUSErrorHandler.getIgnoredErrorLogMessage(error, this.usecase),
          loggerEnum.Event.SYNC_IN_MY_UCW_FAILED,
          loggerEnum.Component.MY_UCW
        );
        return { message: "Clarification submission ignored - permission denied" };
      }

      await this.error(event, error, loggerEnum.Event.SYNC_IN_MY_UCW_FAILED, loggerEnum.Component.MY_UCW);
      throw new Error(`Error syncing in myucw: ${error}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.applicationFormId =
      platformEventMessage.payload.Application_Form_Id__c;
    this.brand = platformEventMessage.payload.BusinessUnitFilter__c;
    const sprogramID = platformEventMessage.payload.Applic_Id__c;
    await this.log(
      platformEventMessage.payload,
      `Clarification submission initiated ${sprogramID}`,
      loggerEnum.Event.CLARIFICATION_SUBMISSION_INITIATED
    );
    try {
      if (sprogramID) {
        await this.log(
          platformEventMessage.payload,
          `Sync in my ucw initiated ${sprogramID}`,
          loggerEnum.Event.SYNC_IN_MY_UCW_INITIATED,
          loggerEnum.Component.MY_UCW,
          { sprogramID, status: "status123" }
        );
        const response = await this.syncInMyUCW({ sprogramID, status: "status123" });
        await this.log(
          platformEventMessage.payload,
          `Sync in my ucw completed ${sprogramID}`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.MY_UCW,
          { sprogramID, status: "status123" },
          response
        );
        return {
          statusCode: 200,
          body: JSON.stringify(response),
        }
      } else {
        await this.error(
          platformEventMessage.payload,
          "Invalid Program ID",
          loggerEnum.Event.CLARIFICATION_SUBMISSION_FAILED,
          loggerEnum.Component.MY_UCW
        );
        throw new Error("Invalid Program ID");
      }
    } catch (error) {
      await this.error(
        platformEventMessage,
        error.message ? error.message : error,
        loggerEnum.Event.CLARIFICATION_SUBMISSION_FAILED,
        loggerEnum.Component.MY_UCW
      )
      throw error.message ? error.message : error;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message ? errorMessage.message : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
