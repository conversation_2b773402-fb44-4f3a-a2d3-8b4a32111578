import { getData, postData } from "src/connectors/eip-connector";
import { myUCWMappings } from "src/functions/gus-sf-myucw-integration/myUCWMappings";
import { getAllPicklistValues } from "src/common/getPickListValue";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { postMYUCWData } from "src/connectors/myucw-connector";
import { SnsService } from "src/common/snsService";
import { EventHandler } from "../IEventHandler";
const { v4: uuidv4 } = require("uuid");
const snsService = new SnsService();
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
export class NewApplicationHandler implements EventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(opportunityId: any): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `Fetch gus object details for ${opportunityId}`,
        loggerEnum.Event.FETCH_GUS_APPLICATION_INTIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return await getData(
        `gus/getapplicationsdetails/${opportunityId}?scenario=${this.usecase}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        opportunityId,
        error,
        loggerEnum.Event.FETCH_GUS_APPLICATION_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching gus object details: ${error}`);
    }
  }
  async syncInMyUCW(event: any): Promise<any> {
    try {
      return await postMYUCWData("students", event);
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_MYUCW_FAILED,
        loggerEnum.Component.MY_UCW
      );
      throw new Error(`Error syncing in myucw: ${error}`);
    }
  }

  async handleMessage(event: any): Promise<any> {
    let myUCWDetails;
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    // Generate new UUID for outbound messages

    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.applicationFormId =
      platformEventMessage?.payload?.Application_Form_Id__c;
    this.brand = platformEventMessage?.payload?.BusinessUnitFilter__c;
    const { Opportunity_Id__c } = platformEventMessage?.payload;

    // Check if this is a reprocessing scenario with saved createStudentResponse
    const savedCreateStudentResponse = platformEventMessage?.payload?.createStudentResponse;
    const isReprocessing = !!savedCreateStudentResponse;

    await this.log(
      platformEventMessage.payload,
      `MY UCW Application ${isReprocessing ? 'reprocessing' : 'create'} initiated ${Opportunity_Id__c}`,
      loggerEnum.Event.CREATE_APPLICATION_INITIATED
    );

    try {
      let gusSfObjectDetails = await this.fetchGusSFDetails(Opportunity_Id__c);
      await this.log(
        Opportunity_Id__c,
        `Fetch gus object details for ${Opportunity_Id__c}`,
        loggerEnum.Event.FETCH_GUS_APPLICATION_COMPLETED
      );

      if (!gusSfObjectDetails.Opportunity.ApplicId__c || isReprocessing) {
        let createStudentResponse;

        if (isReprocessing) {
          // Use saved response from previous processing
          createStudentResponse = savedCreateStudentResponse;
          await this.log(
            platformEventMessage.payload,
            `Using saved createStudentResponse for reprocessing ${Opportunity_Id__c}`,
            loggerEnum.Event.OPERATION_COMPLETED,
            loggerEnum.Component.MY_UCW,
            {},
            createStudentResponse
          );
        } else {
          // Normal processing flow - create student in MyUCW
          gusSfObjectDetails = await this.handleCustomFields(gusSfObjectDetails);
          await this.log(
            {},
            "Handling custom fields completed",
            loggerEnum.Event.HANDLE_CUSTOM_FIELD_COMPLETED
          );
          myUCWDetails = await this.mapMYUCWFields(gusSfObjectDetails);
          await this.log(
            {},
            "Mapping myucw fields completed",
            loggerEnum.Event.MYUCW_FIELD_MAPPING_COMPLETED,
            "",
            {},
            myUCWDetails
          );
          myUCWDetails = await this.formatDateFields(myUCWDetails);
          await this.log(
            myUCWDetails,
            "Formating date fields completed",
            loggerEnum.Event.MYUCW_DATE_FIELD_FORMAT_COMPLETED,
            "",
            {},
            myUCWDetails
          );
          myUCWDetails = await this.mapPicklistValues(myUCWDetails);
          await this.log(
            myUCWDetails,
            "Picklist mappings completed",
            loggerEnum.Event.MYUCW_PICKLIST_MAPPING_COMPLETED,
            "",
            {},
            myUCWDetails
          );
          if (gusSfObjectDetails.Account?.Student_External_ID__c) {
            myUCWDetails.studentNum =
              gusSfObjectDetails.Account.Student_External_ID__c;
          }
          myUCWDetails.App_received_date = await this.getCurrentDate();
          createStudentResponse = await this.syncInMyUCW(myUCWDetails);
        }

        // Process update operations with error handling and state saving
        await this.processUpdateOperations(
          createStudentResponse,
          Opportunity_Id__c,
          gusSfObjectDetails,
          platformEventMessage,
          isReprocessing
        );

        await this.log(
          platformEventMessage.payload,
          `MY UCW Application ${isReprocessing ? 'reprocessed' : 'created'} for ${Opportunity_Id__c}`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.MY_UCW,
          myUCWDetails,
          createStudentResponse
        );

        // Clean up saved state if processing completed successfully
        if (platformEventMessage.payload.createStudentResponse) {
          delete platformEventMessage.payload.createStudentResponse;
        }

        platformEventMessage.event.EventUuid = uuidv4();
        platformEventMessage.payload.Scenario__c = "GUS_NEW_DOCUMENT";
        platformEventMessage.payload.sid = createStudentResponse.sprogramID;
        console.log("platformEventMessage ->", platformEventMessage);
        const publishMessageResponse = await snsService.publishMessages(
          platformEventMessage,
          platformEventMessage.payload.Application_Form_Id__c,
          process.env.GUS_SF_OUTBOUND_TOPIC_ARN,
          this.brand
        );
        console.log("publishMessageResponse", publishMessageResponse);
        return createStudentResponse;
      } else {
        await this.log(
          Opportunity_Id__c,
          `Student program already exists in MyUCW ${gusSfObjectDetails.Opportunity.ApplicId__c}`,
          loggerEnum.Event.APPLICATION_EXIST
        );
        return {
          success: true,
          message: `Student program already exists in MyUCW ${gusSfObjectDetails.Opportunity.ApplicId__c}`,
        };
      }
    } catch (error) {
      console.log(error);
      await this.error(
        platformEventMessage,
        error.message ? error.message : error,
        loggerEnum.Event.CREATE_APPLICATION_FAILED,
        loggerEnum.Component.MY_UCW,
        myUCWDetails
      );

      throw error.message ? error.message : error;
    }
  }

  private async getCurrentDate(): Promise<any> {
    const date = new Date();

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  }

  /**
   * Process update operations with error handling and state saving for reprocessing
   */
  private async processUpdateOperations(
    createStudentResponse: any,
    opportunityId: string,
    gusSfObjectDetails: any,
    platformEventMessage: any,
    isReprocessing: boolean
  ): Promise<void> {
    try {
      // Update opportunity
      await this.updateGusOpportunity(createStudentResponse, opportunityId);

      // Update account if needed
      if (!gusSfObjectDetails.Account?.Student_External_ID__c) {
        await this.updateGusAccount(createStudentResponse, gusSfObjectDetails);
      }
    } catch (error) {
      // Save createStudentResponse to payload for reprocessing
      if (!isReprocessing) {
        platformEventMessage.payload.createStudentResponse = createStudentResponse;
        await this.log(
          platformEventMessage.payload,
          `Saved createStudentResponse to payload for reprocessing due to update failure: ${error.message}`,
          loggerEnum.Event.CREATE_APPLICATION_FAILED,
          loggerEnum.Component.MY_UCW,
          {},
          createStudentResponse
        );
      }

      // Re-throw the error to trigger failure handling
      throw error;
    }
  }

  private async updateGusOpportunity(
    createStudentResponse,
    opportunityId
  ): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        { ApplicId__c: createStudentResponse.sprogramID },
        this.correlationId,
        process.env.UCW_KEY
      );
      await this.log(
        { ApplicId__c: createStudentResponse.sprogramID },
        "Opportunity update completed",
        loggerEnum.Event.UPDATE_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        "Opportunity",
        saveResponse
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        { ApplicId__c: createStudentResponse.sprogramID },
        error,
        loggerEnum.Event.UPDATE_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error updating opportunity: ${error}`);
    }
  }

  private async updateGusAccount(
    createStudentResponse,
    gusSfObjectDetails
  ): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateAccount/${gusSfObjectDetails?.Account?.Id}`,
        { Student_External_ID__c: createStudentResponse.studentNum },
        this.correlationId,
        process.env.UCW_KEY,
        "PATCH"
      );
      await this.log(
        { Student_External_ID__c: createStudentResponse.studentNum },
        "Account update completed",
        loggerEnum.Event.UPDATE_ACCOUNT_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        "Account",
        saveResponse
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        { Student_External_ID__c: createStudentResponse.studentNum },
        error,
        loggerEnum.Event.UPDATE_ACCOUNT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error updating account: ${error}`);
    }
  }

  private async formatDateFields(myUCWDetails): Promise<any> {
    try {
      const dateToYearFields = [
        "Edu1_last_year",
        "Edu2_last_year",
        "Edu3_last_year",
        "Edu4_last_year",
        "Edu5_last_year",
      ];
      for (const key of dateToYearFields) {
        if (myUCWDetails.hasOwnProperty(key)) {
          myUCWDetails[key] = new Date(myUCWDetails[key]).getFullYear();
        }
      }
      return myUCWDetails;
    } catch (error) {
      await this.error(
        myUCWDetails,
        error,
        loggerEnum.Event.DATE_FORMAT_ERROR,
        loggerEnum.Component.MY_UCW,
        {}
      );
      throw new Error("Error formatting date fields");
    }
  }

  private async mapPicklistValues(myUCWDetails): Promise<any> {
    try {
      const picklistMappings = await getAllPicklistValues("GUS_SF_MY_UCW");
      for (const list of picklistMappings) {
        if (myUCWDetails[list.SK]) {
          const updatedValue = list.Picklist[myUCWDetails[list.SK]];
          myUCWDetails[list.SK] = updatedValue;
        }
      }
      return myUCWDetails;
    } catch (error) {
      await this.error(
        myUCWDetails,
        error,
        loggerEnum.Event.PICKLIST_MAPPING_ERROR,
        loggerEnum.Component.MY_UCW,
        {}
      );
      throw new Error("Error mapping picklist values");
    }
  }

  private async handleCustomFields(details): Promise<any> {
    try {
      details.Opportunity.OwnerName = details?.Opportunity?.Owner?.Name;
      let miscDetails = details.Opportunity.Application_Misc_Details__c;
      if (miscDetails) {
        const fields = [
          "awardName"
        ];
        let miscDetailsObject = JSON.parse(miscDetails);

        fields.forEach((field) => {
          details.Opportunity[field] = miscDetailsObject[field];
        });
      }
      details.Opportunity.Status = "Partner Referred";
      let workHistoryRecordsResult = {};
      if (details.WorkHistoryRecord__c?.length > 0) {
        details.WorkHistoryRecord__c.slice(0, 3).forEach(
          async (record, index) => {
            record["hasWork"] = "To be assessed";
            record["Duration"] = this.categorizeDuration(
              record.StartDate__c,
              record.EndDate__c
            );
            Object.keys(record).forEach(async (key) => {
              if (!workHistoryRecordsResult?.[`${key}${index + 1}`]) {
                workHistoryRecordsResult[`${key}${index + 1}`] = {};
              }
              await this.appendNestedValues(
                workHistoryRecordsResult,
                key,
                record[key],
                index
              );
            });
          }
        );
        details = { ...details, ...workHistoryRecordsResult };
      }
      let educationHistoryRecordsResult = {};
      if (details.EducationHistoryRecord__c?.length > 0) {
        details.EducationHistoryRecord__c.sort((a, b) => {
          return parseInt(a.Name, 10) - parseInt(b.Name, 10);
        });
        details.EducationHistoryRecord__c.slice(0, 5).forEach(
          (record, index) => {
            record["hasInstitute"] = "Yes";
            Object.keys(record).forEach(async (key) => {
              if (!educationHistoryRecordsResult?.[`${key}${index + 1}`]) {
                educationHistoryRecordsResult[`${key}${index + 1}`] = {};
              }
              await this.appendNestedValues(
                educationHistoryRecordsResult,
                key,
                record[key],
                index
              );
            });
          }
        );
        details = { ...details, ...educationHistoryRecordsResult };
      }
      if (
        details?.LanguageProficiencyRecord__c?.length > 0 &&
        details.Account.Level__pc.startsWith("Postgraduate")
      ) {
        details.LanguageProficiencyRecord__c?.forEach((record) => {
          if (record.ProficiencyQualification__c === "GRE") {
            details.isGRE = "Yes";
            details.isGMAT = "No";
            details.testScore = record.TestScore__c;
            details.GRETestDate = record.TestDate__c;
          } else if (record.ProficiencyQualification__c === "GMAT") {
            details.isGRE = "No";
            details.isGMAT = "Yes";
            details.testScore = record.TestScore__c;
            details.GMATTestDate = record.TestDate__c;
          }
        });
      }
      if (details?.LanguageProficiencyRecord__c?.length > 0) {
        const standardizedTestRecord =
          details.LanguageProficiencyRecord__c.find(
            (record) => record.Name === "Standardized English Test"
          );

        if (standardizedTestRecord) {
          details.isStandardizedTest = "Yes";
          details.isEnglishInstruction = "No";
          if (standardizedTestRecord.ProficiencyQualification__c === "TOEFL") {
            details.standardizedTest =
              standardizedTestRecord.TestProvider__c === "PBT"
                ? "TOEFL (PBT)"
                : "TOEFL (IBT)";
          } else {
            details.standardizedTest =
              standardizedTestRecord.ProficiencyQualification__c;
          }
          details.standardizedTestDate = standardizedTestRecord.TestDate__c;
          details.standardizedTestScore = standardizedTestRecord.TestScore__c;
          details.standardizedTestNo =
            standardizedTestRecord.Test_link_Certification__c;
          details.standardizedTestWritingScore =
            standardizedTestRecord.Writing_Score__c;
        } else {
          details.isStandardizedTest = "No";
          details.isEnglishInstruction = "Yes";
        }
      }
      details.Opportunity.Location__c === "Distance Learning" ||
      details.Opportunity.Location__c === "Online"
        ? (details.campus = "Online")
        : (details.campus = "Campus");
      details.Opportunity.Location__c === "Online"
        ? (details.programLocation = `${details.Opportunity.Location__c}_${details.Opportunity.Programme__c}`)
        : (details.programLocation = details.Opportunity.Programme__c);
      details.Opportunity.IsPathwayProviderId__c = details.Opportunity
        .PathwayProviderId__c
        ? "Yes"
        : "No";
      if (details.Connection__c) {
        details.Connection__c.EmergencyFullName = `${details?.Connection__c?.First_Name_c__c} ${details?.Connection__c?.Last_Name__c}`;
      }
      return details;
    } catch (error) {
      await this.error({}, error, loggerEnum.Event.CUSTOM_FIELD_ERROR);
      throw new Error(`Error handling custom fields: ${error.message}`);
    }
  }

  async appendNestedValues(base, key, value, index) {
    const indexedKey = `${key}${index + 1}`;

    if (typeof value === "object" && value !== null) {
      if (!base[indexedKey]) {
        base[indexedKey] = {};
      }
      Object.keys(value).forEach((nestedKey) => {
        this.appendNestedValues(
          base[indexedKey],
          nestedKey,
          value[nestedKey],
          index
        );
      });
    } else {
      base[indexedKey] = value;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }

  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.MY_UCW,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }

  async mapMYUCWFields(event: any): Promise<any> {
    try {
      const mappedDetails = {};
      for (const [key, value] of Object.entries(myUCWMappings)) {
        if (key.includes(".")) {
          const [objectKey, propertyKey] = key.split(".");
          if (
            event?.[objectKey] &&
            event[objectKey][propertyKey] !== undefined
          ) {
            if (Array.isArray(value)) {
              value.forEach((mappedKey: string) => {
                if (event[objectKey][propertyKey]) {
                  mappedDetails[mappedKey] = event[objectKey][propertyKey];
                }
              });
            } else {
              if (event[objectKey][propertyKey]) {
                mappedDetails[value] = event[objectKey][propertyKey];
              }
            }
          }
        } else {
          if (event?.[key] !== undefined) {
            if (Array.isArray(value)) {
              value.forEach((mappedKey: string) => {
                if (event[key]) {
                  mappedDetails[mappedKey] = event[key];
                }
              });
            } else {
              if (event[key]) {
                mappedDetails[value] = event[key];
              }
            }
          }
        }
      }
      return mappedDetails;
    } catch (error) {
      await this.error({}, error, loggerEnum.Event.MYUCW_FIELD_MAPPING_ERROR);
      throw new Error(`Error mapping MYUCW fields: ${error.message}`);
    }
  }

  categorizeDuration(startDateStr: string, endDateStr?: string): string {
    const startDate = new Date(startDateStr);
    const endDate = endDateStr ? new Date(endDateStr) : new Date();

    if (isNaN(startDate.getTime())) {
      console.log("Invalid Start Date");
    }
    if (isNaN(endDate.getTime())) {
      console.log("Invalid End Date");
    }

    const diffInMs = endDate.getTime() - startDate.getTime();
    const diffInYears = diffInMs / (1000 * 60 * 60 * 24 * 365.25);

    if (diffInYears < 1) {
      return "< 1 Year";
    } else if (diffInYears >= 1 && diffInYears <= 3) {
      return "= {1,…,3} Years";
    } else {
      return "> 3 Years";
    }
  }
}
