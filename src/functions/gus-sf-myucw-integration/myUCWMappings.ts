export const myUCWMappings = {
  "Opportunity.ApplicationFormId__c": "GUS_appid",
  "Opportunity.ApplicationId__c": "SA_appid",
  "Opportunity.ApplicantId__c": "GUS_h_id",
  "Account.FirstName": "firstname",
  "Account.LastName": "surname",
  "Account.Middle_Name__c": "middlename",
  "Account.PreferedFirstName__c": "preferredname",
  "Account.Mobile__c": ["App_phone", "Curr_phone"],
  "Account.DateofBirth__c": "birthday",
  "Account.Gender__c": "gender",
  "Account.Type_of_Student__c": "citizenshipID",
  "Account.gaconnector_City__c": "App_city",
  "Account.Primary_Language__c": "Pri_lang",
  "Account.PersonMailingPostalCode": "App_postal",
  "Account.Citizenship__c": "originID",
  "Opportunity.Location__c": "campusID",
  "Opportunity.Status": "status",
  "Opportunity.awardName": "Award_name",
  programLocation: "programID",
  "Opportunity.Programme__c": "App_program",
  "Account.PersonMailingStreet": "App_st_address",
  "Account.ShippingStreet": "Curr_st_address",
  "Account.ShippingCity": "Curr_city",
  "Account.ShippingCountryCode": "Curr_country",
  "Account.ShippingPostalCode": "Curr_postal",
  "Account.PersonEmail": ["Curr_email", "App_email", "email"],
  "Account.PersonMailingCountryCode": "country_code",
  "Account.Citizenship_Status__c": "App_residency",
  "Connection__c.Email_c__c": "Emerg_email",
  "Connection__c.Phone_c__c": "Emerg_phone",
  "Connection__c.EmergencyFullName": "Emerg_name",
  "Connection__c.Relationship__c": "Emerg_relation",
  "Opportunity.Product_Intake_Date__c": [
    "App_intake",
    "IntakeID",
    "primaryTermID",
    "admissionTermID",
  ],
  "Opportunity.Citizenship__c": "originID",
  "Opportunity.AgentAccountName__c": "Agent_name",
  "Opportunity.AgentAccount__c": "Agent_id",
  "Opportunity.OwnerName": "Rep_name",

  Education_Level__c1: "Edu1_level",
  Study_completed__c1: "Edu1_completed",
  GraduationDate__c1: "Edu1_last_year", //No UI
  InstitutionName__c1: "Edu1_name",
  GPA_Score__c1: "Edu1_cgpa",
  Country__c1: "Edu1_country",
  hasInstitute1: "Edu1_add",

  Education_Level__c2: "Edu2_level",
  Study_completed__c2: "Edu2_completed",
  InstitutionName__c2: "Edu2_name",
  GraduationDate__c2: "Edu2_last_year",
  GPA_Score__c2: "Edu2_cgpa", //No UI
  Country__c2: "Edu2_country",
  hasInstitute2: "Edu2_add",

  Education_Level__c3: "Edu3_level",
  Study_completed__c3: "Edu3_completed",
  InstitutionName__c3: "Edu3_name",
  GraduationDate__c3: "Edu3_last_year",
  GPA_Score__c3: "Edu3_cgpa", //No UI
  Country__c3: "Edu3_country",
  hasInstitute3: "Edu3_add",

  Education_Level__c4: "Edu4_level",
  Study_completed__c4: "Edu4_completed",
  InstitutionName__c4: "Edu4_name",
  GraduationDate__c4: "Edu4_last_year",
  GPA_Score__c4: "Edu4_cgpa", //No UI
  Country__c4: "Edu4_country",
  hasInstitute4: "Edu4_add",

  Education_Level__c5: "Edu5_level",
  Study_completed__c5: "Edu5_completed",
  InstitutionName__c5: "Edu5_name",
  GraduationDate__c5: "Edu5_last_year",
  GPA_Score__c5: "Edu5_cgpa", //No UI
  Country__c5: "Edu5_country",
  hasInstitute5: "Edu5_add",

  hasWork1: "Work_busi1",
  NatureOfDuties__c1: "Work_busi1_level",
  Duration1: "Work_busi1_duration",
  Position_Type__c1: "Work_busi1_status",
  Position__c1: "Work_busi1_title",
  Employer__c1: "Work_busi1_comp",
  EmployerAddress__c1: "Work_busi1_country",
  StartDate__c1: "Work_busi1_start",
  EndDate__c1: "Work_busi1_end",

  hasWork2: "Work_busi2",
  NatureOfDuties__c2: "Work_busi2_level",
  Duration2: "Work_busi2_duration",
  Position_Type__c2: "Work_busi2_status",
  Position__c2: "Work_busi2_title",
  Employer__c2: "Work_busi2_comp",
  EmployerAddress__c2: "Work_busi2_country",
  StartDate__c2: "Work_busi2_start",
  EndDate__c2: "Work_busi2_end",

  hasWork3: "Work_busi3",
  NatureOfDuties__c3: "Work_busi3_level",
  Duration3: "Work_busi3_duration",
  Position_Type__c3: "Work_busi3_status",
  Position__c3: "Work_busi3_title",
  Employer__c3: "Work_busi3_comp",
  EmployerAddress__c3: "Work_busi3_country",
  StartDate__c3: "Work_busi3_start",
  EndDate__c3: "Work_busi3_end",

  isGRE: "GRE_score",
  isGMAT: "GMAT_test",
  testScore: "GMAT_score",
  GRETestDate: "GRE_date",
  GMATTestDate: "GMAT_date",
  isStandardizedTest: "Engl_test",
  standardizedTest: "Engl_test_name",
  standardizedTestDate: "Engl_test_date",
  standardizedTestScore: "Engl_score",
  standardizedTestNo: "Engl_test_report",
  standardizedTestWritingScore: "Engl_score_writing",
  campus: "App_campus",
  "Opportunity.IsPathwayProviderId__c": "Esl_pathway",
  "Opportunity.PathwayProviderId__c": ["Esl_institution", "Adm_pathway_prog"],
  isEnglishInstruction: "Engl_instruction",
};
