export const ulawAgentFieldMapper = {
  // Nested record‐types (if any arrays in payload; none here)
  IdentityInfoRecord__c: [],
  LanguageProficiencyRecord__c: [],
  EducationHistoryRecord__c: [],
  OpportunityFile__c: [],
  Connection__c: [],
  WorkHistoryRecord__c: [],

  // Lead mapping
  // Lead: {
  //   // Person details
  //   "accountDetails.firstName": "FirstName",
  //   "accountDetails.lastName": "LastName",
  //   "accountDetails.email": "Email",
  //   "accountDetails.phone.dialCode,accountDetails.phone.number": "Phone",
  //   "accountDetails.alternatePhone.dialCode,accountDetails.alternatePhone.number":
  //     "MobilePhone",

  //   // Campaign & source
  //   "leadDetails.source": "LeadSource",
  //   "leadDetails.media": "Media__c",
  //   "leadDetails.leadType.code": "Lead_Type__c",
  //   "leadDetails.courseOfInterest.code": "Programme__c",
  //   "leadDetails.campaign.code": "Campaign__c",
  //   "leadDetails.event.code": "Event__c",

  //   // Campus
  //   "leadDetails.campus.code": "Campus_Code__c",
  //   "leadDetails.campus.name": "Campus_Name__c",

  //   // Agent linkage
  //   "applicationDetails.agentDetails.AgentAccountId": "AgentAccount__c",
  //   "applicationDetails.agentDetails.agentContactId": "Agent_Contact__c",
  //   "applicationDetails.agentDetails.accountManagerId": "BusinessDeveloper__c",
  //   "applicationDetails.agentDetails.agentContactUserId": "OwnerId",
  // },

  // Account (PersonAccount) mapping
  Account: {
    "accountDetails.firstName": "FirstName",
    "accountDetails.lastName": "LastName",
    "accountDetails.email": "PersonEmail",
    "accountDetails.dob": "DateOfBirth__c",
    "accountDetails.gender": "Gender__c",
    "accountDetails.nationality.code": "Citizenship__c",
    "accountDetails.country.name": "Country__c",
    "accountDetails.phone.dialCode,accountDetails.phone.number": [
      "Mobile__c",
      "PersonMobilePhone",
    ],
    // PersonMailing address
    "accountDetails.mailingAddress.street": "PersonMailingStreet",
    "accountDetails.mailingAddress.city": "PersonMailingCity",
    "accountDetails.mailingAddress.state": "PersonMailingState",
    "accountDetails.mailingAddress.postalCode": "PersonMailingPostalCode",
    "accountDetails.mailingAddress.country.code": "PersonMailingCountryCode",
    "accountDetails.mailingAddress.country.name": "PersonMailingCountry",

    // Shipping address
    "accountDetails.shippingAddress.street": "ShippingStreet",
    "accountDetails.shippingAddress.city": "ShippingCity",
    "accountDetails.shippingAddress.state": "ShippingState",
    "accountDetails.shippingAddress.postalCode": "ShippingPostalCode",
    "accountDetails.shippingAddress.country.code": "ShippingCountryCode",
    "accountDetails.shippingAddress.country.name": "ShippingCountry",

    // Agent linkage
    "applicationDetails.agentDetails.agentContactUserId": "OwnerId",
  },

  // Opportunity mapping
  Opportunity: {
    "applicationDetails.externalApplicationId": "ApplicId__c",
    "applicationDetails.gusApplicationId": "ApplicationFormId__c",
    "applicationDetails.applicationSubmitted": "ApplicationSubmitted__c",
    "applicationDetails.stageName": "StageName",
    "applicationDetails.applicationProgressPercentage":
      "ApplicationProgress__c",
    "applicationDetails.applicantId": "Student_ID__c",

    // Course / intake
    "applicationDetails.courseInfo.courseId": "Programme__c",
    "applicationDetails.courseInfo.courseName": "Name",
    "applicationDetails.courseInfo.intake": "Product_Intake_Date__c",
    "applicationDetails.courseInfo.location": "Location__c",
    "applicationDetails.courseInfo.deliveryMode": "Delivery_Mode__c",
    "applicationDetails.submittedDate": "CloseDate",
    "accountDetails.phone.dialCode,accountDetails.phone.number":
      "AccountMobile__c",

    // Agent linkage
    "applicationDetails.agentDetails.AgentAccountId": "AgentAccount__c",
    "applicationDetails.agentDetails.agentContactId": "Agent_Contact__c",
    "applicationDetails.agentDetails.accountManagerId": "BusinessDeveloper__c",
    "applicationDetails.agentDetails.agentContactUserId": "OwnerId",
  },

  // Custom Application__c object mapping
  Application__c: {
    "applicationDetails.gusApplicationId": "Application_Form_Id__c",
    "applicationDetails.submissionSignature": "Submission_Signature__c",
    "applicationDetails.submittedDate": "Submitted_Date__c",
    "applicationDetails.stageName": "StageName__c",
    "applicationDetails.applicationProgressPercentage": "Progress__c",
  },

  // OpportunityLineItem mapping
  OpportunityLineItem: {
    "applicationDetails.courseInfo.courseId": "Product2Id",
  },
};
