import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";

import { v4 as uuid } from "uuid";
import { <PERSON>Hand<PERSON> } from "../HandlerRegistry";
import { AcknowledgmentService } from "../services/AcknowledgementService";
import { AckStatus } from "../enums/ack-status.enum";
import { ulawAgentFieldMapper } from "../mappers/field-mappings/ulawAgentMapper";
import { ulawStudentFieldMapper } from "../mappers/field-mappings/ulawStudentMapper";
import { salesforceCustomMetadata } from "../mappers/metadata/salesforce-custom-fields";
import { handleErrorAcknowledgment } from "../utils/AcknowledgementUtils";
import { validateEventPayload } from "../validations/application.validation";
import { BadRequestException } from "@nestjs/common";
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

@RegisterHandler("ULAW_NEW_APPLICATION")
export class ApplicationHandler implements IULAWOutboundEventHandler {
  private correlationId: string;
  private usecase: string = "ULAW_NEW_APPLICATION";
  private readonly brand: string = "ULAW";
  private applicationFormId: string = uuid();
  async fetchGusSFDetails(sid: string): Promise<any> {
    throw new Error("Method not implemented.");
  }

  private generateSfPayload(input: Record<string, any>, config: any) {
    const result: Record<string, any> = {};

    for (const [objectName, fieldMap] of Object.entries(config)) {
      const sfObject: Record<string, any> = {};

      for (const [pathKey, sfFieldMapping] of Object.entries(fieldMap)) {
        const paths = pathKey.split(",").map((p) => p.trim());
        const values = paths
          .map((path) => this.getValueByPath(input, path))
          .filter((v) => v !== undefined && v !== null);

        const joinedValue = values.join(" ");

        if (Array.isArray(sfFieldMapping)) {
          // Assign same value to all SF fields in the array
          sfFieldMapping.forEach((sfField) => {
            sfObject[sfField] = joinedValue;
          });
        } else if (typeof sfFieldMapping === "string") {
          sfObject[sfFieldMapping] = joinedValue;
        } else {
          console.warn(`Invalid SF field mapping for key: ${pathKey}`);
        }
      }

      result[objectName] = sfObject;
    }

    return result;
  }

  // Helper to extract value from dot-path like 'accountDetails.firstName'
  private getValueByPath(obj: any, path: string): any {
    return path.split(".").reduce((acc, key) => acc?.[key], obj);
  }

  async syncToGus(applicationDetails: any): Promise<any> {
    try {
      const response = await postData(
        "gus/ulaw/persistapplication",
        applicationDetails,
        this.correlationId,
        "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa"
      );

      return response;
    } catch (error) {
      throw new Error(`Error creating Application in GUS: ${error}`);
    }
  }
  async checkApplicationIdExistsInSalesforce(applicationId: string) {
    const response = await getData(
      `gus/applicationDetailsById/${applicationId}`,
      this.correlationId,
      "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa"
    );

    return response;
  }

  async handleMessage(event: any, retryCount?: number): Promise<any> {
    console.log("event", event);
    this.correlationId = event?.correlationId ?? uuid();
    this.usecase = event?.scenario ?? "ULAW_NEW_APPLICATION";
    await this.log(
      event,
      `ULAW Campusnet GUS SF Application sync initiated`,
      loggerEnum.Event.VALIDATION_FAILED
    );
    const validationErrors = validateEventPayload(event);
    if (validationErrors.length > 0) {
      // Call error acknowledgment & return
      await handleErrorAcknowledgment(
        this.correlationId,
        this.usecase,
        event,
        { message: "Validation failed", errors: validationErrors },
        { message: "Validation failed", errors: validationErrors }
      );
      // Optionally, log & throw a specific error
      throw new BadRequestException("Payload validation failed");
    }
    const isAgent = event?.payload?.applicationDetails?.agentDetails;
    if (isAgent) {
      // check if application id exists in salesforce
      const applicationDetails =
        await this.checkApplicationIdExistsInSalesforce(
          event?.payload?.applicationDetails?.gusApplicationId
        );
      if (applicationDetails.length === 0) {
        // Call error acknowledgment & return
        await handleErrorAcknowledgment(
          this.correlationId,
          this.usecase,
          event,
          { message: "Application does not exist in Salesforce" },
          { message: "Application does not exist in Salesforce" }
        );
        await this.error(
          event,
          `Application does not exist in Salesforce`,
          loggerEnum.Event.SYNC_IN_GUS_SF_FAILED
        );
        // Optionally, log & throw a specific error
        throw new BadRequestException(
          "Application does not exist in Salesforce"
        );
      }
      this.applicationFormId = applicationDetails[0].Application_Form_Id__c;
    }
    let opportunityId: string | undefined;
    try {
      const sfConfig = event?.payload?.applicationDetails?.agentDetails
        ? ulawAgentFieldMapper
        : ulawStudentFieldMapper;
      let applicationDetails = this.generateSfPayload(event.payload, sfConfig);
      applicationDetails = this.enrichPayloadWithCustomFields(
        applicationDetails,
        process.env.STAGE || "dev"
      );
      applicationDetails = {
        ...applicationDetails,
        applicationId: event?.payload?.applicationDetails?.gusApplicationId,
        email: event?.payload?.accountDetails?.email,
        requestId: this.correlationId,
        sectionLabel: this.usecase,
      };
      console.log("applicationDetails ", applicationDetails);
      const response = await this.syncToGus(applicationDetails);
      const details = {
        externalApplicationId: event?.payload?.externalApplicationId,
        gusOpportunityId: response?.opportunityId,
        gusApplicationId: event.payload.gusApplicationId || "",
      };
      // Send acknowledgment after successful sync
      await AcknowledgmentService.sendAcknowledgment(
        this.correlationId,
        this.usecase,
        AckStatus.SUCCESS,
        "Application processed successfully",
        details
      );
      await this.log(
        event,
        `ULAW Campusnet GUS SF Application creation completed`,
        loggerEnum.Event.SYNC_IN_GUS_SF_COMPLETED
      );
      await this.log(
        event,
        `ULAW Campusnet GUS SF Application sync completed`,
        loggerEnum.Event.OPERATION_COMPLETED
      );
      return response;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      if (retryCount && retryCount > 3) {
        const details = {
          externalApplicationId:
            event.payload?.applicationDetails?.externalApplicationId,
          gusOpportunityId: opportunityId,
          gusApplicationId:
            event.payload?.applicationDetails?.gusApplicationId || null,
        };

        await handleErrorAcknowledgment(
          this.correlationId,
          this.usecase,
          event,
          error,
          details
        );
      }
      throw error;
    }
  }
  /**
   * Enriches the payload with custom fields for the given stage.
   *
   * This enriches the payload with the following custom fields:
   * - BusinessUnit__c
   * - RecordTypeId
   * - Brand__c
   *
   * The custom field values are determined by the stage.
   *
   * @param {Object} applicationDetails - The payload to enrich.
   * @param {String} stage - The stage of the payload.
   * @returns {Object} The enriched payload.
   */
  private enrichPayloadWithCustomFields(
    applicationDetails: any,
    stage: string
  ): any {
    const config =
      salesforceCustomMetadata[stage] || salesforceCustomMetadata.dev;
    const recordTypeIds = config.RECORD_TYPE_IDS;

    ["Lead", "Opportunity", "Account"].forEach((object) => {
      if (applicationDetails[object]) {
        applicationDetails[object] = {
          ...applicationDetails[object],
          BusinessUnit__c: config.BUSINESS_UNIT_ID,
          RecordTypeId: recordTypeIds[object],
        };

        if (["Lead", "Account"].includes(object)) {
          applicationDetails[object].Brand__c = "ULAW";
        }
      }
    });
    applicationDetails.Opportunity.ApplicationSubmitted__c =
      !!applicationDetails.Opportunity.Application_Submitted__c;
    return applicationDetails;
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destinationPayload
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
