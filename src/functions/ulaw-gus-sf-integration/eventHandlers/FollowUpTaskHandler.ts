import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { v4 as uuid } from "uuid";
import { LoggerEnum } from "@gus-eip/loggers";
import { <PERSON>Hand<PERSON> } from "../HandlerRegistry";
import { AckStatus } from "../enums/ack-status.enum";
import { AcknowledgmentService } from "../services/AcknowledgementService";
import { handleErrorAcknowledgment } from "../utils/AcknowledgementUtils";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();

@RegisterHandler("ULAW_NEED_CLARIFICATION")
export class FollowUpTaskHandler implements IULAWOutboundEventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        sid,
        error,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncToGus(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa"
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async handleMessage(event: any, retryCount?: number): Promise<any> {
    this.correlationId = event?.correlationId || uuid();
    this.usecase = event?.scenario || "ULAW_NEED_CLARIFICATION";
    this.brand = "ULAW";
    let payload;
    if (typeof event.payload === "string") {
      payload = JSON.parse(event.payload);
    } else {
      payload = event.payload;
    }
    const applicId = payload?.externalApplicationId;
    this.applicationFormId = payload?.gusApplicationId || applicId;
    const casStatus = payload?.externalApplicationStatus;
    const description = payload?.clarificationNote;
    let opportunityId;
    try {
      if (!applicId || !casStatus) {
        console.error("Missing applicId or casStatus in event payload");
        await this.error(
          event,
          "applicId or casStatus not found",
          loggerEnum.Event.SYNC_IN_GUS_SF_FAILED
        );
        throw new Error("Invalid event message: Missing applicId or casStatus");
      }
      const opportunity = await this.fetchGusSFDetails(applicId);
      if (!opportunity[0]?.Id) {
        await this.error(
          event,
          `No opportunity found for ${applicId}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${applicId}`);
      }
      opportunityId = opportunity[0].Id;
      await this.log(
        event,
        `fetch opportunity by ApplicationFormId completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        applicId,
        opportunity,
        "Opportunity"
      );

      const opportunityUpdateRequest: any = {
        AdmissionsStage__c: "Further clarification required",
        Admission_Condition__c: description,
        Admissions_Condition__c: description,
      };
      await this.log(
        event,
        `sync opportunity details initiated`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityUpdateRequest,
        {},
        "Opportunity"
      );
      console.log("opportunityUpdateRequest", opportunityUpdateRequest);
      await this.syncToGus(opportunityUpdateRequest, opportunityId);

      const details = {
        externalApplicationId: applicId,
        gusOpportunityId: opportunityId,
        gusApplicationId: payload?.gusApplicationId || null,
        gusAdmissionsStage: "Further clarification required",
      };
      // Send acknowledgment after successful sync
      await AcknowledgmentService.sendAcknowledgment(
        this.correlationId,
        this.usecase,
        AckStatus.SUCCESS,
        `Admissions Stage updated successfully for ${applicId} to Further clarification required`,
        details
      );
      await this.log(
        event,
        `ULAW Campusnet GUS SF Application sync completed`,
        loggerEnum.Event.SYNC_IN_GUS_SF_COMPLETED
      );
      await this.log(
        event,
        `sync opportunity details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      if (retryCount && retryCount > 3) {
        const details = {
          externalApplicationId: applicId,
          gusOpportunityId: opportunityId,
          gusApplicationId: payload?.gusApplicationId || null,
        };

        await handleErrorAcknowledgment(
          this.correlationId,
          this.usecase,
          event,
          error,
          details
        );
      }
      throw error;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
