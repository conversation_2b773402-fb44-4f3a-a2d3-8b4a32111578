import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
import { v4 as uuid } from "uuid";
import { casStatusMapping, SfMappedValues } from "../enums/offer.enum";
import { RegisterHandler } from "../HandlerRegistry";
import { AckStatus } from "../enums/ack-status.enum";
import { AcknowledgmentService } from "../services/AcknowledgementService";
import { handleErrorAcknowledgment } from "../utils/AcknowledgementUtils";

@RegisterHandler("ULAW_OFFER_STAGE")
export class OfferHandler implements IULAWOutboundEventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private errors: Error[] = [];
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        sid,
        error,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }
  async syncToGus(event: unknown, opportunityId?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa"
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(
        new Error(`Error syncing opportunity details: ${error}`)
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async handleMessage(event: any, retryCount?: number): Promise<any> {
    this.correlationId = event?.correlationId || uuid();
    this.usecase = event?.scenario || "ULAW_OFFER_STAGE";

    await this.log(
      event,
      `ULAW Campusnet GUS SF Application sync initiated`,
      loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED
    );

    let payload;
    if (typeof event.payload === "string") {
      payload = JSON.parse(event.payload);
    } else {
      payload = event.payload;
    }
    const applicId = payload?.externalApplicationId;
    const casStatus = payload?.externalApplicationStatus;
    const condition = payload?.condition;
    const vrfSubmitted = payload?.vrfSubmitted;
    let opportunityId;
    try {
      if (!applicId || !casStatus) {
        console.error("Missing applicId or casStatus in event payload");
        await this.error(
          event,
          "applicId or casStatus not found",
          loggerEnum.Event.SYNC_IN_GUS_SF_FAILED
        );
        throw new Error("Invalid event message: Missing applicId or casStatus");
      }

      const opportunity = await this.fetchGusSFDetails(applicId);
      if (!opportunity[0]?.Id) {
        await this.error(
          event,
          `No opportunity found for ${applicId}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${applicId}`);
      }

      await this.log(
        event,
        `Opportunity fetch successful for ${applicId}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      const { offerType, applicationStatus } =
        this.getSfValuesFromCmdStatus(casStatus);

      const opportunityUpdateRequest: Record<string, any> = {
        CASStatus__c: casStatus || "",
        StageName: "Offer",
        OfferType__c: offerType || "",
        Condition__c: condition || "",
        VRFSubmitted__c: vrfSubmitted || "",
        AdmissionsStage__c: applicationStatus || "",
      };

      console.log("opportunityUpdateRequest", opportunityUpdateRequest);

      if (Object.keys(opportunityUpdateRequest).length > 0) {
        await this.log(
          event,
          `Opportunity update initiated with fields: ${Object.keys(
            opportunityUpdateRequest
          ).join(", ")}`,
          loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED
        );
        opportunityId = opportunity[0].Id;
        await this.syncToGus(opportunityUpdateRequest, opportunityId);

        const details = {
          externalApplicationId: applicId,
          gusOpportunityId: opportunityId,
          gusApplicationId: payload?.gusApplicationId || null,
          gusStage: "Offer",
        };
        // Send acknowledgment after successful sync
        await AcknowledgmentService.sendAcknowledgment(
          this.correlationId,
          this.usecase,
          AckStatus.SUCCESS,
          `Stage updated successfully for ${applicId} to Offer`,
          details
        );
        await this.log(
          event,
          `ULAW Campusnet GUS SF Application sync completed`,
          loggerEnum.Event.SYNC_IN_GUS_SF_COMPLETED
        );

        await this.log(
          event,
          `Opportunity update completed successfully`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE
        );
      } else {
        console.log("No valid fields found in the event to update.");
        throw new Error("No valid fields found in the event to update.");
      }
    } catch (error) {
      await this.error(event, error, loggerEnum.Event.SYNC_IN_GUS_SF_FAILED);
      if (retryCount && retryCount > 3) {
        const details = {
          externalApplicationId: applicId,
          gusOpportunityId: opportunityId,
          gusApplicationId: payload?.gusApplicationId || null,
        };

        await handleErrorAcknowledgment(
          this.correlationId,
          this.usecase,
          event,
          error,
          details
        );
      }
      throw error;
    }
  }

  private getSfValuesFromCmdStatus(casStatus: string): SfMappedValues {
    const mapping = casStatusMapping[casStatus];
    if (!mapping) {
      throw new Error(`No SF mapping found for CMD status: ${casStatus}`);
    }
    return mapping;
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
