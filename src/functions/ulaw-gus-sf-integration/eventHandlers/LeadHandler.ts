import { postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { AcknowledgmentService } from "../services/AcknowledgementService";
import { AckStatus } from "../enums/ack-status.enum";
import { v4 as uuid } from "uuid";
import { RegisterHandler } from "../HandlerRegistry";
import { leadFieldMapper } from "../mappers/field-mappings/leadFieldMapper";
import { salesforceCustomMetadata } from "../mappers/metadata/salesforce-custom-fields";
import { handleErrorAcknowledgment } from "../utils/AcknowledgementUtils";
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

@RegisterHandler("ULAW_NEW_LEAD")
export class CreateLeadHandler implements IULAWOutboundEventHandler {
  private correlationId: string;
  private usecase: string = "ULAW_NEW_LEAD";
  private brand: string = "ULAW";
  private applicationFormId: string;
  async fetchGusSFDetails(sid: string): Promise<any> {
    throw new Error("Method not implemented.");
  }

  private transformLeadData(payload: any): any {
    const leadData = {};
    const config = leadFieldMapper;

    for (const [sfField, payloadPath] of Object.entries(config)) {
      if (Array.isArray(payloadPath)) {
        const values = payloadPath.map((path) => {
          return path.split(".").reduce((obj, key) => obj?.[key], payload);
        });
        leadData[sfField.split(".")[1]] = values.join("");
      } else if (typeof payloadPath === "string") {
        const value = payloadPath
          .split(".")
          .reduce((obj, key) => obj?.[key], payload);
        leadData[sfField.split(".")[1]] = value;
      } else {
        leadData[sfField.split(".")[1]] = payloadPath;
      }
    }

    return leadData;
  }

  private async updateCustomFields(payload: any): Promise<any> {
    try {
      const config =
        salesforceCustomMetadata[process.env.stage] ||
        salesforceCustomMetadata.dev;
      const recordTypeIds = config.RECORD_TYPE_IDS;
      payload.Distribute_Automatically__c = 1;
      payload.started_application__c = false;
      payload.RecordTypeId = "123";
      payload.BusinessUnit__c = "ULAW";
      payload.Application_Status__c = "New";
      payload.Status = "New";
      payload.OwnerId = "321";
      payload.Brand__c = "ULAW";
      payload.RecordTypeId = recordTypeIds["Lead"];
      return payload;
    } catch (error) {
      throw new Error(`Error updating custom fields: ${error.message}`);
    }
  }

  async syncToGus(leadData: any): Promise<any> {
    try {
      const response = await postData(
        "gus/lead",
        leadData,
        this.correlationId,
        "tHiTuzRZRn69TsmJP5gEX1sQGsxTsmA2mz1KzPqa"
      );

      return response;
    } catch (error) {
      throw new Error(`Error creating lead in GUS: ${error}`);
    }
  }

  async handleMessage(event: any, retryCount?: number): Promise<any> {
    try {
      this.correlationId = event?.correlationId || uuid();
      this.usecase = event?.scenario || "ULAW_NEW_LEAD";
      await this.log(
        event,
        `ULAW Campusnet GUS SF Lead sync initiated`,
        loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED
      );
      const transformedLeadData = this.transformLeadData(event.payload);
      const updatedLeadData = await this.updateCustomFields(
        transformedLeadData
      );

      console.log("Updated Lead Data:", updatedLeadData);

      const response = await this.syncToGus(updatedLeadData);
      const details = {
        gusLeadId: response?.id,
        externalLeadId: event?.payload?.externalLeadId,
      };

      // Send acknowledgment after successful sync
      await AcknowledgmentService.sendAcknowledgment(
        this.correlationId,
        this.usecase,
        AckStatus.SUCCESS,
        "Lead processed successfully",
        details
      );
      await this.log(
        event,
        `ULAW Campusnet GUS SF Lead sync completed`,
        loggerEnum.Event.SYNC_IN_GUS_SF_COMPLETED
      );
      await this.log(
        event,
        `ULAW Campusnet GUS SF Lead sync completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return response;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      if (retryCount && retryCount > 3) {
        const details = {
          externalLeadId: event?.payload?.externalLeadId,
        };

        await handleErrorAcknowledgment(
          this.correlationId,
          this.usecase,
          event,
          error,
          details
        );
      }
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destinationPayload
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.ULAW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
