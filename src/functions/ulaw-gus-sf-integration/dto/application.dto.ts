// AgentDetails DTO
export const AgentDetailsRequired = [
  "agentAccountId",
  "accountManagerId",
  "agentContactId",
  "agentContactUserId",
  "gusApplicationId",
];

// AccountDetails DTO
export const AccountDetailsRequired = [
  "firstName",
  "dob",
  "email",
  "externalAccountId",
  "gender",
  { phone: ["dialCode", "number"] },
];

// ApplicationDetails DTO
export const ApplicationDetailsRequired = [
  "stageName",
  "programName",
  "primaryLanguage",
  "intake", // assuming this field is present
  "externalApplicationId",
  "applicantId",
];
