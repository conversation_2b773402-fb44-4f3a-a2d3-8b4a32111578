import {
  AgentDetailsRequired,
  AccountDetailsRequired,
  ApplicationDetailsRequired,
} from "../dto/application.dto";

type ErrorDetail = {
  section: string;
  field: string;
  message: string;
};

export function validateEventPayload(event: any): ErrorDetail[] {
  const errors: ErrorDetail[] = [];
  const payload = event.payload ?? {};

  // 1. Agent details validation (only if agentDetails is present & not null/empty)
  if (payload.applicationDetails?.agentDetails) {
    AgentDetailsRequired.forEach((field) => {
      if (!payload.applicationDetails.agentDetails[field]) {
        errors.push({
          section: "agentDetails",
          field,
          message: `${field} is required in agentDetails`,
        });
      }
    });
    // gusApplicationId mandatory for agents
    if (!payload.applicationDetails.gusApplicationId) {
      errors.push({
        section: "applicationDetails",
        field: "gusApplicationId",
        message: "gusApplicationId is required for agent applications",
      });
    }
  }

  // 2. Account details validation (dynamic, supports object keys)
  AccountDetailsRequired.forEach((field) => {
    if (typeof field === "string") {
      // Primitive
      if (
        payload.accountDetails?.[field] === undefined ||
        payload.accountDetails?.[field] === null ||
        payload.accountDetails?.[field] === ""
      ) {
        errors.push({
          section: "accountDetails",
          field,
          message: `${field} is required in accountDetails`,
        });
      }
    } else if (typeof field === "object" && field !== null) {
      // Nested object
      const [parentKey, childFields] = Object.entries(field)[0];
      const parentObj = payload.accountDetails?.[parentKey];
      if (!parentObj || typeof parentObj !== "object") {
        errors.push({
          section: "accountDetails",
          field: parentKey,
          message: `${parentKey} object is required in accountDetails`,
        });
      } else {
        childFields.forEach((childKey: string) => {
          if (
            parentObj[childKey] === undefined ||
            parentObj[childKey] === null ||
            parentObj[childKey] === ""
          ) {
            errors.push({
              section: "accountDetails",
              field: `${parentKey}.${childKey}`,
              message: `${childKey} is required in ${parentKey} object`,
            });
          }
        });
      }
    }
  });

  // 3. Application details validation
  ApplicationDetailsRequired.forEach((field) => {
    if (!payload.applicationDetails?.[field]) {
      errors.push({
        section: "applicationDetails",
        field,
        message: `${field} is required in applicationDetails`,
      });
    }
  });

  return errors;
}
