// PlatformEventHandlerFactory.ts
import { getRegisteredHandler } from "./HandlerRegistry";

// **Ensure all decorated handlers are loaded so they can register themselves**  
import "./eventHandlers/AcceptedHandler";
import "./eventHandlers/ApplicationDeferredHandler";
import "./eventHandlers/EnrollmentHandler";
import "./eventHandlers/LeadHandler";
import "./eventHandlers/AdmissionHandler"
import "./eventHandlers/PaymentHandler";
import "./eventHandlers/FollowUpTaskHandler"
import "./eventHandlers/ApplicationHandler";
import "./eventHandlers/AdmissionHandler";

export class PlatformEventHandlerFactory {
  static getHandler(scenario: string) {
    const handler = getRegisteredHandler(scenario);
    if (!handler) {
      console.error(`No handler found for scenario: ${scenario}`);
    }
    return handler;
  }
}
