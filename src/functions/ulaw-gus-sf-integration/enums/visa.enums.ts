export enum SFOfferType {
  UNCONDITIONAL = "Unconditional",
  CONDITIONAL = "Conditional",
}

const unconditionalStatuses: string[] = [
  "Accepted",
  "Unconditional Acceptance",
  "Clearing Acceptance",
  "Unconditional Acceptance (Partner verification pending)",
];

const conditionalStatuses: string[] = [
  "Conditional Acceptance",
  "Conditional Acceptance (Partner verification pending)",
];

export function getSFOfferTypeFromStatus(
  status: string
): SFOfferType | undefined {
  if (unconditionalStatuses.includes(status)) {
    return SFOfferType.UNCONDITIONAL;
  } else if (conditionalStatuses.includes(status)) {
    return SFOfferType.CONDITIONAL;
  }
  return undefined;
}
