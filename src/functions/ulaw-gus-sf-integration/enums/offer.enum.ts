export enum SfOfferType {
  Unconditional = "Unconditional",
  Conditional = "Conditional",
}

export enum SfApplicationStatus {
  Deferred = "Deferred",
  Offer = "Offer",
  Rejected = "Rejected",
  InfoRequested = "Info Requested",
}

export type SfMappedValues = {
  offerType: SfOfferType;
  applicationStatus: SfApplicationStatus;
};

export const casStatusMapping: Record<string, SfMappedValues> = {
  Deferred: {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Deferred,
  },
  "Deferred Exported": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Deferred,
  },
  "Deferred Offer": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Offer,
  },
  Offer: {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Offer,
  },
  "Offer Pending": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Offer,
  },
  "Deferred Offer Pending": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Offer,
  },
  "Course Full": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Rejected,
  },
  "Unconditional Offer": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Offer,
  },
  "Contacted for Late Offer": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.InfoRequested,
  },
  "Conditional Offer": {
    offerType: SfOfferType.Conditional,
    applicationStatus: SfApplicationStatus.Offer,
  },
  "Conditional Offer Pending": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Offer,
  },
  "Unconditional Offer Pending": {
    offerType: SfOfferType.Unconditional,
    applicationStatus: SfApplicationStatus.Offer,
  },
};
