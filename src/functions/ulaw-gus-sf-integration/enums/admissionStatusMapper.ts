export enum AdmissionStatus {
  APPLICATION = "Application",
  REFERENCE_PENDING = "Reference Pending",
  SELECTION_PANEL = "Selection Panel",
  INFO_REQUESTED = "Info Requested",
  INTERVIEW = "Interview",
}

export const CMD_TO_ADMISSION_STATUS_MAP: Record<string, AdmissionStatus> = {
  "Reference Received": AdmissionStatus.APPLICATION,
  "Reference Pending": AdmissionStatus.REFERENCE_PENDING,
  "Selection Panel": AdmissionStatus.SELECTION_PANEL,
  "Waiting List": AdmissionStatus.APPLICATION,
  "Awaiting Processing": AdmissionStatus.INFO_REQUESTED,
  "Processing Started": AdmissionStatus.APPLICATION,
  "Further Information Requested": AdmissionStatus.INFO_REQUESTED,
  "Invite To Interview": AdmissionStatus.INTERVIEW,
  "Awaiting Sponsorship Confirmation": AdmissionStatus.INFO_REQUESTED,
};

export function getAdmissionStatus(
  cmdStatus: string
): AdmissionStatus | undefined {
  return CMD_TO_ADMISSION_STATUS_MAP[cmdStatus];
}
