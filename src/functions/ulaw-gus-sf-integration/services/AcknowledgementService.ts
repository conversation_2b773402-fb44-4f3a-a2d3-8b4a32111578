import { sendToSQS } from "src/common/sqsService";
import { AckStatus } from "../enums/ack-status.enum";
import { v4 as uuid } from "uuid";

export class AcknowledgmentService {
  static async sendAcknowledgment(
    correlationId: string,
    ackFor: string,
    status: AckStatus,
    message: string,
    details: any,
    scenario: string = "ACK",
  ): Promise<void> {
    const payload = {
      correlationId,
      scenario,
      ackFor,
      timestamp: new Date().toISOString(),
      status,
      message,
      details,
    };

    const queueUrl = process.env.GUS_SF_ULAW_INTEGRATION_SQS_QUEUE_URL;
    const messageDeduplicationId = `${uuid()}-${Date.now()}`;

    await sendToSQS(queueUrl, payload, uuid(), messageDeduplicationId);
    console.log(
      `Acknowledgment sent for ${ackFor} with status ${status}. Correlation ID: ${correlationId}`
    );
  }
}
