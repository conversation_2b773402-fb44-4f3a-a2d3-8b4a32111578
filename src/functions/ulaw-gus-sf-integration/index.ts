import { handlerPath } from "@libs/handler-resolver";

export const ULAWGusSfIntegration = {
  handler: `${handlerPath(
    __dirname
  )}/ULAWGusSfIntegrationService.handleOutboundIntegration`,
  name: "ulaw-gus-sf-integration-${self:provider.stage}",
  events: [
    {
      sqs: {
        arn: "${self:provider.environment.ULAW_GUS_SF_INTEGRATION_SQS_QUEUE_ARN}",
      },
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 180,
  memorySize: 512,
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};

export const ULAWGusSfFailedRecordProcessor = {
  handler: `${handlerPath(
    __dirname
  )}/ULAWGusSfIntegrationService.handleFailedRecords`,
  name: "ulaw-gus-sf-failed-record-processor-${self:provider.stage}",
  events: [
    {
      schedule: "rate(60 minutes)",
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 180,
  memorySize: 512,
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};
