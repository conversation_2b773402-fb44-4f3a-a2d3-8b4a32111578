import { getData, post, postData } from "src/connectors/eip-connector";
import unfcEllucianConnector from "src/connectors/unfc-ellucian-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { getPicklistByField } from "src/common/getPickListValue";
import { v4 as uuid } from 'uuid';

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

export class VisaHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  
  async fetchPersonAccount(personId: string): Promise<any> {
    try {
      await this.log(
        personId,
        `Fetching person account by student external ID initiated`,
        loggerEnum.Event.FETCH_PERSON_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      return await getData(
        `gus/personaccountbystudentexternalid/${personId}`,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        personId,
        `Error fetching person account: ${error}`,
        loggerEnum.Event.FETCH_PERSON_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching person account: ${error}`);
    }
  }
  
  async fetchGusSFDetails(emailId: string): Promise<any> {
    try {
      await this.log(
        emailId,
        `Fetching opportunities by email initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      return await getData(
        `gus/getOpportunities/AccountEmail__c/${emailId}`,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        emailId,
        `Error fetching opportunities by email: ${error}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunities by email: ${error}`);
    }
  }
  
  async checkDocumentExists(opportunityDetails: any, colleaguePersonId: string, sprogramID: string): Promise<boolean> {
    try {
      const fileName = `${uuid()}.pdf`; // Generate UUID and append .pdf
      const payload = {
        s3FilePath: `${opportunityDetails.ApplicationFormId__c}/Visa/${fileName}`,
        ellusionFilePath: `Outbound/${opportunityDetails.ApplicationId__c}_${colleaguePersonId}/${sprogramID}/Visa`,
        method: "GET_DOC"
      };

      await this.log(
        payload,
        `Checking document existence initiated`,
        loggerEnum.Event.DOCUMENT_CHECK_INITIATED,
        loggerEnum.Component.SFTP_CONNECTOR
      );

      const response = await post(
        `eip/unfc/sftp`,
        payload,
        this.correlationId,
        process.env.UNFC_KEY
      );

      await this.log(
        payload,
        `Document check completed successfully`,
        loggerEnum.Event.DOCUMENT_CHECK_COMPLETED,
        loggerEnum.Component.SFTP_CONNECTOR,
        response
      );

      return true;
    } catch (error) {
      await this.log(
        error,
        `Document not found in SFTP server`,
        loggerEnum.Event.DOCUMENT_CHECK_FAILED,
        loggerEnum.Component.SFTP_CONNECTOR
      );
      
      if ((error.error && error.error.includes("FileNotFound")) || 
          (error.error && error.error.includes("DirectoryNotFound"))) {
        return false;
      }
      
      throw error;
    }
  }

  async fetchColleaguePersonId(applicantId: string): Promise<string> {
    try {
      await this.log(
        applicantId,
        `Fetching colleague person ID initiated`,
        loggerEnum.Event.FETCH_PERSON_INITIATED,
        loggerEnum.Component.ELLUCIAN_CONNECTOR
      );

      const response = await unfcEllucianConnector.get(
        `persons/${applicantId}`,
        this.correlationId,
        process.env.UNFC_KEY
      );

      if (!response?.credentials?.length) {
        throw new Error(`No credentials found for applicant ${applicantId}`);
      }

      const colleaguePersonIdObj = response.credentials.find(
        (cred: any) => cred.type === "colleaguePersonId"
      );

      if (!colleaguePersonIdObj) {
        throw new Error(`No colleaguePersonId found for applicant ${applicantId}`);
      }

      await this.log(
        applicantId,
        `Fetching colleague person ID completed`,
        loggerEnum.Event.FETCH_PERSON_COMPLETED,
        loggerEnum.Component.ELLUCIAN_CONNECTOR,
        response
      );

      return colleaguePersonIdObj.value;
    } catch (error) {
      await this.log(
        applicantId,
        `Error fetching colleague person ID: ${error}`,
        loggerEnum.Event.FETCH_PERSON_FAILED,
        loggerEnum.Component.ELLUCIAN_CONNECTOR
      );
      throw new Error(`Error fetching colleague person ID: ${error}`);
    }
  }

  async syncInGus(opportunityId: string, data: any): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        data,
        this.correlationId,
        process.env.UNFC_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.log(
        data,
        `Error syncing opportunity details: ${error}`,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }

  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Inside handle message for visa");
      this.correlationId = event.scenarioName;
      this.usecase = event.scenarioName;
      this.brand = 'UNFC';
      
      // Extract the person ID from the content
      const personId = event.person?.id;
      if (!personId) {
        throw new Error("Person ID not found in the event");
      }
      
      console.log("personId:", personId);
      
      // Fetch person account details
      const accountDetails = await this.fetchPersonAccount(personId);
      if (!accountDetails || !accountDetails.PersonEmail) {
        throw new Error(`No account found or missing email for person ${personId}`);
      }
      
      const emailId = accountDetails.PersonEmail;
      console.log("emailId:", emailId);
      
      // Fetch opportunities by email
      const opportunities = await this.fetchGusSFDetails(emailId);
      if (!opportunities || opportunities.length === 0) {
        throw new Error(`No opportunities found for email ${emailId}`);
      }
      
      // Filter opportunities with StageName as "Payment"
      const paymentOpportunities = opportunities.filter(
        (opp: any) => opp.StageName === "Payment"
      );
      
      if (paymentOpportunities.length === 0) {
        throw new Error(`Requirement not found`);
      }
      
      console.log(`Found ${paymentOpportunities.length} opportunities in Payment stage`);
      
      // Check if the event contains visa details
      if (!event.visaId || !event.visaIssueDate || !event.visaExpiryDate) {
        throw new Error("Visa details not found in the event");
      }
      
      // Get visa type from picklist if visaType.detail.id exists
      let visaType = null;
      if (event.visaType?.detail?.id) {
        const visaTypes = await getPicklistByField("UNFC_GUS_SF", "visaType");
        visaType = visaTypes ? visaTypes[event.visaType.detail.id] : null;
        console.log("Mapped Visa Type:", visaType);
      }
      
      // Get colleaguePersonId for document check
      const colleaguePersonId = await this.fetchColleaguePersonId(personId);
      
      // Process each eligible opportunity
      const results = [];
      for (const opportunity of paymentOpportunities) {
        this.applicationFormId = opportunity.Application_Form_Id__c;
        
        // Check if document exists
        const documentExists = await this.checkDocumentExists(opportunity, colleaguePersonId, opportunity.ApplicId__c);
        if (!documentExists) {
          await this.log(
            event,
            `Requirement not found for opportunity ${opportunity.Id}`,
            loggerEnum.Event.REQUIREMENT_NOT_FOUND,
            loggerEnum.Component.GUS_SALESFORCE
          );
          throw new Error(`Requirement not found`);
        }
        
        // Prepare the data to update the opportunity
        const opportunityData = {
          StageName: "Visa",
          Visa_Status__c: "Approved",
          VisaIssueDate__c: event.visaIssueDate,
          VisaExpiryDate__c: event.visaExpiryDate,
          VisaType__c: visaType
        };
        
        await this.log(
          event,
          `sync opportunity details initiated`,
          loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          opportunityData
        );
        
        // Update the opportunity in Salesforce
        const response = await this.syncInGus(
          opportunity.Id,
          opportunityData
        );
        
        await this.log(
          event,
          `sync opportunity details completed`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          opportunityData,
          response
        );
        
        results.push(response);
      }
      
      console.log("Results:", results);
      return results;
    } catch (error) {
      console.log("Error:", error);
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      "",
      "Application_Form_Id__c",
      this.applicationFormId,
      "",
      "",
      "",
      this.applicationFormId,
      response
    );
  }
}
