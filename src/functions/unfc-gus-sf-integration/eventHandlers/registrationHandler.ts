import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

export class Regis<PERSON>Handler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;

  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      await this.log(
        sid,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        sid,
        `Error fetching opportunity details: ${error}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }

  async syncInGus(opportunityId: string, data: any): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        data,
        this.correlationId,
        process.env.UNFC_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.log(
        data,
        `Error syncing opportunity details: ${error}`,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }

  async handleMessage(event: any): Promise<any> {
    try {
      this.correlationId = event.scenarioName;
      this.usecase = event.scenarioName;
      this.brand = 'UNFC';

      // Extract the application ID (sprogramID) from the content
      const sprogramID = event.content.id;

      // Fetch opportunity details using the sprogramID
      await this.log(
        event,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID
      );

      const opportunity = await this.fetchGusSFDetails(sprogramID);
      this.applicationFormId = opportunity[0].Application_Form_Id__c;

      if (!opportunity?.length) {
        await this.log(
          event,
          `No opportunity found for ${sprogramID}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${sprogramID}`);
      }

      await this.log(
        event,
        `fetch opportunity by sprogramID completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID,
        opportunity
      );

      // Prepare the data to update the opportunity
      const opportunityData = {
        StageName: "Closed Won"
      };

      await this.log(
        event,
        `sync opportunity details initiated`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityData
      );

      // Update the opportunity in Salesforce
      const response = await this.syncInGus(
        opportunity[0].Id,
        opportunityData
      );

      await this.log(
        event,
        `sync opportunity details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityData,
        response
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      "UNFC_GUS_SALESFORCE_INTEGRATION_HANDLER",
      "UNFC_GUS_SALESFORCE_INTEGRATION_QUEUE",
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      "",
      "Application_Form_Id__c",
      this.applicationFormId,
      "",
      "",
      "",
      this.applicationFormId,
      response
    );
  }
} 