import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

export class ApplicationAssessmentHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;

  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      await this.log(
        sid,
        `Fetching opportunity by application ID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        sid,
        `Error fetching opportunity by application ID: ${error}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity by application ID: ${error}`);
    }
  }

  async syncInGus(event: any, updateById?: any, correlationId?: string): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        correlationId || this.correlationId,
        process.env.UNFC_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.log(
        event,
        `Error syncing opportunity details: ${error}`,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }

  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Inside handle message for application assessment");
      this.correlationId = event.scenarioName;
      this.usecase = event.scenarioName;
      this.brand = 'UNFC';

      const sprogramID = event.content.id;
      
      console.log("sprogramID:", sprogramID);
      await this.log(
        event,
        `Fetching opportunity by application ID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID
      );
      // Fetch opportunity details
      const opportunities = await this.fetchGusSFDetails(sprogramID);
      await this.log(
        event,
        `Fetching opportunity by application ID completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID,
        opportunities
      );
      if (!opportunities || opportunities.length === 0) {
        throw new Error(`No opportunity found for application ID: ${sprogramID}`);
      }

      const opportunity = opportunities[0];


      const updateData = {
        AdmissionsStage__c: "Partner – Referred"
      };

      await this.log(
        event,
        `Initiating update of opportunity stage to Partner Referred`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        updateData
      );
      // Update the opportunity
      await this.syncInGus(updateData, opportunity.Id);

      await this.log(
        event,
        `Successfully updated opportunity stage to Partner Referred`,
        loggerEnum.Event.SYNC_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        updateData
      );

      return {
        status: "success",
        message: "Application Assessment processed successfully"
      };
    } catch (error) {
      await this.log(
        event,
        `Error processing application assessment: ${error}`,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw error;
    }
  }

  async log(
    sourcePayload: any,
    logMessage: string,
    event: string,
    destination?: string,
    destinationPayload?: any,
    response?: any
  ): Promise<any> {
    return await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      'UNFC-GUS-SF-INTEGRATION',
      'UNFC',
      destination || 'GUS-SALESFORCE',
      event,
      this.usecase,
      sourcePayload,
      destinationPayload,
      logMessage,
      this.brand,
      this.applicationFormId,
      'applicationId',
      this.applicationFormId,
      'Opportunity',
      response?.Id,
      'Application',
      this.applicationFormId,
      response
    );
  }
} 