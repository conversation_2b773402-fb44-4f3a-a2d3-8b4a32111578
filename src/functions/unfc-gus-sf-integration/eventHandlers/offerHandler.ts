import { getData, post, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import unfcEllucianConnector from "src/connectors/unfc-ellucian-connector";
import { v4 as uuid } from 'uuid';

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

export class OfferHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      await this.log(
        sid,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        sid,
        `Error fetching opportunity details: ${error}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }

  async fetchConditionalRemarks(applicationId: string): Promise<any> {
    try {
      const criteria = {
        applicationId: applicationId,
        remarkDetails: {
          type: "Conditional"
        }
      };
      
      // Use the unfc-ellucian-connector to make the API call
      return await unfcEllucianConnector.get(
        "unf-appl-remarks", 
        null, 
        { criteria: JSON.stringify(criteria) }
      );
    } catch (error) {
      await this.log(
        applicationId,
        `Error fetching conditional offer remarks: ${error}`,
        loggerEnum.Event.FETCH_REMARKS_FAILED,
        loggerEnum.Component.ELLUCIAN_API
      );
      throw new Error(`Error fetching conditional offer remarks: ${error}`);
    }
  }

  async syncInGus(opportunityId: string, data: any): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        data,
        this.correlationId,
        process.env.UNFC_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.log(
        data,
        `Error syncing opportunity details: ${error}`,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }

  async fetchColleaguePersonId(applicantId: string): Promise<string> {
    try {
      await this.log(
        applicantId,
        `Fetching colleague person ID initiated`,
        loggerEnum.Event.FETCH_PERSON_INITIATED,
        loggerEnum.Component.ELLUCIAN_CONNECTOR
      );

      const response = await unfcEllucianConnector.get(
        `persons/${applicantId}`,
      );

      if (!response?.credentials?.length) {
        throw new Error(`No credentials found for applicant ${applicantId}`);
      }

      const colleaguePersonIdObj = response.credentials.find(
        (cred: any) => cred.type === "colleaguePersonId"
      );

      if (!colleaguePersonIdObj) {
        throw new Error(`No colleaguePersonId found for applicant ${applicantId}`);
      }

      await this.log(
        applicantId,
        `Fetching colleague person ID completed`,
        loggerEnum.Event.FETCH_PERSON_COMPLETED,
        loggerEnum.Component.ELLUCIAN_CONNECTOR,
        response
      );

      return colleaguePersonIdObj.value;
    } catch (error) {
      await this.log(
        applicantId,
        `Error fetching colleague person ID: ${error}`,
        loggerEnum.Event.FETCH_PERSON_FAILED,
        loggerEnum.Component.ELLUCIAN_CONNECTOR
      );
      throw new Error(`Error fetching colleague person ID: ${error}`);
    }
  }

  async checkDocumentExists(opportunityDetails: any, colleaguePersonId: string, sprogramID: string): Promise<boolean> {
    try {
      const fileName = `${sprogramID}_offer_letter.pdf`;
      const payload = {
        s3FilePath: `${opportunityDetails.ApplicationFormId__c}/Offer letter/${fileName}`,
        ellusionFilePath: `Outbound/${opportunityDetails.ApplicationId__c}_${colleaguePersonId}/${sprogramID}/Offer Letter`,
        method: "GET_DOC"
      };

      await this.log(
        payload,
        `Checking document existence initiated`,
        loggerEnum.Event.DOCUMENT_CHECK_INITIATED,
        loggerEnum.Component.SFTP_CONNECTOR
      );

      const response = await post(
        `eip/unfc/sftp`,
        payload,
        this.correlationId,
        process.env.UNFC_KEY
      );

      await this.log(
        payload,
        `Document check completed successfully`,
        loggerEnum.Event.DOCUMENT_CHECK_COMPLETED,
        loggerEnum.Component.SFTP_CONNECTOR,
        response
      );

      return true;
    } catch (error) {
      console.log("Error checking document ->", error);
      await this.log(
        error,
        `Document not found in SFTP server`,
        loggerEnum.Event.DOCUMENT_CHECK_FAILED,
        loggerEnum.Component.SFTP_CONNECTOR
      );
      
      // Check for both FileNotFound and DirectoryNotFound errors
      if ((error.error && error.error.includes("FileNotFound")) || 
          (error.error && error.error.includes("DirectoryNotFound"))) {
        return false;
      }
      throw error;
    }
  }

  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Inside handle message for offer");
      this.correlationId = event.scenarioName;
      this.usecase = event.scenarioName;
      this.brand = 'UNFC';
      
      // Extract the application ID (sprogramID) from the content
      const sprogramID = event.content.id;
      console.log("sprogramID:", sprogramID);

      const applicationsId = event.content.applicationsId;
      console.log("Applications ID:", applicationsId);

      await this.log(
        event,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID
      );
      
      const opportunity = await this.fetchGusSFDetails(sprogramID);
      this.applicationFormId = opportunity[0].Application_Form_Id__c;
      console.log("Opportunity:", opportunity[0]);
      if (!opportunity?.length) {
        await this.log(
          event,
          `No opportunity found for ${sprogramID}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${sprogramID}`);
      }
      
      await this.log(
        event,
        `fetch opportunity by sprogramID completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID,
        opportunity
      );
      
      // Fetch colleague person ID
      const colleaguePersonId = await this.fetchColleaguePersonId(event.content.applicant.id);
      
      // Check if document exists in SFTP server
      const documentExists = await this.checkDocumentExists(opportunity[0], colleaguePersonId, sprogramID);
      
      if (!documentExists) {
        throw new Error("Requirement not found");
      }

      // Sync document details
      const documentUpdateRequest = {
        DocumentType__c: "Offer letter",
        ApplicationId__c: opportunity[0].ApplicationFormId__c,
        Name: `${sprogramID}_offer_letter.pdf`,
        FilePath__c: `${opportunity[0].ApplicationFormId__c}/Offer letter/${sprogramID}_offer_letter.pdf`,
        Opportunity__c: opportunity[0].Id,
        FullUrl__c: `${opportunity[0].ApplicationFormId__c}/Offer letter/${sprogramID}_offer_letter.pdf`,
        OriginalValue__c: `${sprogramID}_offer_letter.pdf`,
        S3FileName__c: `${opportunity[0].ApplicationFormId__c}/Offer letter/${sprogramID}_offer_letter.pdf`,
        BucketName__c: process.env.REVIEW_CENTER_BUCKET_NAME,
        DocumentSource__c: "UNFC",
        Status__c: "Accepted",
      };

      await this.log(
        event,
        `Syncing document for offer letter`,
        loggerEnum.Event.SYNC_DOCUMENT_REQUEST,
        loggerEnum.Component.GUS_SALESFORCE,
        documentUpdateRequest,
        "OpportunityFile__c"
      );

      await this.syncGusDocument(documentUpdateRequest);

      await this.log(
        event,
        `sync document details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        "OpportunityFile__c"
      );

      // Fetch conditional remarks
      const remarks = await this.fetchConditionalRemarks(applicationsId);

      // Prepare opportunity data based on remarks
      const opportunityData: any = {
        StageName: "Offer"
      };

      if (remarks && remarks.length > 0) {
        let offerRemarks; 
        const textArray = remarks[0].remarkDetails?.text || [];
        if (textArray.length > 0) {
          offerRemarks = textArray.join(", ");
        }
        // If remarks exist, it's a conditional offer
        opportunityData.AdmissionsStage__c = "Conditional Offer";
        opportunityData.Admissions_Condition__c = offerRemarks;
      } else {
        // If no remarks, it's an unconditional offer
        opportunityData.AdmissionsStage__c = "Unconditional Offer";
      }
      
      await this.log(
        event,
        `sync opportunity details initiated`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityData
      );
      
      // Update the opportunity in Salesforce
      const response = await this.syncInGus(
        opportunity[0].Id,
        opportunityData
      );
      
      await this.log(
        event,
        `sync opportunity details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityData,
        response
      );
      
      console.log("Response:", response);
      return response;
    } catch (error) {
      console.log("Error:", error);
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      "",
      "Application_Form_Id__c",
      this.applicationFormId,
      "",
      "",
      "",
      this.applicationFormId,
      response
    );
  }

  async syncGusDocument(documentUpdateRequest) {
    try {
      await postData(
        `gus/opportunityfile`,
        documentUpdateRequest,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        documentUpdateRequest,
        `Error syncing document details: ${error}`,
        loggerEnum.Event.SYNC_DOCUMENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing document details: ${error}`);
    }
  }
} 