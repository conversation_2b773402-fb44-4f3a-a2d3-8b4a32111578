import { getData, postData } from "src/connectors/eip-connector";
import unfcEllucianConnector from "src/connectors/unfc-ellucian-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

export class FurtherClarificationHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      await this.log(
        sid,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        sid,
        `Error fetching opportunity details: ${error}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }

  async fetchClarificationRemarks(applicationId: string): Promise<any> {
    try {
      const criteria = {
        applicationId: applicationId,
        remarkDetails: {
          type: "Further Clarification Req"
        }
      };
      
      return await unfcEllucianConnector.get(
        "unf-appl-remarks", 
        null, 
        { criteria: JSON.stringify(criteria) }
      );
    } catch (error) {
      await this.log(
        applicationId,
        `Error fetching clarification remarks: ${error}`,
        loggerEnum.Event.FETCH_REMARKS_FAILED,
        loggerEnum.Component.ELLUCIAN_API
      );
      throw new Error(`Error fetching clarification remarks: ${error}`);
    }
  }

  async syncInGus(opportunityId: string, data: any): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        data,
        this.correlationId,
        process.env.UNFC_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.log(
        data,
        `Error syncing opportunity details: ${error}`,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }

  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Inside handle message for further clarification required");
      this.correlationId = event.scenarioName;
      this.usecase = event.scenarioName;
      this.brand = 'UNFC';

      const sprogramID = event.content.id;
      console.log("sprogramID:", sprogramID);
      
      const applicationsId = event.content.applicationsId;
      console.log("Applications ID:", applicationsId);
      
      await this.log(
        event,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID
      );
      
      const opportunity = await this.fetchGusSFDetails(sprogramID);
      this.applicationFormId = opportunity[0].Application_Form_Id__c;
      
      if (!opportunity?.length) {
        await this.log(
          event,
          `No opportunity found for ${sprogramID}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${sprogramID}`);
      }
      
      await this.log(
        event,
        `fetch opportunity by sprogramID completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID,
        opportunity
      );

      await this.log(
        event,
        `fetch clarification remarks initiated`,
        loggerEnum.Event.FETCH_REMARKS_INITIATED,
        loggerEnum.Component.ELLUCIAN_API,
        { applicationId: applicationsId }
      );
      
      const clarificationRemarks = await this.fetchClarificationRemarks(applicationsId);
      console.log("Clarification Remarks:", clarificationRemarks);
      
      await this.log(
        event,
        `fetch clarification remarks completed`,
        loggerEnum.Event.FETCH_REMARKS_COMPLETED,
        loggerEnum.Component.ELLUCIAN_API,
        { applicationId: applicationsId },
        clarificationRemarks
      );
      
      let clarificationReason; 
      
      if (clarificationRemarks && clarificationRemarks.length > 0) {
        const textArray = clarificationRemarks[0].remarkDetails?.text || [];
        if (textArray.length > 0) {
          clarificationReason = textArray.join(", ");
        }
      }
      
      console.log("Clarification Reason:", clarificationReason);

      const opportunityData = {
        AdmissionsStage__c: "Further clarification required",
        Admissions_Condition__c: clarificationReason
      };
      
      await this.log(
        event,
        `sync opportunity details initiated`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityData
      );

      const response = await this.syncInGus(
        opportunity[0].Id,
        opportunityData
      );
      
      await this.log(
        event,
        `sync opportunity details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityData,
        response
      );
      
      console.log("Response:", response);
      return response;
    } catch (error) {
      console.log("Error:", error);
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      "",
      "Application_Form_Id__c",
      this.applicationFormId,
      "",
      "",
      "",
      this.applicationFormId,
      response
    );
  }
}
