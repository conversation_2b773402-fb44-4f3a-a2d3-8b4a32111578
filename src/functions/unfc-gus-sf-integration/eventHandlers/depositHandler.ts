import { getData, post, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import unfcEllucianConnector from "src/connectors/unfc-ellucian-connector";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

export class DepositHandler implements GusEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  
  async fetchGusSFDetails(sid: string): Promise<any> {
    try {
      await this.log(
        sid,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      return await getData(
        `gus/getOpportunities/ApplicId__c/${sid}`,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        sid,
        `Error fetching opportunity details: ${error}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching opportunity details: ${error}`);
    }
  }

  async fetchColleaguePersonId(applicantId: string): Promise<string> {
    try {
      await this.log(
        applicantId,
        `Fetching colleague person ID initiated`,
        loggerEnum.Event.FETCH_VERSION_DATA_INITIATED,
        loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR
      );

      const response = await unfcEllucianConnector.get(
        `persons/${applicantId}`,
      );

      if (!response?.credentials?.length) {
        throw new Error(`No credentials found for applicant ${applicantId}`);
      }

      const colleaguePersonIdObj = response.credentials.find(
        (cred: any) => cred.type === "colleaguePersonId"
      );

      if (!colleaguePersonIdObj) {
        throw new Error(`No colleaguePersonId found for applicant ${applicantId}`);
      }

      await this.log(
        applicantId,
        `Fetching colleague person ID completed`,
        loggerEnum.Event.FETCH_VERSION_DATA_COMPLETED,
        loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
        response
      );

      return colleaguePersonIdObj.value;
    } catch (error) {
      await this.log(
        applicantId,
        `Error fetching colleague person ID: ${error}`,
        loggerEnum.Event.FETCH_VERSION_DATA_INITIATED,
        loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR
      );
      throw new Error(`Error fetching colleague person ID: ${error}`);
    }
  }

  async checkDocumentExists(opportunityDetails: any, colleaguePersonId: string, sprogramID: string, documentType: 'LOA' | 'PAL'): Promise<boolean> {
    try {
      const fileName = documentType === 'LOA' ? 
        `${sprogramID}_letter_of_acceptance.pdf` : 
        `${sprogramID}_provisional_attestation_letter.pdf`;
      
      const documentFolder = documentType === 'LOA' ? 'Letter of Acceptance' : 'Provincial Attestation Letter';
      const ellucianDocumentFolder = documentType === 'LOA' ? 'Letter of Acceptance' : 'Provisional Attestation Letter';
      
      const payload = {
        s3FilePath: `${opportunityDetails.ApplicationFormId__c}/${documentFolder}/${fileName}`,
        ellusionFilePath: `Outbound/${opportunityDetails.ApplicationId__c}_${colleaguePersonId}/${sprogramID}/${ellucianDocumentFolder}`,
        method: "GET_DOC"
      };

      await this.log(
        payload,
        `Checking ${documentType} document existence initiated`,
        loggerEnum.Event.DOCUMENT_CLOSURE_INITIATED,
        loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR
      );

      const response = await post(
        `eip/unfc/sftp`,
        payload,
        this.correlationId,
        process.env.UNFC_KEY
      );

      await this.log(
        payload,
        `${documentType} document check completed successfully`,
        loggerEnum.Event.DOCUMENT_UPLOAD_COMPLETED,
        loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
        response
      );

      return true;
    } catch (error) {
      console.log(`Error checking ${documentType} document ->`, error);
      await this.log(
        error,
        `${documentType} document not found in SFTP server`,
        loggerEnum.Event.DOCUMENT_CLOSURE_INITIATED,
        loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR
      );
      
      if ((error.error && error.error.includes("FileNotFound")) || 
          (error.error && error.error.includes("DirectoryNotFound"))) {
        return false;
      }
      throw error;
    }
  }

  async syncInGus(opportunityId: string, data: any): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        data,
        this.correlationId,
        process.env.UNFC_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.log(
        data,
        `Error syncing opportunity details: ${error}`,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }

  async syncGusDocument(documentUpdateRequest) {
    try {
      await postData(
        `gus/opportunityfile`,
        documentUpdateRequest,
        this.correlationId,
        process.env.UNFC_KEY
      );
    } catch (error) {
      await this.log(
        documentUpdateRequest,
        `Error syncing document details: ${error}`,
        loggerEnum.Event.SYNC_DOCUMENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing document details: ${error}`);
    }
  }

  async checkExistingLOAFile(opportunity: any, sprogramID: string): Promise<boolean> {
    try {
      const s3FileName = `${opportunity.ApplicationFormId__c}/Letter of Acceptance/${sprogramID}_letter_of_acceptance.pdf`;
      console.log("s3FileName:", s3FileName);
      await this.log(
        { s3FileName, opportunityId: opportunity.Id },
        `Checking existing LOA file in GUS`,
        loggerEnum.Event.DOCUMENT_CLOSURE_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      const response = await getData(
        `gus/opportunityfilesByS3Filename?s3FileName=${encodeURIComponent(s3FileName)}&opportunityId=${opportunity.Id}&documentType=Letter of Acceptance`,
        this.correlationId,
        process.env.UNFC_KEY
      );
      console.log("response:", response);
      await this.log(
        { s3FileName, opportunityId: opportunity.Id },
        `Existing LOA file check completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        response
      );

      return response && response.length > 0;
    } catch (error) {
      await this.log(
        { opportunityId: opportunity.Id },
        `Error checking existing LOA file: ${error}`,
        loggerEnum.Event.DOCUMENT_CLOSURE_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error checking existing LOA file: ${error}`);
    }
  }

  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Inside handle message for deposit");
      this.correlationId = event.scenarioName;
      this.usecase = event.scenarioName;
      this.brand = 'UNFC';
      
      const sprogramID = event.content.id;
      console.log("sprogramID:", sprogramID);
      
      await this.log(
        event,
        `fetch opportunity by sprogramID initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID
      );
      
      const opportunity = await this.fetchGusSFDetails(sprogramID);
      console.log("opportunity:", opportunity);
      this.applicationFormId = opportunity[0].Application_Form_Id__c;
      if (!opportunity?.length) {
        await this.log(
          event,
          `No opportunity found for ${sprogramID}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${sprogramID}`);
      }
      
      await this.log(
        event,
        `fetch opportunity by sprogramID completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        sprogramID,
        opportunity
      );

      const colleaguePersonId = await this.fetchColleaguePersonId(event.content.applicant.id);
      console.log("colleaguePersonId:", colleaguePersonId);

      const existingLOA = await this.checkExistingLOAFile(opportunity[0], sprogramID);
      console.log("existingLOA:", existingLOA);
      if (!existingLOA) {
        const loaExists = await this.checkDocumentExists(opportunity[0], colleaguePersonId, sprogramID, 'LOA');
        if (!loaExists) {
          await this.log(
            event,
            `LOA document not found for ${sprogramID}`,
            loggerEnum.Event.DOCUMENT_CLOSURE_INITIATED,
            loggerEnum.Component.GUS_SALESFORCE
          );
          throw new Error("Requirement not found");
        }

        // Sync LOA document
        const loaDocumentUpdateRequest = {
          DocumentType__c: "Letter of Acceptance",
          ApplicationId__c: opportunity[0].ApplicationFormId__c,
          Name: `${sprogramID}_letter_of_acceptance.pdf`,
          FilePath__c: `${opportunity[0].ApplicationFormId__c}/Letter of Acceptance/${sprogramID}_letter_of_acceptance.pdf`,
          Opportunity__c: opportunity[0].Id,
          FullUrl__c: `${opportunity[0].ApplicationFormId__c}/Letter of Acceptance/${sprogramID}_letter_of_acceptance.pdf`,
          OriginalValue__c: `${sprogramID}_letter_of_acceptance.pdf`,
          S3FileName__c: `${opportunity[0].ApplicationFormId__c}/Letter of Acceptance/${sprogramID}_letter_of_acceptance.pdf`,
          BucketName__c: process.env.REVIEW_CENTER_BUCKET_NAME,
          DocumentSource__c: "UNFC",
          Status__c: "Accepted",
        };

        await this.log(
          event,
          `Syncing LOA document`,
          loggerEnum.Event.SYNC_DOCUMENT_REQUEST,
          loggerEnum.Component.GUS_SALESFORCE,
          loaDocumentUpdateRequest,
          "OpportunityFile__c"
        );

        await this.syncGusDocument(loaDocumentUpdateRequest);
        
        const opportunityData = {
          StageName: "Payment",
          DateMDA__c: event?.content?.n60AppDepDate,
          AmountMDA__c: event?.content?.n60AppDepArdAmt
        };
        
        await this.log(
          event,
          `sync opportunity details initiated`,
          loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          opportunityData
        );
        
        const response = await this.syncInGus(
          opportunity[0].Id,
          opportunityData
        );
        
        await this.log(
          event,
          `sync opportunity details completed`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          opportunityData,
          response
        );
      } else {
        await this.log(
          event,
          `LOA document already exists in GUS, skipping LOA processing`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE
        );
      }
      
      // Check and sync PAL document
      const palExists = await this.checkDocumentExists(opportunity[0], colleaguePersonId, sprogramID, 'PAL');
      if (!palExists) {
        await this.log(
          event,
          `PAL document not found for ${sprogramID}`,
          loggerEnum.Event.DOCUMENT_CLOSURE_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error("Requirement not found");
      }

      // Sync PAL document
      const palDocumentUpdateRequest = {
        DocumentType__c: "Provincial Attestation Letter",
        ApplicationId__c: opportunity[0].ApplicationFormId__c,
        Name: `${sprogramID}_provisional_attestation_letter.pdf`,
        FilePath__c: `${opportunity[0].ApplicationFormId__c}/Provincial Attestation Letter/${sprogramID}_provisional_attestation_letter.pdf`,
        Opportunity__c: opportunity[0].Id,
        FullUrl__c: `${opportunity[0].ApplicationFormId__c}/Provincial Attestation Letter/${sprogramID}_provisional_attestation_letter.pdf`,
        OriginalValue__c: `${sprogramID}_provisional_attestation_letter.pdf`,
        S3FileName__c: `${opportunity[0].ApplicationFormId__c}/Provincial Attestation Letter/${sprogramID}_provisional_attestation_letter.pdf`,
        BucketName__c: process.env.REVIEW_CENTER_BUCKET_NAME,
        DocumentSource__c: "UNFC",
        Status__c: "Accepted",
      };

      await this.log(
        event,
        `Syncing PAL document`,
        loggerEnum.Event.SYNC_DOCUMENT_REQUEST,
        loggerEnum.Component.GUS_SALESFORCE,
        palDocumentUpdateRequest,
        "OpportunityFile__c"
      );

      await this.syncGusDocument(palDocumentUpdateRequest);
      
      return { success: true };
    } catch (error) {
      console.log("Error:", error);
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.MYUCW_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      "",
      "Application_Form_Id__c",
      this.applicationFormId,
      "",
      "",
      "",
      this.applicationFormId,
      response
    );
  }
}