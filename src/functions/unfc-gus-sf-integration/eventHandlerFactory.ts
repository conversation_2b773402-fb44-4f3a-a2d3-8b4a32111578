import { WithdrawnApplicationHandler } from "./eventHandlers/withdrawnApplicationHandler";
import { DeniedApplicationHandler } from "./eventHandlers/deniedApplicationHandler";
import { FurtherClarificationHandler } from "./eventHandlers/furtherClarificationHandler";
import { RegistrationHandler } from "./eventHandlers/registrationHandler";
import { DeferredHandler } from "./eventHandlers/deferredHandler";
import { VisaHandler } from "./eventHandlers/visaHandler";
import { DepositHandler } from "./eventHandlers/depositHandler";
import { ApplicationAssessmentHandler } from "./eventHandlers/applicationAssessmentHandler";
import { AppealsCommitteeHandler } from "./eventHandlers/appealsCommitteeHandler";
import { OfferHandler } from "./eventHandlers/offerHandler";

export class EventHandlerFactory {
  static getHandler(scenario): GusEventsHandler {
    switch (scenario) {
      case "UNFC_WITHDRAWN_APPLICATION":
        return new WithdrawnApplicationHandler();
      case "UNFC_DENIED_APPLICATION":
        return new DeniedApplicationHandler();
      case "UNFC_FURTHER_CLARIFICATION_REQUIRED":
        return new FurtherClarificationHandler();
      case "UNFC_OFFER_APPROVED":
        return new OfferHandler();
      case "UNFC_REGISTRATION":
        return new RegistrationHandler();
      case "UNFC_APPLICATION_DEFERRED":
        return new DeferredHandler();
      case "UNFC_VISA_APPROVED":
        return new VisaHandler();
      case "UNFC_DEPOSIT_CONFIRMED":
        return new DepositHandler();
      case "UNFC_APPLICATION_ASSESSMENT":
        return new ApplicationAssessmentHandler();
      case "UNFC_APPEALS_COMMITTEE":
        return new AppealsCommitteeHandler();
      default:
        break;
    }
  }
}
