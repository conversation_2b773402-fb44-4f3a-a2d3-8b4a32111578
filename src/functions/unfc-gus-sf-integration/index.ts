import { handlerPath } from "@libs/handler-resolver";

export const unfcGusSfIntegration = {
  handler: `${handlerPath(
    __dirname
  )}/unfcGusSfIntegrationService.handleSfRequests`,
  name: "unfc-gus-sf-integration-${self:provider.stage}",
  events: [
    {
      sqs: {
        arn: "${self:provider.environment.UNFC_GUS_SF_INTEGRATION_SQS_QUEUE_ARN}",
      },
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 180,
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};

export const unfcGusSfIntegrationFailedRecordProcessor = {
    handler: `${handlerPath(__dirname)}/unfcGusSfIntegrationService.handleFailedRecords`,
    name: 'unfc-gus-sf-failed-record-processor-${self:provider.stage}',
    events: [
        {
            schedule: 'rate(60 minutes)',
        },
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 180,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};

export const unfcGusSfIntegrationBacklogEventProcessor = {
  handler: `${handlerPath(__dirname)}/unfcGusSfIntegrationService.handleBacklogRecords`,
  name: 'unfc-gus-sf-backlog-event-processor-${self:provider.stage}',
  events: [
      {
          schedule: 'rate(270 minutes)',
      },
  ],
  role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
  timeout: 180,
  tags: {
      PROJECT: "EIP",
      ENVIRONMENT: "${self:provider.stage}",
      TEAM: "EIP Development Team"
  }
};
