import { DynamoDBService } from "src/common/dynamodbService";
import { EventHandlerFactory } from "./eventHandlerFactory";
import { storeFailedRecordsQueue } from "src/common/storeFailedRecords";
import { checkApplicationIdExist } from "src/common/checkFailedRecords";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { ScenarioMapping } from "./enums/scenario-mapping.enum";
import { SnsService } from "src/common/snsService";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const dbService = new DynamoDBService();
const snsService = new SnsService();

let correlationId: string;
let usecase: string;
let applicationId: string;
let brand: string;

function getMappedScenario(statusCode: string): string {
  return ScenarioMapping[statusCode] || statusCode;
}

export const handleSfRequests = async (event) => {
  const responses = [];
  console.log("Event received -->", event);

  for (const record of event.Records) {
    try {
      const eventBody = JSON.parse(record.body);
      console.log("eventBody", eventBody);

      let ellucianEventMessages = eventBody.Message
        ? JSON.parse(eventBody.Message)
        : eventBody;
      
      if (ellucianEventMessages.content?.visaId) {
        ellucianEventMessages.content.currentStatus = 'X-VS';
      }
      
      console.log("Processing message:", ellucianEventMessages);
      
      correlationId = ellucianEventMessages.id || record.messageId;

      const currentStatus = ellucianEventMessages.content?.currentStatus;
      if (!currentStatus) {
        console.log("No current status found in the message");
        continue;
      }
      
      usecase = getMappedScenario(currentStatus);
      applicationId = ellucianEventMessages.content?.id;
      brand = "UNFC";
      
      if (!applicationId) {
        console.log("No application ID found in the message");
        continue;
      }

      await loggerService.log(
        correlationId,
        new Date().toISOString(),
        "UNFC_GUS_SALESFORCE_INTEGRATION_HANDLER",
        "UNFC_GUS_SALESFORCE_INTEGRATION_QUEUE",
        loggerEnum.Component.GUS_SALESFORCE,
        loggerEnum.Event.EVENT_INITIATED,
        usecase,
        record,
        {},
        "event initiated",
        brand,
        applicationId,
        "Applic_Id__c",
        applicationId,
        "",
        "",
        "",
        applicationId
      );

      const isFailedMessageGroupData = await checkApplicationIdExist(
        record,
        applicationId,
        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
        "UNFC_GUS_SF"
      );

      if (isFailedMessageGroupData === "No messages to process") {
        try {
          const handlerData = {
            scenarioName: usecase,
            gusApplicationId: applicationId,
            content: ellucianEventMessages.content
          };
          
          console.log("Handler data:", handlerData);

          const handler = EventHandlerFactory.getHandler(usecase);

          if (handler && typeof handler.handleMessage === "function") {
            try {
              const response = await handler.handleMessage(handlerData);
              console.log("Response", response);

              if (response && ellucianEventMessages.status === "Failed") {
                await dbService.deleteItem(
                  process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                  {
                    PK: `UNFC_GUS_SF#${applicationId}`,
                    SK: ellucianEventMessages?.id || record?.messageId,
                  }
                );

                const queryParams = {
                  TableName:
                    process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                  KeyConditionExpression: "PK = :pkValue",
                  ExpressionAttributeValues: {
                    ":pkValue": `UNFC_GUS_SF#${applicationId}`,
                  },
                };

                const checkExistingFailedRecordForMessageGrpId =
                  await dbService.queryObjects(queryParams);
                console.log(
                  "checkExistingFailedRecordForMessageGrpId -->",
                  checkExistingFailedRecordForMessageGrpId
                );

                if (checkExistingFailedRecordForMessageGrpId.Items.length === 0) {
                  await dbService.deleteItem(
                    process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                    {
                      PK: "UNFC_GUS_SF",
                      SK: applicationId,
                    }
                  );
                }
              }

              responses.push(response);
            } catch (error) {
              console.log("Handler error ->", error);
              
              if (error.message === "Requirement not found") {
                console.log("Requirement not found, storing record for retry");
                
                const existingRecord = await dbService.getObject(
                  process.env.GUS_EIP_INTEGRATION_EVENT_BACKLOG_TABLE,
                  {
                    PK: "UNFC_GUS_SF",
                    SK: applicationId
                  }
                );
                
                if (existingRecord.Item) {
                  await dbService.updateObject(
                    process.env.GUS_EIP_INTEGRATION_EVENT_BACKLOG_TABLE,
                    {
                      PK: "UNFC_GUS_SF",
                      SK: applicationId
                    },
                    {
                      retryCount: (existingRecord.Item.retryCount || 0) + 1,
                      lastUpdated: new Date().toISOString()
                    }
                  );
                } else {
                  await dbService.putObject(
                    process.env.GUS_EIP_INTEGRATION_EVENT_BACKLOG_TABLE,
                    {
                      PK: "UNFC_GUS_SF",
                      SK: applicationId,
                      status: "Pending",
                      retryCount: 0,
                      lastUpdated: new Date().toISOString(),
                      ...record
                    }
                  );
                }
              } else {
                console.log("ERROR ->", error);
                await storeFailedRecordsQueue(
                  applicationId,
                  process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                  "UNFC_GUS_SF",
                  record
                );

                await loggerService.error(
                  correlationId,
                  new Date().toISOString(),
                  loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_HANDLER,
                  loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_QUEUE,
                  loggerEnum.Component.GUS_SALESFORCE,
                  loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
                  usecase,
                  record,
                  {},
                  error?.message || error,
                  brand,
                  applicationId,
                  "Applic_Id__c",
                  applicationId,
                  "",
                  "",
                  "",
                  applicationId
                );
              }
            }
          } else {
            console.log(`No handler found for scenario: ${usecase}`);
            responses.push(null);
          }
        } catch (error) {
          console.log("ERROR ->", error);
          await storeFailedRecordsQueue(
            applicationId,
            process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            "UNFC_GUS_SF",
            record
          );

          await loggerService.error(
            correlationId,
            new Date().toISOString(),
            loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_HANDLER,
            loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_QUEUE,
            loggerEnum.Component.GUS_SALESFORCE,
            loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
            usecase,
            record,
            {},
            error?.message || error,
            brand,
            applicationId,
            "Applic_Id__c",
            applicationId,
            "",
            "",
            "",
            applicationId
          );
        }
      } else {
        console.log(`Message already processed and failed: ${applicationId}`);
      }
    } catch (error) {
      console.error("Error processing record:", error);
      await loggerService.error(
        correlationId,
        new Date().toISOString(),
        loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_HANDLER,
        loggerEnum.Component.UNFC_GUS_SALESFORCE_INTEGRATION_QUEUE,
        loggerEnum.Component.GUS_SALESFORCE,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        usecase,
        event,
        {},
        error?.message || error,
        brand,
        applicationId,
        "Applic_Id__c",
        applicationId,
        "",
        "",
        "",
        applicationId
      );
    }
  }

  return responses;
};

export const handleFailedRecords = async () => {
  try {
    console.log("Processing failed records");

    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": "UNFC_GUS_SF",
      },
    };

    const partitionResponse = await dbService.queryObjects(params);
    console.log("PartitionItemData -->", partitionResponse);

    if (partitionResponse.Items && partitionResponse.Items.length > 0) {
      for (const partitionItem of partitionResponse.Items) {
        console.log("Item -->", partitionItem);

        if (
          partitionItem.status === "Failed" &&
          (partitionItem.retryCount <= 3 || !partitionItem.retryCount)
        ) {
          const params = {
            TableName:
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            KeyConditionExpression: "PK = :partitionKey",
            ExpressionAttributeValues: {
              ":partitionKey": `${partitionItem.PK}#${partitionItem.SK}`,
            },
          };

          const failedRecordResponse = await dbService.queryObjects(params);
          console.log("FailedRecordResponse -->", failedRecordResponse);

          if (
            failedRecordResponse.Items &&
            failedRecordResponse.Items.length > 0
          ) {
            for (const failedRecord of failedRecordResponse.Items) {
              try {
                await handleSfRequests({
                  Records: [JSON.parse(failedRecord.record)],
                });
                await dbService.updateObject(
                  process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                  {
                    PK: partitionItem.PK,
                    SK: partitionItem.SK,
                  },
                  {
                    retryCount: (partitionItem.retryCount || 0) + 1,
                  }
                );
              } catch (error) {
                console.error("Error processing failed record:", error);
              }
            }
          }
        }
      }
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Failed records processed successfully",
      }),
    };
  } catch (error) {
    console.error("Error processing failed records:", error);
    throw error;
  }
};

export const handleBacklogRecords = async() => {
  try {
    console.log("Starting to reprocess backlog records");
    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_EVENT_BACKLOG_TABLE || '',
      KeyConditionExpression: 'PK = :pk',
      ExpressionAttributeValues: {
        ':pk': 'UNFC_GUS_SF'
      }
    };

    const response = await dbService.queryObjects(params);
    console.log(`Found ${response.Items?.length || 0} records to reprocess`);

    if (!response.Items || response.Items.length === 0) {
      console.log("No records found to reprocess");
      return;
    }

    console.log("Items found ->", response.Items)
    for (const Item of response.Items) {
      try {
        console.log("Record details ->", Item)
        const eventBody = JSON.parse(Item.body);
        console.log("Event body ->", eventBody)
        const eventMessage = JSON.parse(eventBody.Message);
        console.log("Event message: ",eventMessage)
        const response = await snsService.publishMessages(eventMessage,Item.SK, process.env.UNFC_OUTBOUND_TOPIC_ARN);
        console.log(`Successfully published message for record ${Item.SK}: ${response}`);

        await dbService.deleteItem(process.env.GUS_EIP_INTEGRATION_EVENT_BACKLOG_TABLE,{
          PK: Item.PK,
          SK: Item.SK
        } );
        console.log(`Successfully deleted record ${Item.SK}`);
      } catch (error) {
        console.error(`Error processing record ${Item.SK}:`, error);
        continue;
      }
    }

    console.log("Completed reprocessing backlog records");
  } catch (error) {
    console.error("Error in reprocessBacklogRecords:", error);
    throw error;
  }
}
