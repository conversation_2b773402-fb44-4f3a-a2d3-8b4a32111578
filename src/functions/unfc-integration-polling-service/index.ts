import { handlerPath } from "@libs/handler-resolver";

export const unfcIntegrationPollingService = {
  handler: `${handlerPath(__dirname)}/unfcIntegrationPollingService.pollApi`,
  name: "unfc-integration-polling-service-${self:provider.stage}",
  events: [
    {
      schedule: "rate(5 minutes)",
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 60, // 1 minutes
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};
