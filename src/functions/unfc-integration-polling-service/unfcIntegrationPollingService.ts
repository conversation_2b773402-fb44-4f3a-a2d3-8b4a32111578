import unfcEllucianConnector from "../../connectors/unfc-ellucian-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { v4 as uuidv4 } from "uuid";

// Initialize services
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

/**
 * Main handler function that polls the API endpoints
 */
export const pollApi = async () => {
  // Generate correlation ID for tracking
  const correlationId = uuidv4();
  const brand = "UNFC";

  await loggerService.log(
    correlationId,
    new Date().toISOString(),
    loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
    loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
    loggerEnum.Component.UNFC_INTEGRATION_SNS,
    loggerEnum.Event.INITIATED_POLLING_SERVICE,
    loggerEnum.UseCase.UNFC_DATA_POLLING,
    {},
    {},
    "Starting UNFC GUS SF Integration polling service",
    brand,
    "",
    "",
    ""
  );

  try {
    // Authenticate with the API

    // Fetch data from the /consume endpoint using the base get method
    await loggerService.log(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
      loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
      loggerEnum.Component.UNFC_INTEGRATION_SNS,
      loggerEnum.Event.DATA_FETCH_INITIATED,
      loggerEnum.UseCase.UNFC_DATA_POLLING,
      {},
      {},
      "Started fetching data from /consume endpoint",
      brand,
      "",
      "",
      ""
    );

    // Get the last processed ID from DynamoDB
    const lastProcessedID = await unfcEllucianConnector.getLastProcessedID();

    const consumeData = await unfcEllucianConnector.consumeData(
      lastProcessedID
    );

    // Process the data if needed
    if (consumeData && Array.isArray(consumeData) && consumeData.length > 0) {
      await loggerService.log(
        correlationId,
        new Date().toISOString(),
        loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
        loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
        loggerEnum.Component.UNFC_INTEGRATION_SNS,
        loggerEnum.Event.DATA_FETCH_COMPLETED,
        loggerEnum.UseCase.UNFC_DATA_POLLING,
        { recordCount: consumeData.length },
        {},
        `Received ${consumeData.length} records from /consume endpoint`,
        brand,
        "UNFC_POLLING",
        "UNFC_POLLING_ID",
        "UNFC_POLLING",
        "",
        "",
        "",
        "UNFC_POLLING"
      );

      // Process each record and publish to SNS
      for (const record of consumeData) {
        try {
          // Publish the record to the SNS topic
          await loggerService.log(
            correlationId,
            new Date().toISOString(),
            loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
            loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
            loggerEnum.Component.UNFC_INTEGRATION_SNS,
            loggerEnum.Event.PUBLISH_TO_SNS_INITIATED,
            loggerEnum.UseCase.UNFC_DATA_POLLING,
            {},
            { record },
            `Publishing record ${record.id} to SNS topic`,
            brand,
            "",
            "",
            ""
          );

          await unfcEllucianConnector.publishMessageToSNS(record);

          await loggerService.log(
            correlationId,
            new Date().toISOString(),
            loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
            loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
            loggerEnum.Component.UNFC_INTEGRATION_SNS,
            loggerEnum.Event.PUBLISH_TO_SNS_COMPLETED,
            loggerEnum.UseCase.UNFC_DATA_POLLING,
            {},
            {},
            `Successfully published record ${record.id} to SNS topic`,
            brand,
            "",
            "",
            ""
          );
        } catch (publishError) {
          await loggerService.error(
            correlationId,
            new Date().toISOString(),
            loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
            loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
            loggerEnum.Component.UNFC_INTEGRATION_SNS,
            loggerEnum.Event.FAILED_PUBLISH_TO_SNS,
            loggerEnum.UseCase.UNFC_DATA_POLLING,
            { recordId: record.id },
            {},
            `Error publishing record ${record.id} to SNS topic: ${
              publishError.message || publishError
            }`,
            brand,
            "",
            "",
            ""
          );
          console.error(`Error publishing record to SNS topic:`, publishError);
        }
      }

      const lastRecord = consumeData[consumeData.length - 1];
      if (lastRecord && lastRecord.id) {
        await unfcEllucianConnector.updateLastProcessedID(
          lastRecord.id,
          lastRecord
        );
      }
    } else {
      await loggerService.log(
        correlationId,
        new Date().toISOString(),
        loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
        loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
        loggerEnum.Component.UNFC_INTEGRATION_SNS,
        loggerEnum.Event.NO_RECORDS_FOUND,
        loggerEnum.UseCase.UNFC_DATA_POLLING,
        {},
        {},
        "No records received from /consume endpoint",
        brand,
        "UNFC_POLLING",
        "UNFC_POLLING_ID",
        "UNFC_POLLING",
        "",
        "",
        "",
        "UNFC_POLLING"
      );
    }

    await loggerService.log(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
      loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
      loggerEnum.Component.UNFC_INTEGRATION_SNS,
      loggerEnum.Event.OPERATION_COMPLETED,
      loggerEnum.UseCase.UNFC_DATA_POLLING,
      {},
      { recordsProcessed: consumeData?.length || 0 },
      "Polling completed successfully",
      brand,
      "",
      "",
      ""
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Polling completed successfully",
        recordsProcessed: consumeData?.length || 0,
      }),
    };
  } catch (error) {
    await loggerService.error(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.UNFC_INTEGRATION_POLLING_SERVICE,
      loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
      loggerEnum.Component.UNFC_INTEGRATION_SNS,
      loggerEnum.Event.FAILED_UNFC_POLLING_SERVICE,
      loggerEnum.UseCase.UNFC_DATA_POLLING,
      {},
      {},
      `Error in UNFC GUS SF Integration polling service: ${
        error.message || error
      }`,
      brand,
      "",
      "",
      ""
    );
    console.error("Error in UNFC GUS SF Integration polling service:", error);
    throw error;
  }
};
