export const salesforceSubObjectsConfig = {
  EducationHistoryRecord__c: {
    Institution_Type__c: "apex_Institution_Type__c",
    InstitutionName__c: "hed__Educational_Institution_Name__c",
    // 'Application__c.Name': "apex_Name_While_Enrolled__c",7
    GPA_Score__c: "hed__GPA__c",
    EnrolmentDateYear__c: "hed__Start_Date__c",
    GraduationDate__c: "hed__End_Date__c",
    Degree_earned__c: "Degree_Earned_Student_Provided__c",
    Transferring_credits_to_our_Institution__c: "apex_Transferring_Credits__c",
    hed__Account__c: "hed__Account__c",
  },
};

export const salesforceConfig = {
  Account: {
    "Account.AccountName": "Name",
    "Account.AccountRecordType": "RecordTypeId"
  },
  Contact: {
    "Opportunity.Race__c": "hed__Race__c",
    "Opportunity.OwnerId": 'OwnerId',
    "Account.ContactRecordType": "RecordTypeId",
    "Account.FirstName": "FirstName",
    "Account.LastName": "LastName",
    "Account.PersonEmail": ["Email", "apex_Personal_Email__c"],
    "Account.Preferred_Email__c": "hed__Preferred_Email__c",
    "Account.Middle_Name__c": "MiddleName",
    "Account.Former_Last_Name__c": "hed__Former_Last_Name__c",
    "Account.PlaceOfBirth__c": "apex_City_of_Birth__c",
    "Account.Country_of_Origin__c": "hed__Country_of_Origin__c",
    "Account.DateofBirth__c": "birthdate",
    "Account.Gender__c": "hed__Gender__c",
    "Account.Mobile__c": "apex_International_Phone_Number__c",
    "Account.PersonOtherPhone": "OtherPhone",
    "Account.Marital_Status__c": "apex_Marital_Status__c",
    "Opportunity.Citizenship_Status__c": "hed__Citizenship_Status__c",
    "Opportunity.Location__c": "apex_Campus_Code_Picklist__c",
    "Account.Country__c": ["hed__Citizenship__c", "OtherCountry"],
    // "Account.Country__c": "OtherCountry",
    "Account.PersonMailingStreet": "OtherStreet",
    "Account.PersonMailingCity": "OtherCity",
    "Account.PersonMailingState": "OtherState",
    "Account.PersonMailingPostalCode": "OtherPostalCode",
    "Account.ShippingStreet": "MailingStreet",
    "Account.ShippingCity": "MailingCity",
    "Account.ShippingCountry": "MailingCountry",
    // "Account.ShippingState": "MailingState",
    "Account.ShippingPostalCode": "MailingPostalCode",
    "Lead.PreferredContactMethod__c": "apex_Preferred_Contact_Method__c",
    "Opportunity.Preferred_Contact_Time__c": "apex_Best_Time_To_Contact__c",
    "Opportunity.Ethinicity__c": "hed__Ethnicity__c",
    "Application__c.First_Generation_College_Student__c":
      "apex_First_Generation_College_Student__c",
    "WorkHistoryRecord__c.Work_Phone__c": "hed__WorkPhone__c",
    "Opportunity.CriminalConvictions__c": "apex_Prior_Conviction__c",
    "Opportunity.Serviced_by__c": "apex_Serviced_by__c",
    "Lead.LeadSource__c": "LeadSource"
  },
  Opportunity: {
    "Opportunity.Type": "Type",
    "Opportunity.Location__c": "apex_Campus__c",
    "Opportunity.Affiliated_Account_Name__c": "Affiliated_Account_Name__c",
    "Opportunity.Affiliated_Account_Type__c": "Affiliated_Account_Type__c",
    "Opportunity.Programme__c": ["apex_program__c", "Name", "apex_Applying_To__c"],
    "Opportunity.Product_Intake_Date__c": [
      "CloseDate",
      "apex_expected_start_date__c",
    ],
    "Opportunity.StageName": "StageName",
    "Opportunity.ApplicationFormId__c": "External_ID__c",
    "Opportunity.OwnerId": 'OwnerId',
    "Opportunity.Serviced_by__c": "apex_Serviced_by__c"
  },
  hed__Application__c: {
     "Account.Current_Location__c": "apex_Current_Location__c",
    "Opportunity.Affiliated_Account_Name__c": "Affiliated_Account_Name__c",
    "Opportunity.Affiliated_Account_Type__c": ["Affiliated_Account_Type__c", "hed__Application_Type__c"],
    "Opportunity.ApplicationFormId__c": "External_ID__c",
    "Application__c.Country__c": "Application_Country__c",
    "Opportunity.Location__c": ["apex_Campus__c", "apex_Application_Campus__c"],
    "Opportunity.Programme__c": ["apex_Application_Program__c", "hed__Applying_To__c"],
    "Opportunity.CampusAccountId": ['apex_Campus_Account__c'],
    "Opportunity.OwnerId": 'OwnerId',
    "Opportunity.Product_Intake_Date__c": ["apex_Expected_Start_Date__c", "hed__Term__c", "apex_Application_Term__c"],
    "Opportunity.VisaComments__c": "apex_Visa_Type__c",
    "Application__c.First_Name_Emergency__c": "apex_Emergency_Contact_Name__c",
    "Application__c.Surname_Emergency__c": "apex_Emergency_Contact_Name__c",
    'Connection__c.Relationship__c': "apex_Emergency_Contact_Relationship__c",
    "Application__c.Emergency_Phone_Number__c":
      "apex_Emergency_Intnl_Primary_Phone__c",
    "Opportunity.Highschool_Completion_Status__c": "High_School_Completion_Status__c",
    "Account.WorkExperience__c": "apex_Are_You_Currently_Employed__c",
    "WorkHistoryRecord__c.Employer__c": "apex_Employer__c",
    "Opportunity.Tuition_Reimbursement_by_Employer__c":
      "apex_Tuition_Reimbursement_By_Employer__c",
    "WorkHistoryRecord__c.Position__c": "apex_Employment_Position__c",
    "WorkHistoryRecord__c.StartDate__c": "apex_Employment_Start_Date__c",
    "Lead.How_did_you_hear_about_us__c": "apex_How_Did_You_Hear_About_Us__c",
    "Opportunity.Are_you_currently_employed_with_us__c":
      "apex_Current_Employee_Of_Herzing__c",
    "Application__c.EmergencyContactName": "apex_Emergency_Contact_Name__c",
    "Account.Health_Information__c": "apex_General_Health__c",
    "Application__c.Application_Status__c": "hed__Application_Status__c",
    "Application__c.Application_Date__c": "hed__Application_Date__c",
  },
  hed__Education_History__c: ["EducationHistoryRecord__c"],
};
