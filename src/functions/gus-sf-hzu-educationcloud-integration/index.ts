import { handlerPath } from '@libs/handler-resolver';

export const gusSfHzuEducationcloudIntegration = {
    handler: `${handlerPath(__dirname)}/gusSfHzuEducationcloudIntegrationService.handleSfRequests`,
    name: 'gus-sf-hzu-educationcloud-integration-${self:provider.stage}',
    events: [
        {
            sqs: {
                arn: '${self:provider.environment.GUS_SF_HZU_EDUCATIONCLOUD_INTEGRATION_SQS_QUEUE_ARN}'
            }
        }
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 180,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};

export const gusSfHzuEducationcloudFailedRecordProcessor = {
    handler: `${handlerPath(__dirname)}/gusSfHzuEducationcloudIntegrationService.handleFailedRecords`,
    name: 'gus-sf-hzu-educationcloud-failed-record-processor-${self:provider.stage}',
    events: [
        {
            schedule: 'rate(60 minutes)',
        },
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 180,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};
