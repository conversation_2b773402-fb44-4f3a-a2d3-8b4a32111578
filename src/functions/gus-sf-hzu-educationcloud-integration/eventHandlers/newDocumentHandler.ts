import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { getPicklistValue } from "src/common/getPickListValue";
import { S3Service } from "src/common/s3Service";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const s3Service = new S3Service();

export class NewDocumentHandler implements PlatformEventHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private email: string;
  async fetchGusSFDetails(event: any, correlationId?: string): Promise<any> {
    try {
      await this.log(
        event,
        `fetch opportunity files initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FILES_INTIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        event
      );
      return await getData(
        `gus/opportunityfile/${event.payload.Opportunity_file_Id__c}`,
        correlationId
      );
    } catch (error) {
      throw new Error(
        `Error fetching documents from gus by opportunityId: ${JSON.stringify(error)}`
      );
    }
  }
  async getHZUDocumentType(documentDetails, event): Promise<any> {
    if (documentDetails.DocumentType__c === "Degree transcripts") {
      let institutionDetails;
      if (documentDetails.Related_Education_History__c) {
        await this.log(
          event,
          'get education history by oppId initiated',
          loggerEnum.Event.GET_EDU_HISTORY_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          event
        )
        const educationHistory = await getData(
          `gus/geteducationhistorybyoppid/${documentDetails.Opportunity__c}`,
          event?.event?.EventUuid,
        );
        await this.log(
          event,
          'get education history by oppId completed',
          loggerEnum.Event.GET_EDU_HISTORY_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          event,
          educationHistory
        )
        console.log("institutionDetails", educationHistory);
        institutionDetails = educationHistory.find(
          (item) => item.Id === documentDetails?.Related_Education_History__c
        );
        documentDetails.Additional_Info__c = institutionDetails.InstitutionName__c
      } else {
        await this.log(
          event,
          'get education history by additional info initiated',
          loggerEnum.Event.GET_EDU_HISTORY_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          `gus/geteducationhistory?institutionName=${encodeURIComponent(
            documentDetails.Additional_Info__c
          )}&opportunityId=${event.payload.Opportunity_Id__c}`
        )
        institutionDetails = await getData(
          `gus/geteducationhistory?institutionName=${encodeURIComponent(
            documentDetails.Additional_Info__c
          )}&opportunityId=${event.payload.Opportunity_Id__c}`,
          event?.event?.EventUuid
        );
      }
      await this.log(
        event,
        'fetch institution details completed',
        loggerEnum.Event.GET_EDU_HISTORY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        `gus/geteducationhistory?institutionName=${encodeURIComponent(
          documentDetails.Additional_Info__c
        )}&opportunityId=${event.payload.Opportunity_Id__c}`,
        institutionDetails
      )
      console.log("filtered", institutionDetails);
      const schoolLevelTypes = ["High School", "Home School", "GED"];
      if (
        institutionDetails &&
        schoolLevelTypes.includes(institutionDetails.Institution_Type__c) &&
        documentDetails.Opportunity__r.LevelCode__c === "UG"
      ) {
        return await getPicklistValue("DocumentType__c", "School");
      } else if (institutionDetails.Institution_Type__c === "University") {
        let type = await getPicklistValue("DocumentType__c", "College");
        return `${type} ${documentDetails.Additional_Info__c}`;
      }
    }
    const hzuDocumentType = await getPicklistValue(
      "DocumentType__c",
      documentDetails.DocumentType__c
    );
    return hzuDocumentType;
  }

  async syncInHZU(
    event: any,
    updateById?: any,
    correlationId?: string
  ): Promise<any> {
    try {
      return await postData("hzu/fetchanduploadfile", event, correlationId);
    } catch (error) {
      throw new Error(`Error syncing in hzu: ${JSON.stringify(error)}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.applicationFormId = platformEventMessage?.payload?.Application_Form_Id__c;
    this.brand = platformEventMessage?.payload?.BusinessUnitFilter__c;
    this.email = platformEventMessage?.payload?.Email__c

    try {
      const opportunityFileDetails = await this.fetchGusSFDetails(
        platformEventMessage,
        this.correlationId
      );
      const opportunityFile = opportunityFileDetails[0];
      if (!opportunityFile) {
        throw new Error(
          `Opportunity file not found for opportunity: ${platformEventMessage.payload.Opportunity_file_Id__c}`
        );
      }
      await this.log(
        platformEventMessage,
        "get opportunity files completed",
        loggerEnum.Event.GUS_SF_FETCH_OPPFILE_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage,
        opportunityFile
      );
      await this.log(
        platformEventMessage,
        "get hzu document type initiated",
        loggerEnum.Event.GET_DOCUMENT_TYPE,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage
      );
      const hzuDocumentType = await this.getHZUDocumentType(
        opportunityFile,
        platformEventMessage
      );
      await this.log(
        platformEventMessage,
        "get hzu document type completed",
        loggerEnum.Event.GET_DOCUMENT_TYPE,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage,
        hzuDocumentType
      );
      console.log(
        `hzu document type for ${opportunityFile?.DocumentType__c}`,
        hzuDocumentType
      );

      if (hzuDocumentType) {
        await this.log(
          platformEventMessage,
          'fetch applicationId by external Id initiated',
          loggerEnum.Event.FETCH_HZU_APPLICATION_INITIATED,
          loggerEnum.Component.HZU_EDUCATIONCLOUD,
          platformEventMessage
        )
        const applicationResponse = await getData(
          `hzu/applicationIdByExternalId/${platformEventMessage.payload.Application_Form_Id__c}`,
          this.correlationId
        );

        if (applicationResponse?.Id) {
          await this.log(
            platformEventMessage,
            'fetch applicationId by external Id completed',
            loggerEnum.Event.FETCH_HZU_APPLICATION_COMPLETED,
            loggerEnum.Component.HZU_EDUCATIONCLOUD,
            platformEventMessage,
            applicationResponse
          )
          await this.log(
            platformEventMessage,
            "get document checklist item initiated",
            loggerEnum.Event.FETCH_HZU_DOCUMENT_CHECKLIST_ITEM_INITIATED,
            loggerEnum.Component.HZU_EDUCATIONCLOUD,
            platformEventMessage
          )
          const documentChecklistItemResponse = await getData(
            `hzu/documentChecklistItemDetails/${applicationResponse.Id}`,
            this.correlationId
          );
          console.log(
            "DocumentChecklistItemResponse: ",
            documentChecklistItemResponse
          );

          await this.log(
            platformEventMessage,
            "get document checklist item completed",
            loggerEnum.Event.FETCH_HZU_DOCUMENT_CHECKLIST_ITEM_COMPLETED,
            loggerEnum.Component.HZU_EDUCATIONCLOUD,
            platformEventMessage,
            documentChecklistItemResponse
          )

          const documentChecklistItemDetails =
            documentChecklistItemResponse.filter(
              (obj) => obj.Name.toLowerCase() === hzuDocumentType.toLowerCase()
            );

          console.log(
            "Document checklist item details",
            documentChecklistItemDetails
          );

          if (documentChecklistItemDetails.length > 0) {
            const fileName = opportunityFile.Name.split(".")[0];
            const fileType = opportunityFile.Name.split(".")[1];
            const hzuRequest = {
              fileName: fileName,
              fileNameWithExtension: opportunityFile.Name,
              fileExtension: fileType,
              fileType: fileType.toUpperCase(),
              documentCheckListId: documentChecklistItemDetails[0].Id,
              externalId: platformEventMessage.payload.Opportunity_file_Id__c,
            };

            const requestBody = {
              uploadDetails: hzuRequest,
              additionalDetails: opportunityFile,
            };
            await this.log(
              platformEventMessage,
              "sync files in hzu initiated",
              loggerEnum.Event.SYNC_IN_HZU_INITIATED,
              loggerEnum.Component.HZU_EDUCATIONCLOUD,
              requestBody
            );
            const uploadFileResponse = await this.syncInHZU(
              requestBody,
              this.correlationId
            );
            console.log("File uploaded successfully", uploadFileResponse);

            // Log upload success and stop execution here if no further action needed
            await this.log(
              platformEventMessage,
              "sync file in hzu successful",
              loggerEnum.Event.OPERATION_COMPLETED,
              loggerEnum.Component
                .HZU_EDUCATIONCLOUD,
              requestBody,
              uploadFileResponse
            )
            return uploadFileResponse;
          } else {
            throw new Error(
              `No document checklist item found for application ${applicationResponse.Id} with document type: ${hzuDocumentType}`
            );
          }
        } else {
          throw new Error(
            `No herzing application found for gus application formId: ${platformEventMessage.payload.Application_Form_Id__c}`
          );
        }
      } else {
        console.log(
          `No matching hzu documentType found for gus documentType: ${opportunityFile.DocumentType__c}`
        );
        await this.log(
          platformEventMessage,
          `No matching hzu documentType found for gus documentType: ${opportunityFile.DocumentType__c}`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component
            .HZU_EDUCATIONCLOUD,
          platformEventMessage
        )

        return {
          success: true,
          message: `No matching hzu documentType found for gus documentType: ${opportunityFile.DocumentType__c}`
        }
      }
    } catch (error) {
      console.log("Error->", error);
      await this.error(
        platformEventMessage,
        error.message ? JSON.stringify(error.message) : JSON.stringify(error),
        loggerEnum.Event.REQUIRED_DOCUMENT_SYNC_FAILED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        platformEventMessage
      )
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message ? errorMessage.message : JSON.stringify(errorMessage),
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
