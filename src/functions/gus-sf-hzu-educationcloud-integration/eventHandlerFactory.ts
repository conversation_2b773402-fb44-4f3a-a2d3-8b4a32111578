import { OpportunityDocumentUploadHandler } from "./eventHandlers/documentUploadHandler";
import { NewApplicationHandler } from "./eventHandlers/newApplicationHandler";
import { WithdrawApplicationHandler } from "./eventHandlers/withdrawApplicationHandler";
import { NewDocumentHandler } from "./eventHandlers/newDocumentHandler";
export class PlatformEventHandlerFactory {
  static getHandler(scenario): PlatformEventHandler {
    switch (scenario) {
      case "GUS_NEW_APPLICATION":
        return new NewApplicationHandler();
      case "GUS_WITHDRAW_APPLICATION":
        return new WithdrawApplicationHandler();
      case "DOCUMENT_CHECKLIST_CREATED":
        return new OpportunityDocumentUploadHandler();
      case "GUS_DOCUMENT_UPDATE":
        return new NewDocumentHandler();
      default:
        console.log("No handler found for event type: " + scenario);
        break;
    }
  }
}
