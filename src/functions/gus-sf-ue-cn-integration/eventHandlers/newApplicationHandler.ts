import { getData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { sendToSQS } from "src/common/sqsService";
import {
  ApplicationData,
  defaultApplicationData,
  EQHEData,
  FileData,
} from "../models/application-creation.interface";
import { documentTypeMapping } from "../enums/files-mapping.enum";
import { RegisterHandler } from "../HandlerRegistry";
import {
  applicationCreationFieldMapping,
  educationHistoryRecordFieldMapping,
} from "../mapper/applicationCreationFieldMapping";
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

@RegisterHandler("GUS_NEW_APPLICATION")
export class NewApplicationHandler implements ICampusNetInboundHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private applicationData: ApplicationData = defaultApplicationData;
  async fetchGusSFDetails(opportunityId: any): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `Fetch application details by opportunityId initiated ${opportunityId}`,
        loggerEnum.Event.GET_OPPORTUNITY_BY_ID,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return await getData(
        `gus/getapplicationsdetails/${opportunityId}?scenario=${this.usecase}`,
        this.correlationId,
        process.env.UE_KEY
      );
    } catch (error) {
      await this.error(
        opportunityId,
        error,
        loggerEnum.Event.FETCH_GUS_APPLICATION_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching gus object details: ${error}`);
    }
  }
  async fetchOpportunityFiles(
    opportunityId: any,
    correlationId?: string
  ): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `Fetch opportunity files for ${opportunityId}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FILES_INTIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return await getData(
        `gus/getopportunityfiles/${opportunityId}`,
        correlationId,
        process.env.UE_KEY
      );
    } catch (error) {
      await this.error(
        opportunityId,
        error,
        loggerEnum.Event.REQUIRED_DOCUMENT_SYNC_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(
        `Error fetching documents from gus by opportunityId: ${error}`
      );
    }
  }
  syncToTargetSystem(
    event: unknown,
    updateById?: unknown,
    correlationId?: unknown
  ): Promise<any> {
    throw new Error("Method not implemented.");
  }
  async handleMessage(event: any): Promise<any> {
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.applicationFormId =
      platformEventMessage?.payload?.Application_Form_Id__c;
    this.brand = platformEventMessage?.payload?.BusinessUnitFilter__c || "UEG";
    const { Opportunity_Id__c } = platformEventMessage?.payload;
    await this.log(
      platformEventMessage.payload,
      `UE CampusNet Application create initiated ${Opportunity_Id__c}`,
      loggerEnum.Event.CREATE_APPLICATION_INITIATED
    );
    if (!this.applicationFormId || !Opportunity_Id__c) {
      await this.error(
        platformEventMessage,
        `Application form id or opportunity id missing in ${this.usecase} scenario`,
        loggerEnum.Event.CREATE_APPLICATION_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(
        `Application form id or opportunity id missing in ${this.usecase} scenario`
      );
    }

    try {
      const sfDetails = await this.fetchGusSFDetails(Opportunity_Id__c);
      await this.log(
        sfDetails,
        `Fetch application details by opportunityId completed ${Opportunity_Id__c}`,
        loggerEnum.Event.GET_OPPORTUNITY_BY_ID,
        loggerEnum.Component.GUS_SALESFORCE
      );
      const opportunityfileDetails = await this.fetchOpportunityFiles(
        Opportunity_Id__c,
        this.correlationId
      );

      sfDetails.Opportunity = await this.enrichOpportunityWithLineItemData(
        sfDetails.Opportunity
      );
      await this.log(
        sfDetails,
        `Fetch opportunity file details by opportunityId completed ${Opportunity_Id__c}`,
        loggerEnum.Event.GET_OPPORTUNITY_BY_ID,
        loggerEnum.Component.GUS_SALESFORCE
      );
      await this.transformData(sfDetails, applicationCreationFieldMapping);
      await this.transformEducationHistory(
        sfDetails.EducationHistoryRecord__c || [],
        educationHistoryRecordFieldMapping
      );
      await this.processOpportunityFiles(
        opportunityfileDetails?.response || []
      );
      console.log("transformedData", JSON.stringify(this.applicationData));
      const sqsPayload = {
        name: "oap-opportunity",
        apiVersion: 2,
        externalId: this.applicationFormId,
        createdAt: new Date().toISOString(),
        data: this.applicationData,
      };
      const SQS_QUEUE_URL =
        process.env.GUS_SF_UE_CAMPUSNET_INTEGRATION_SQS_QUEUE_URL;
      await sendToSQS(
        SQS_QUEUE_URL,
        sqsPayload,
        Opportunity_Id__c,
        `${Opportunity_Id__c}-${Date.now()}`
      );
      console.log("successfully sent application data to SQS");
      await this.log(
        this.applicationData,
        `Successfully sent transformed data to SQS for ${Opportunity_Id__c}`,
        loggerEnum.Event.OPERATION_COMPLETED
      );
    } catch (error) {
      await this.error(
        platformEventMessage,
        error.message ? error.message : error,
        loggerEnum.Event.CREATE_APPLICATION_FAILED,
        loggerEnum.Component.UE_CAMPUSNET
      );
      throw error;
    }
  }
  private async enrichOpportunityWithLineItemData(
    opportunity: any
  ): Promise<any> {
    let enrichedOpportunity = { ...opportunity };

    if (opportunity?.OpportunityLineItems?.records?.length) {
      const lineItem = opportunity.OpportunityLineItems.records[0];

      enrichedOpportunity = {
        ...opportunity,
        Duration__c: lineItem?.Product2?.Duration__c ?? "",
        Product2Id: lineItem?.Product2Id ?? "",
        Location__c: lineItem?.Location__c ?? "",
        ProductLanguage__c: lineItem?.ProductLanguage__c ?? "",
        Intake_Date__c: lineItem?.Intake_Date__c ?? "",
      };
    }

    return enrichedOpportunity;
  }

  private async processOpportunityFiles(files: any[]): Promise<void> {
    if (!Array.isArray(files)) {
      console.log("processOpportunityFiles: files is not an array", files);
      return;
    }

    const categorizedFiles: Partial<ApplicationData> = {
      fileCV: [],
      filePassport: [],
      educationSchoolsTranscriptFile: [],
      photo: [],
      files: [],
      educationSchoolsCertificateFile: [],
      applyReasonFile: [],
    };

    for (const file of files) {
      const docType = file.DocumentType__c?.trim();
      const fileData: FileData = {
        OpportunityFileId: file.Id,
        s3BucketName: file.BucketName__c || "",
        originalFileName: file.OriginalValue__c,
        filePath: file.S3FileName__c,
      };

      const key = documentTypeMapping[docType];

      if (key && categorizedFiles[key] instanceof Array) {
        (categorizedFiles[key] as FileData[]).push(fileData);
      } else {
        categorizedFiles.files?.push(fileData);
      }
    }

    Object.assign(this.applicationData, categorizedFiles);
  }

  private async transformEducationHistory(
    educationRecords: any[],
    educationHistoryFieldMapping: any
  ): Promise<void> {
    if (!Array.isArray(educationRecords)) {
      console.log(
        "transformEducationHistory: educationRecords is not an array",
        educationRecords
      );
      return;
    }

    const mappedData: EQHEData[] = [];

    for (const record of educationRecords) {
      const entry: EQHEData = {
        eqheDate: "",
        eqheCity: "",
        eqhecountry: "",
        eqheTitle: "",
      };

      for (const [sfField, localField] of Object.entries(
        educationHistoryFieldMapping
      )) {
        if (record[sfField] !== undefined) {
          entry[localField as keyof EQHEData] = record[sfField];
        }
      }

      mappedData.push(entry);
    }

    this.applicationData.eqhe = mappedData;
  }

  private async transformData(
    inputData: any,
    salesforceConfig: any
  ): Promise<void> {
    try {
      for (const [objectType, mappings] of Object.entries(salesforceConfig)) {
        const inputObject = inputData[objectType] || {};

        for (const [inputPath, outputFieldOrFields] of Object.entries(
          mappings
        )) {
          const keys = inputPath.replace(`${objectType}.`, "").split(".");
          let value = inputObject;

          for (const key of keys) {
            if (value && value[key] !== undefined) {
              value = value[key];
            } else {
              value = null;
              break;
            }
          }

          const assignValue = value ?? "";

          if (Array.isArray(outputFieldOrFields)) {
            for (const field of outputFieldOrFields) {
              this.applicationData[field] = Array.isArray(
                this.applicationData[field]
              )
                ? assignValue ?? []
                : assignValue ?? "";
            }
          } else {
            this.applicationData[outputFieldOrFields] = Array.isArray(
              this.applicationData[outputFieldOrFields]
            )
              ? assignValue ?? []
              : assignValue ?? "";
          }
        }
      }
    } catch (error) {
      throw new Error(`Error in transformData: ${error.message}`);
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.UE_CAMPUSNET,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }

  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.UE_CAMPUSNET,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
