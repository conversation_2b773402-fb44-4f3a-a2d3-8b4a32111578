import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { sendToSQS } from "src/common/sqsService";
import { RegisterHandler } from "../HandlerRegistry";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();

@RegisterHandler("GUS_DOCUMENT_UPLOAD")
export class DocumentSyncHandler implements ICampusNetInboundHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(
    opportunityId: any,
    correlationId?: string
  ): Promise<any> {
    throw new Error("Method not implemented.");
  }
  syncToTargetSystem(
    event: unknown,
    transformData?: unknown,
    correlationId?: unknown
  ): Promise<any> {
    throw new Error("Method not implemented.");
  }

  async handleMessage(event: any): Promise<any> {
    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);
    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage?.payload?.Scenario__c;
    this.applicationFormId =
      platformEventMessage.payload.Application_Form_Id__c;
    this.brand = platformEventMessage.payload.BusinessUnitFilter__c || "UEG";
    const {
      Opportunity_Id__c,
      S3FileName__c,
      DocumentType__c,
      Name__c,
      Comment__c,
      BucketName__c,
      Opportunity_file_Id__c,
    } = platformEventMessage.payload;

    await this.log(
      platformEventMessage.payload,
      `UE CampusNet sync required docs initiated for ${Opportunity_Id__c}`,
      loggerEnum.Event.SYNC_IN_UE_CAMPUSNET_INITIATED
    );

    try {
      if (!S3FileName__c || !DocumentType__c || !Name__c) {
        await this.error(
          event,
          `Required fields missing in ${Opportunity_Id__c}`,
          loggerEnum.Event.SYNC_IN_UE_CAMPUSNET_FAILED,
          loggerEnum.Component.GUS_SALESFORCE
        );
      }
      const Files = [
        {
          S3FileName: S3FileName__c,
          DocumentType: DocumentType__c,
          Name: Name__c,
          BucketName__c: BucketName__c,
          OpportunityFileId: Opportunity_file_Id__c,
        },
      ];
      const transformedData = {
        ExternalId: this.applicationFormId,
        Comment__c,
        Files,
      };
      console.log("transformedData", transformedData);
      // Construct SQS payload
      const sqsPayload = {
        name: "cn-application-files",
        apiVersion: 2,
        createdAt: new Date().toISOString(),
        data: transformedData,
      };

      // Send to SQS
      const SQS_QUEUE_URL =
        process.env.GUS_SF_UE_CAMPUSNET_INTEGRATION_SQS_QUEUE_URL;
      await sendToSQS(
        SQS_QUEUE_URL,
        sqsPayload,
        Opportunity_Id__c,
        `${Opportunity_Id__c}-${Date.now()}`
      );
      console.log("successfully sent the application document data to SQS");
      await this.log(
        transformedData,
        `Successfully sent application document data to SQS for ${Opportunity_Id__c}`,
        loggerEnum.Event.OPERATION_COMPLETED
      );
    } catch (error) {
      await this.error(
        platformEventMessage,
        error.message ? error.message : error,
        loggerEnum.Event.REQUIRED_DOCUMENT_SYNC_FAILED,
        loggerEnum.Component.UE_CAMPUSNET
      );
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.UE_CAMPUSNET,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.UE_CAMPUSNET,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
