import { DynamoDBService } from "src/common/dynamodbService";
import { SnsService } from "src/common/snsService";
import { storeFailedRecords } from "src/common/storeFailedRecords";
import { checkExistingMessageGroupId } from "src/common/checkFailedRecords";
import { SQSEvent } from "aws-lambda";
import { PlatformEventHandlerFactory } from "./PlatformEventHandlerFactory";
import { LoggerEnum } from "@gus-eip/loggers";
import { LoggerService } from "src/common/cloudwatchService";
import { v4 as uuidv4 } from "uuid";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const correlationId = uuidv4();
const dbService = new DynamoDBService();
const snsService = new SnsService();
let applicationFormId: string;
let scenario: string;
const brand: string = "UEG";
export const handleSfRequests = async (event: SQSEvent) => {
  const clonedEvent = deepClone(event);
  console.log("clonedEvent-->", clonedEvent);
  for (const record of clonedEvent.Records) {
    try {
      const isFailedMessageGroupData = await checkExistingMessageGroupId(
        record,
        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
        "GUS_SF_UE"
      );
      if (isFailedMessageGroupData === "No messages to process") {
        const eventBody = JSON.parse(record.body);
        console.log("eventBody", eventBody);
        const platformEventMessage = JSON.parse(eventBody.Message);
        scenario = platformEventMessage.payload.Scenario__c || "";
        applicationFormId =
          platformEventMessage.payload?.Application_Form_Id__c || "";
        const handler = PlatformEventHandlerFactory.getHandler(
          platformEventMessage.payload.Scenario__c
        );
        if (handler && typeof handler.handleMessage === "function") {
          const response = await handler.handleMessage(record);
          // update failed record status
          console.log("Response", response);
          if (response && platformEventMessage.status === "Failed") {
            await dbService.deleteItem(
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
              {
                PK: `GUS_SF_UE#${record?.attributes?.MessageGroupId}`,
                SK: platformEventMessage?.uuid || record?.messageId,
              }
            );

            const queryParams = {
              TableName:
                process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
              KeyConditionExpression: "PK = :pkValue",
              ExpressionAttributeValues: {
                ":pkValue": `GUS_SF_UE#${record.attributes.MessageGroupId}`,
              },
            };

            const checkExcistingFailedRecordForMessageGrpId =
              await dbService.queryObjects(queryParams);
            console.log(
              "checkExcistingFailedRecordForMessageGrpId -->",
              checkExcistingFailedRecordForMessageGrpId
            );

            if (checkExcistingFailedRecordForMessageGrpId.Items.length === 0) {
              await dbService.deleteItem(
                process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                {
                  PK: "GUS_SF_UE",
                  SK: record.attributes.MessageGroupId,
                }
              );
            }
          }
          console.log("Response:", response);
        } else {
          console.log("Response:", null);
          throw new Error(
            `No handler found for ${platformEventMessage.payload.Scenario__c}`
          );
        }
      }
    } catch (error) {
      console.log("ERROR ->", error);
      await loggerService.error(
        correlationId,
        new Date().toISOString(),
        loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
        loggerEnum.Event.SYNC_APPLICATION_STATUS_UPDATE,
        scenario,
        "",
        "",
        error?.message ? JSON.stringify(error.message) : JSON.stringify(error),
        brand,
        "",
        "Application_Form_Id__c",
        applicationFormId,
        "Opportunity",
        applicationFormId,
        "Opportunity",
        ""
      );
      await storeFailedRecords(
        record,
        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
        "GUS_SF_UE"
      );
      return {
        status: "FAILED",
        message: error?.message
          ? JSON.stringify(error.message)
          : JSON.stringify(error),
      };
    }
  }
};

const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item));
  }
  const clonedObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }
  return clonedObj;
};

//Handle failed records
export const handleFailedRecords = async () => {
  try {
    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": "GUS_SF_UE",
      },
    };

    const partitionResponse = await dbService.queryObjects(params);
    console.log("PartitionItemData -->", partitionResponse);
    if (partitionResponse.Items && partitionResponse.Items.length > 0) {
      for (const partitionItem of partitionResponse.Items) {
        console.log("Item -->", partitionItem);
        if (
          partitionItem.status === "Failed" &&
          (partitionItem.retryCount <= 3 || !partitionItem.retryCount)
        ) {
          const params = {
            TableName:
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            KeyConditionExpression: "PK = :partitionKey",
            ExpressionAttributeValues: {
              ":partitionKey": `${partitionItem.PK}#${partitionItem.SK}`,
            },
          };
          const records = await dbService.queryObjects(params);
          for (const record of records.Items) {
            const eventBody = JSON.parse(record.body);
            const eventMessage = JSON.parse(eventBody.Message);
            eventMessage.status = "Failed";
            console.log("Event message ->", eventMessage);
            const publishMessages = await snsService.publishMessages(
              eventMessage,
              eventMessage.payload.Application_Form_Id__c,
              process.env.GUS_SF_OUTBOUND_TOPIC_ARN,
              "UEG"
            );

            console.log("Publish messages", publishMessages);
          }
          const currentRetryCount = partitionItem.retryCount || 0;
          await dbService.updateObject(
            process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            {
              PK: partitionItem.PK,
              SK: partitionItem.SK,
            },
            {
              retryCount: currentRetryCount + 1,
            }
          );
        }
      }
      return "All records processed successfully";
    } else {
      return "No records to process";
    }
  } catch (error) {
    throw new Error(error);
  }
};
