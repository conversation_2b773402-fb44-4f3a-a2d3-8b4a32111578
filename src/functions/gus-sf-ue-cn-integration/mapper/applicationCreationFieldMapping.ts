export const applicationCreationFieldMapping = {
  Opportunity: {
    "Opportunity.ProgrammeName__c": "programName",
    "Opportunity.Product_Intake_Date__c": "intakeDate",
    "Opportunity.Programme__c": "program",
    "Opportunity.Duration__c": "duration",
    "Opportunity.ProductLanguage__c": [
      "correspondenceLanguage",
      "language",
      "formLanguage",
    ],
    "Opportunity.Location__c": "location",
    "Opportunity.Intake_Date__c": "intake",
  },
  Account: {
    "Account.PersonMailingPostalCode": "postalCode",
    "Account.PersonMailingStreet": "correspondenceAddress",
    "Account.PersonMailingState": "state",
    "Account.PersonMailingCity": "city",
    "Account.ShippingStreet": "addressCo",
    "Account.PersonMailingAddress": "mailingAddress",
    "Account.Passport__pc": "passportNumber",
    "Account.Citizenship__c": "citizenship",
    "Account.Country__c": ["countryOfBirth", "country"],
    "Account.gaconnector_City__c": "placeOfBirth",
    "Account.DateofBirth__c": "dateOfBirth",
    "Account.FirstName": "firstName",
    "Account.LastName": "legalFamilyName",
    "Account.Level__pc": "level",
    "Account.PersonEmail": "personalEmail",
    "Account.Mobile__c": "phone",
    "Account.PersonOtherPhone": "phoneHome",
    "Account.Salutation": "title",
  },
};

export const educationHistoryRecordFieldMapping = {
  EducationHistoryRecord__c: {
    "EducationHistoryRecord__c.GraduationDate__c": "eqheDate",
    "EducationHistoryRecord__c.City__c": "eqheCity",
    "EducationHistoryRecord__c.Country__c": "eqhecountry",
    "EducationHistoryRecord__c.AwardDegree__c": "eqheTitle",
  },
};
