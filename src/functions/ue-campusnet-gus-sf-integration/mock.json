{"Records": [{"messageId": "8fe1cd3f-b821-4b12-b62c-f5200e5be6b6", "receiptHandle": "AQEBAi9rELbcjbdEInRwo/1O6XzsXKtJHrmq4ZAFE2E+e04bY0fq+3aFRZYYyAbVga2TSPlMa/1M5tm0dPue5OTKT1xtSXcuHSkxYwEDv9KnRfXgH1YMteX+gPe+kxqyO5KseVv12ZpsepCTqpVpddvKw+e6/o+maJveXL7mPR17HJwSnlxLIVwt2D+J3q43GP/rC8qNrebCrWMKmtTP1hRA5qKRZnBxk+qohU1Mmi7l8gPKMBUXPBxAZas1owJTfFNH1aqzeU5SX3UJXFy8WLOGWCpkQT5FYnhDMgVlaDlmSdDH+yXFcWwEg8AHfRdT7HqWGNVHPEaXNNx0hxL3IYMFcA==", "body": {"name": "cn-application", "data": {"ExternalId": "c6e5a9d4-994f-11ef-9426-7a8797da8de2", "StageName": "Offer", "StudentId": "50926184"}}, "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "1717414194253", "SequenceNumber": "18886402107438319616", "MessageGroupId": "72877fce-6b72-4e76-ae2a-aff2729afb27", "SenderId": "AIDAWYJAWPFU7SUQGUJC6", "MessageDeduplicationId": "db2d545663f74137a82c9a531d58698aafec28a0632d8aeae8fc8ca6b9693603", "ApproximateFirstReceiveTimestamp": "1717414194253"}, "messageAttributes": {}, "md5OfBody": "bf93a55040ec25053661e7f9e09a4633", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:eu-west-1:933713876074:DEV-GUS-EDUCATIONCLOUD-HZU-SF-INTEGRATION-QUEUE.fifo", "awsRegion": "eu-west-1"}]}