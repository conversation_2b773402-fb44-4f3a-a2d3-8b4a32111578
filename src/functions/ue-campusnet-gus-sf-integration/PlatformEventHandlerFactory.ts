// PlatformEventHandlerFactory.ts
import { getRegisteredHandler } from "./HandlerRegistry";

// **Ensure all decorated handlers are loaded so they can register themselves**  
import "./eventHandlers/applicationHandler";
import "./eventHandlers/followUpTaskHandler";
import "./eventHandlers/documentApprovalHandler";
import "./eventHandlers/documentUploadHandler";
import "./eventHandlers/visaApprovalHandler";
import "./eventHandlers/paymentHandler";

export class PlatformEventHandlerFactory {
  static getHandler(scenario: string) {
    const handler = getRegisteredHandler(scenario);
    if (!handler) {
      console.error(`No handler found for scenario: ${scenario}`);
    }
    return handler;
  }
}
