import { handlerPath } from "@libs/handler-resolver";

export const UECampusNetGusSfIntegration = {
  handler: `${handlerPath(
    __dirname
  )}/UECampusNetGusSfIntegrationService.handleOutboundIntegration`,
  name: "ue-campusnet-gus-sf-integration-${self:provider.stage}",
  events: [
    {
      sqs: {
        arn: "${self:provider.environment.UE_CAMPUSNET_GUS_SF_INTEGRATION_SQS_QUEUE_ARN}",
      },
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 180,
  memorySize: 512,
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};

export const UECampusNetgusSfFailedRecordProcessor = {
  handler: `${handlerPath(
    __dirname
  )}/UECampusNetGusSfIntegrationService.handleFailedRecords`,
  name: "ue-campusnet-gus-sf-failed-record-processor-${self:provider.stage}",
  events: [
    {
      schedule: "rate(60 minutes)",
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 180,
  memorySize: 512,
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};
