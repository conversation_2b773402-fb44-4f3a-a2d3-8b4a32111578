import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { v4 as uuid } from "uuid";
import { <PERSON>Hand<PERSON> } from "../HandlerRegistry";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();

@RegisterHandler("UEG_DOCUMENT_ISSUED")
export class UploadDocumentHandler implements ICampusNetOutboundHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private errors: Error[] = [];
  async fetchGusSFDetails(applicationFormId: string): Promise<any> {
    try {
      await this.log(
        applicationFormId,
        `fetch opportunity id by application form id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED
      );
      const apiKey = process.env.UE_KEY;
      return await getData(
        `gus/opportunityId/${applicationFormId}?scenario=${this.usecase}`,
        this.correlationId,
        apiKey
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`);
    }
  }
  async syncToGus(documentUpdateRequest: any): Promise<any> {
    try {
      await postData(
        `gus/opportunityfile`,
        documentUpdateRequest,
        this.correlationId,
        process.env.UE_KEY
      );
    } catch (error) {
      await this.error(
        documentUpdateRequest,
        error,
        loggerEnum.Event.SYNC_DOCUMENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(new Error(`Error syncing document details: ${error}`));
      throw new Error(`Error syncing document details: ${error}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    this.correlationId = event?.correlationId || uuid();
    this.usecase = event?.scenario || "UEG_DOCUMENT_ISSUED";
    this.brand = "UEG";
    const { ExternalId, Comment__c, Files: files = [], BucketName } = event;
    this.applicationFormId = ExternalId;
    try {
      await this.log(
        event,
        `sync document upload task initiated`,
        loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED
      );
      if (!this.applicationFormId) {
        await this.error(
          event,
          `Required field applicationFormId not found`,
          loggerEnum.Event.REQUIRED_FIELD_NOT_FOUND
        );
        throw new Error(`Required field not found`);
      }

      const opportunity = await this.fetchGusSFDetails(this.applicationFormId);
      if (!opportunity?.Id) {
        await this.error(
          event,
          `No opportunity found for ${this.applicationFormId}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND
        );
        throw new Error(`No opportunity found for ${this.applicationFormId}`);
      }
      await this.log(
        event,
        `fetch opportunity by ApplicationFormId completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED
      );

      // Handle document upload if files exist
      if (files.length > 0) {
        const documentPromises = files.map((file) => {
          const documentUpdateRequest = {
            Comment__c: Comment__c || "",
            DocumentType__c: file?.DocumentType__c,
            ApplicationId__c: this.applicationFormId,
            Name: file?.Name,
            FilePath__c: file?.link,
            Opportunity__c: opportunity.Id,
            FullUrl__c: file?.S3FileName__c,
            OriginalValue__c: file?.Name,
            S3FileName__c: file?.S3FileName__c,
            BucketName__c: BucketName
              ? BucketName
              : process.env.UE_BUCKET_NAME,
            DocumentSource__c: "UEG",
            Status__c: "Accepted",
          };
          console.log("documentUpdateRequest", documentUpdateRequest);
          this.log(
            event,
            `Syncing document for file: ${file?.Name}`,
            loggerEnum.Event.SYNC_DOCUMENT_REQUEST
          );
          return this.syncToGus(documentUpdateRequest);
        });
        // Await all document syncs
        await Promise.all(documentPromises);

        if (this.errors.length > 0) {
          const errorMessage = this.errors
            .map((error, index) => `${index + 1}. ${error.message}`)
            .join("\n");
          this.errors = [];
          throw new Error(`Error occurred:\n${errorMessage}`);
        }
      }
      await this.log(
        event,
        `sync opportunity document completed`,
        loggerEnum.Event.OPERATION_COMPLETED
      );
    } catch (error) {
      await this.error(event, error, loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED);
      throw error;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
