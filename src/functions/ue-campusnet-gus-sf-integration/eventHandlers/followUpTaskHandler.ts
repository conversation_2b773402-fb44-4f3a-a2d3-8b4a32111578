import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { v4 as uuid } from "uuid";
import { LoggerEnum } from "@gus-eip/loggers";
import { <PERSON>Hand<PERSON> } from "../HandlerRegistry";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();

@RegisterHandler("UEG_FOLLOW_UP_TASK")
export class FollowUpTaskHandler implements ICampusNetOutboundHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(applicationFormId: string): Promise<any> {
    try {
      await this.log(
        applicationFormId,
        `fetch opportunity id by application form id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      const apiKey = process.env.UE_KEY;
      return await getData(
        `gus/opportunityId/${applicationFormId}?scenario=${this.usecase}`,
        this.correlationId,
        apiKey
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`);
    }
  }
  async syncToGus(event: unknown, updateById?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${updateById}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UE_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    this.correlationId = event?.correlationId || uuid();
    this.usecase = event?.scenario || "UEG_FOLLOW_UP_TASK";
    this.brand = "UE";
    const { ExternalId, Description } = event;
    this.applicationFormId = ExternalId;
    try {
      await this.log(
        event,
        `follow up task handler initiated`,
        loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED
      );
      if (!this.applicationFormId) {
        await this.error(
          event,
          `Required field applicationFormId not found`,
          loggerEnum.Event.REQUIRED_FIELD_NOT_FOUND
        );
        throw new Error(`Required field not found`);
      }
      const opportunity = await this.fetchGusSFDetails(this.applicationFormId);
      if (!opportunity?.Id) {
        await this.error(
          event,
          `No opportunity found for ${this.applicationFormId}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${this.applicationFormId}`);
      }
      await this.log(
        event,
        `fetch opportunity by ApplicationFormId completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        this.applicationFormId,
        opportunity,
        "Opportunity"
      );

      const opportunityUpdateRequest: any = {
        AdmissionsStage__c: "Further clarification required",
        Admissions_Condition__c : Description,
        Admission_Condition__c : Description
      };
      await this.log(
        event,
        `sync opportunity details initiated`,
        loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityUpdateRequest,
        {},
        "Opportunity"
      );
      console.log("opportunityUpdateRequest", opportunityUpdateRequest);
      await this.syncToGus(opportunityUpdateRequest, opportunity.Id);
      await this.log(
        event,
        `sync opportunity details completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw error;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
