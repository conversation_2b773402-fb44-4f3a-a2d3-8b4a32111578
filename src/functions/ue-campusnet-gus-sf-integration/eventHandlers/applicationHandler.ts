import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
import { v4 as uuid } from "uuid";
import { opportunityFieldMapping } from "../salesforceFieldMap";
import { RegisterHandler } from "../HandlerRegistry";
import { SnsService } from "src/common/snsService";
import { outboundBacklogService } from "src/common/outboundEventBacklog";

@RegisterHandler("UEG_APPLICATION_SYNC")
export class ApplicationHandler implements ICampusNetOutboundHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private errors: Error[] = [];
  private snsService = new SnsService();

  async fetchGusSFDetails(applicationFormId: string): Promise<any> {
    try {
      await this.log(
        applicationFormId,
        `fetch opportunity id by application form id initiated $`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED
      );
      const apiKey = process.env.UE_KEY;
      return await getData(
        `gus/opportunityId/${applicationFormId}?scenario=${this.usecase}`,
        this.correlationId,
        apiKey
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`);
    }
  }
  async syncToGus(event: unknown, opportunityId?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UE_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(
        new Error(`Error syncing opportunity details: ${error}`)
      );
      throw new Error(`Error syncing opportunity details: ${error}`);
    }
  }
  async handleMessage(event: any): Promise<any> {
    try {
      this.correlationId = event?.correlationId || uuid();
      this.usecase = event?.scenario || "UEG_APPLICATION_SYNC";
      await this.log(
        event,
        `UE Campusnet GUS SF Application sync initiated`,
        loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED
      );
      this.applicationFormId = event?.ExternalId;

      if (!this.applicationFormId) {
        console.error("Missing ApplicationFormId in event message");
        await this.error(
          event,
          "ApplicationFormId not found",
          loggerEnum.Event.SYNC_IN_GUS_SF_FAILED
        );
        throw new Error("Invalid event message: Missing ApplicationFormId");
      }

      const opportunity = await this.fetchGusSFDetails(this.applicationFormId);
      if (!opportunity?.Id) {
        await this.error(
          event,
          `No opportunity found for ${this.applicationFormId}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${this.applicationFormId}`);
      }

      await this.log(
        event,
        `Fetch opportunity completed for ${this.applicationFormId}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      // Generate the payload dynamically
      const opportunityUpdateRequest = await this.buildOpportunityUpdatePayload(
        event
      );
      if (Object.keys(opportunityUpdateRequest).length > 0) {
        await this.log(
          event,
          `Sync opportunity details initiated`,
          loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED
        );
        if (opportunityUpdateRequest["StageName"] === "Payment") {
          opportunityUpdateRequest["DateMDA__c"] = new Date().toISOString();
        }
        console.log("opportunityUpdateRequest", opportunityUpdateRequest);
        await this.syncToGus(opportunityUpdateRequest, opportunity.Id);

        if (event.StageName === "Payment") {
          // Step 1: Sync completed
          await this.log(
            event,
            `Sync opportunity details completed`,
            loggerEnum.Event.SYNC_IN_GUS_SF_COMPLETED,
            loggerEnum.Component.GUS_SALESFORCE
          );

          // Step 2: Check backlog for pending VISA event
          const backlogTableName = `gus-eip-outbound-event-backlog-${process.env.stage}`;
          const existingBacklog = await outboundBacklogService.get(
            backlogTableName,
            this.applicationFormId,
            "Visa"
          );
          if (existingBacklog && existingBacklog.status === "pending") {
            console.log(
              "✅ Found deferred Visa backlog event → republishing..."
            );

            const cleanPayload = existingBacklog.payload;

            const publishResponse = await this.snsService.publishMessages(
              cleanPayload,
              this.applicationFormId,
              process.env.UE_CN_OUTBOUND_TOPIC_ARN
            );

            console.log("📤 Re-published Visa event →", publishResponse);
          }
        }
        await this.log(
          event,
          `Sync opportunity details completed`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE
        );
      } else {
        console.log("No valid fields found in the event to update.");
      }
    } catch (error) {
      await this.error(event, error, loggerEnum.Event.SYNC_IN_GUS_SF_FAILED);
      throw error;
    }
  }

  private async buildOpportunityUpdatePayload(
    eventData: any
  ): Promise<Record<string, any>> {
    const payload: Record<string, any> = {};

    for (const [inputField, salesforceField] of Object.entries(
      opportunityFieldMapping
    )) {
      if (eventData[inputField] !== undefined) {
        payload[salesforceField] = eventData[inputField];
      }
    }

    return payload;
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
