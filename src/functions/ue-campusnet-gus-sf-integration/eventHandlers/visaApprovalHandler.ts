import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();
import { v4 as uuid } from "uuid";
import { visaFieldMapping } from "../salesforceFieldMap";
import { RegisterHandler } from "../HandlerRegistry";
import { outboundBacklogService } from "src/common/outboundEventBacklog";

@RegisterHandler("UEG_VISA_ISSUED")
export class VisaHandler implements ICampusNetOutboundHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private errors: Error[] = [];
  async fetchGusSFDetails(applicationFormId: string): Promise<any> {
    try {
      await this.log(
        applicationFormId,
        `fetch opportunity id by application form id initiated $`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED
      );
      const apiKey = process.env.UE_KEY;
      return await getData(
        `gus/opportunityId/${applicationFormId}?scenario=${this.usecase}`,
        this.correlationId,
        apiKey
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`);
    }
  }
  async fetchOpportunityDetails(opportunityId: string): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `featch opportunity details by opportunity id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED
      );
      const apiKey = process.env.UE_KEY;
      return await getData(
        `gus/getapplicationsdetails/${opportunityId}?scenario=${this.usecase}`,
        this.correlationId,
        apiKey
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`);
    }
  }
  async syncToGus(event: unknown, opportunityId?: unknown): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UE_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      this.errors.push(
        new Error(`Error syncing opportunity details: ${error}`)
      );
    }
  }
  async handleMessage(event: any): Promise<any> {
    try {
      this.applicationFormId = event?.ExternalId;

      if (!this.applicationFormId) {
        console.error("Missing ApplicationFormId in event message");
        await this.error(
          event,
          "ApplicationFormId not found",
          loggerEnum.Event.SYNC_IN_GUS_SF_FAILED
        );
        throw new Error("Invalid event message: Missing ApplicationFormId");
      }

      // Step 1: Check if Visa backlog item exists with status 'pending'
      const existingBacklog = await outboundBacklogService.get(
        `gus-eip-outbound-event-backlog-${process.env.stage}`,
        this.applicationFormId,
        "Visa"
      );

      if (existingBacklog?.status === "pending") {
        this.correlationId = uuid(); // override with new correlation ID
      } else {
        this.correlationId = event?.correlationId || uuid();
      }

      this.usecase = event?.scenario || "UEG_VISA_ISSUED";

      await this.log(
        event,
        `UE Campusnet GUS SF Application sync initiated`,
        loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED
      );

      const opportunity = await this.fetchGusSFDetails(this.applicationFormId);
      if (!opportunity?.Id) {
        await this.error(
          event,
          `No opportunity found for ${this.applicationFormId}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`No opportunity found for ${this.applicationFormId}`);
      }

      await this.log(
        event,
        `Fetch opportunity completed for ${this.applicationFormId}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunity
      );
      // fetch opportunity details
      const applicationDetails = await this.fetchOpportunityDetails(
        opportunity.Id
      );
      if (!applicationDetails?.Opportunity?.StageName) {
        await this.error(
          event,
          `No opportunity found for ${this.applicationFormId}`,
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
          loggerEnum.Component.GUS_SALESFORCE,
          applicationDetails
        );
        throw new Error(`No opportunity found for ${this.applicationFormId}`);
      }
      // Stage check
      if (applicationDetails?.Opportunity?.StageName !== "Payment") {
        await this.log(
          event,
          `VISA event deferred – opportunity stage is '${opportunity?.StageName}', not 'Payment'`,
          "DEFERRED"
        );
        const cleanEventData = { ...event };
        delete cleanEventData.scenario;

        const visaBacklogPayload = {
          applicationId: this.applicationFormId,
          eventType: "Visa",
          brand: "UEG",
          status: "pending",
          timestamp: new Date().toISOString(),
          payload: {
            name: "cn-visa-approval",
            data: cleanEventData,
          },
        };

        await outboundBacklogService.store(
          `gus-eip-outbound-event-backlog-${process.env.stage}`,
          visaBacklogPayload
        );

        console.log(
          "ℹ️ VISA event deferred due to stage !== Payment. Stored in backlog."
        );
        await this.log(
          event,
          `VISA event received before Payment stage. Queued and stored in OutboundEventBacklog.`,
          loggerEnum.Event.OPERATION_COMPLETED
        );
        return;
      }

      const opportunityUpdateRequest = await this.buildOpportunityUpdatePayload(
        event
      );
      opportunityUpdateRequest["StageName"] = "Visa";

      if (Object.keys(opportunityUpdateRequest).length > 0) {
        await this.log(
          event,
          `Sync opportunity details initiated`,
          loggerEnum.Event.SYNC_OPPORTUNITY_INITIATED
        );

        await this.syncToGus(opportunityUpdateRequest, opportunity.Id);

        await outboundBacklogService.markProcessed(
          `gus-eip-outbound-event-backlog-${process.env.stage}`,
          this.applicationFormId,
          "Visa"
        );

        const logMessage = existingBacklog
          ? "VISA event reprocessed post-payment and successfully updated in Salesforce."
          : "VISA processed and successfully updated in Salesforce.";

        await this.log(event, logMessage, loggerEnum.Event.OPERATION_COMPLETED);
      } else {
        console.log("No valid fields found in the event to update.");
      }
    } catch (error) {
      await this.error(event, error, loggerEnum.Event.SYNC_IN_GUS_SF_FAILED);
      throw error;
    }
  }

  private async buildOpportunityUpdatePayload(
    eventData: any
  ): Promise<Record<string, any>> {
    const payload: Record<string, any> = {};

    for (const [inputField, salesforceField] of Object.entries(
      visaFieldMapping
    )) {
      if (eventData[inputField] !== undefined) {
        payload[salesforceField] = eventData[inputField];
      }
    }

    return payload;
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
