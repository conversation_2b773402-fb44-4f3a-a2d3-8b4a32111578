import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { v4 as uuid } from "uuid";
import { <PERSON>Hand<PERSON> } from "../HandlerRegistry";
const loggerEnum = new LoggerEnum();
const loggerService = new LoggerService();

@RegisterHandler("UEG_SYNC_DOCUMENT_STATUS")
export class DocumentApprovalHandler implements ICampusNetOutboundHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  async fetchGusSFDetails(applicationFormId: string): Promise<any> {
    try {
      await this.log(
        applicationFormId,
        `fetch opportunity id by application form id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      const apiKey = process.env.UE_KEY;
      return await getData(
        `gus/opportunityId/${applicationFormId}?scenario=${this.usecase}`,
        this.correlationId,
        apiKey
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`);
    }
  }
  async fetchOpportunityDetails(
    s3FileName: string,
    opportunityId: string,
    documentType: string
  ): Promise<any> {
    try {
      await this.log(
        this.applicationFormId,
        `fetch opportunity id by application form id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      const apiKey = process.env.UE_KEY;
      return await getData(
        `gus/opportunityfilesByS3Filename?s3FileName=${s3FileName}&opportunityId=${opportunityId}&documentType=${documentType}&scenario=${this.usecase}`,
        this.correlationId,
        apiKey
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityfile details: ${error}`);
    }
  }
  async syncToGus(event: unknown): Promise<any> {
    try {
      const publishResponse = await postData(
        `gus/publishrejecteddocumentevent`,
        event,
        this.correlationId
      );
      return publishResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error syncing in GUS: ${error}`);
    }
  }

  async updateOpportunityFile(fileId: string, event: any): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateopportunityfile/${fileId}?scenario=${this.usecase}`,
        event,
        this.correlationId,
        process.env.UE_KEY
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
    }
  }

  async handleMessage(event: any): Promise<any> {
    this.correlationId = event.correlationId || uuid();
    this.usecase = event?.scenario || "UEG_SYNC_DOCUMENT_STATUS";
    this.brand = "UEG";

    let {
      ExternalId: externalId,
      RejectionComments__c: rejectionComments,
      OpportunityFileId: opportunityFileId,
      S3FileName__c: s3FileName,
      DocumentType__c: documentType,
      DocumentStatus__c: documentStatus, // Accept or Reject
    } = event;

    this.applicationFormId = externalId;

    try {
      await this.log(
        event,
        `Document handler initiated`,
        loggerEnum.Event.FOLLOW_UP_TASK_INITIATED
      );

      if (!opportunityFileId) {
        if (!externalId) {
          await this.error(
            event,
            `Required field applicationFormId not found`,
            loggerEnum.Event.REQUIRED_FIELD_NOT_FOUND,
            this.brand
          );
          throw new Error(`Required field applicationFormId not found`);
        }

        const opportunity = await this.fetchGusSFDetails(externalId);
        const opportunityId = opportunity?.Id;

        if (!opportunityId) {
          await this.error(
            event,
            `No opportunity found for ${externalId}`,
            loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
            loggerEnum.Component.GUS_SALESFORCE
          );
          throw new Error(`No opportunity found for ${externalId}`);
        }

        const opportunityFiles = await this.fetchOpportunityDetails(
          s3FileName,
          opportunityId,
          documentType
        );

        if (!opportunityFiles.length) {
          await this.error(
            event,
            `No opportunity file found for ${externalId}`,
            loggerEnum.Event.OPPORTUNITY_NOT_FOUND,
            loggerEnum.Component.GUS_SALESFORCE
          );
          throw new Error(
            `OpportunityFileId not present for S3 file ${s3FileName}`
          );
        }

        opportunityFileId = opportunityFiles[0].Id;
      }

      if (documentStatus === "Accept") {
        const updatePayload = {
          DocumentStatus__c: "Accept",
          Status__c: "Accept",
        };
        console.log("updatePayload", updatePayload);
        const response = await this.updateOpportunityFile(
          opportunityFileId,
          updatePayload
        );

        await this.log(
          event,
          `Opportunity file status updated as Accept`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          updatePayload,
          response
        );
      } else if (documentStatus === "Reject") {
        const opportunity = await this.fetchGusSFDetails(externalId);
        const opportunityId = opportunity?.Id;

        const publishEventPayload = {
          Brand__c: this.brand,
          Document_Status__c: "Rejected",
          Opportunity_File_Id__c: opportunityFileId,
          Opportunity_Id__c: opportunityId,
          Rejection_Comments__c: rejectionComments,
        };
        console.log("publishEventPayload", publishEventPayload);
        const publishResponse = await this.syncToGus(publishEventPayload);

        await this.log(
          event,
          `Rejected document event published`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          publishEventPayload,
          publishResponse
        );
      } else {
        await this.error(
          event,
          `Invalid document status: ${documentStatus}`,
          loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
          loggerEnum.Component.GUS_SALESFORCE
        );
        throw new Error(`Invalid document status: ${documentStatus}`);
      }

      await this.log(
        event,
        `Document handler completed`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE
      );
    } catch (error) {
      await this.error(
        event,
        error,
        loggerEnum.Event.SYNC_IN_GUS_EVENT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
  async error(
    sourcePayload,
    errorMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    console.log(
      "error",
      this.correlationId,
      errorMessage,
      event,
      destination,
      destination
    );
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
