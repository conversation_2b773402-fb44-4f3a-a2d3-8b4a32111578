import { PlatformEventHandlerFactory } from "./PlatformEventHandlerFactory";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { storeFailedRecords } from "src/common/storeFailedRecords";
import { checkExistingMessageGroupId } from "src/common/checkFailedRecords";
import { DynamoDBService } from "src/common/dynamodbService";
import { SnsService } from "src/common/snsService";
import { v4 as uuidv4 } from "uuid";
import { ScenarioMapping } from "./enums/scenario-mapping.enum";
import { SQSEvent } from "aws-lambda";

const dbService = new DynamoDBService();
const snsService = new SnsService();
const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const correlationId = uuidv4();
let applicationFormId: string;
let brand = "UEG";
let scenario = "";
export const handleOutboundIntegration = async (event: SQSEvent) => {
  try {
    console.log("event", event);
    const clonedEvent = deepClone(event);
    const responses = [];
    for (const record of clonedEvent.Records) {
      const eventBody = JSON.parse(record.body);
      // Check if messageGroupId is default, then replace it
      if (record.attributes.MessageGroupId === "default") {
        const newMessageGroupId = uuidv4();
        record.attributes.MessageGroupId = newMessageGroupId;
        console.log(`🔁 Replaced messageGroupId with: ${newMessageGroupId}`);
      }
      const platformEventMessage = JSON.parse(eventBody.Message);
      platformEventMessage.eventId = record?.messageId;
      console.log("platformEventMessage", platformEventMessage);
      const isFailedMessageGroupData = await checkExistingMessageGroupId(
        record,
        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
        "UE_CAMPUSNET_GUS_SF"
      );
      console.log("isFailedMessageGroupData ", isFailedMessageGroupData);
      if (isFailedMessageGroupData === "No messages to process") {
        try {
          const eventBody = JSON.parse(record.body);
          scenario = getMappedScenario(platformEventMessage.name);
          eventBody.data = {
            ...eventBody.data,
            scenario,
          };
          console.log("PlatformEventMessage", platformEventMessage);
          applicationFormId = platformEventMessage.data?.ExternalId;
          const handler = PlatformEventHandlerFactory.getHandler(scenario);
          if (handler && typeof handler.handleMessage === "function") {
            const response = await handler.handleMessage(
              platformEventMessage.data
            );
            console.log("response -->", response);

            if (response && platformEventMessage.status === "Failed") {
              await dbService.deleteItem(
                process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                {
                  PK: `UE_CAMPUSNET_GUS_SF#${record?.attributes?.MessageGroupId}`,
                  SK: platformEventMessage?.uuid || record?.messageId,
                }
              );

              const queryParams = {
                TableName:
                  process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                KeyConditionExpression: "PK = :pkValue",
                ExpressionAttributeValues: {
                  ":pkValue": `UE_CAMPUSNET_GUS_SF#${record?.attributes?.MessageGroupId}`,
                },
              };

              const checkExcistingFailedRecordForMessageGrpId =
                await dbService.queryObjects(queryParams);
              console.log(
                "checkExcistingFailedRecordForMessageGrpId -->",
                checkExcistingFailedRecordForMessageGrpId
              );

              if (
                checkExcistingFailedRecordForMessageGrpId.Items.length === 0
              ) {
                await dbService.deleteItem(
                  process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                  {
                    PK: "UE_CAMPUSNET_GUS_SF",
                    SK: record?.attributes?.MessageGroupId,
                  }
                );
              }
            }
            console.log("Response:", response);

            responses.push(response);
          } else {
            console.log("Response:", null);
            responses.push(null);
            throw new Error(
              `No handler found for scenario: ${platformEventMessage.name}`
            );
          }
        } catch (error) {
          console.log("ERROR ->", error);
          await storeFailedRecords(
            record,
            process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            "UE_CAMPUSNET_GUS_SF"
          );
          const platformEventMessage = { ...eventBody.data, correlationId };
          await loggerService.error(
            correlationId,
            new Date().toISOString(),
            loggerEnum.Component
              .GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
            loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
            loggerEnum.Component.GUS_EIP_SERVICE,
            loggerEnum.Event.SYNC_APPLICATION_STATUS_UPDATE,
            scenario,
            platformEventMessage,
            platformEventMessage,
            error.message
              ? JSON.stringify(error.message)
              : JSON.stringify(error),
            brand,
            "",
            "Application_Form_Id__c",
            applicationFormId,
            "Opportunity",
            applicationFormId,
            "Opportunity",
            ""
          );
        }
      } else {
        await loggerService.log(
          correlationId,
          new Date().toISOString(),
          loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
          loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
          loggerEnum.Component.GUS_SALESFORCE,
          loggerEnum.Event.OPERATION_COMPLETED,
          scenario,
          platformEventMessage,
          {},
          "A record with the same messageGroupId failed earlier, so this record can't proceed further",
          brand,
          applicationFormId,
          "Application_Form_Id__c",
          applicationFormId,
          "",
          "",
          "",
          applicationFormId
        );
      }
    }
    return responses;
  } catch (error) {
    console.log("ERROR ->", error);
    await loggerService.error(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_HANDLER,
      loggerEnum.Component.GUS_SALESFORCE_UE_CAMPUSNET_INTEGRATION_QUEUE,
      loggerEnum.Component.GUS_EIP_SERVICE,
      loggerEnum.Event.SYNC_APPLICATION_STATUS_UPDATE,
      scenario,
      "",
      "",
      error.message ? JSON.stringify(error.message) : JSON.stringify(error),
      brand,
      "",
      "Application_Form_Id__c",
      applicationFormId,
      "Opportunity",
      applicationFormId,
      "Opportunity",
      ""
    );
    return {
      status: "Failed",
      message: error.message,
    };
  }
};

const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item));
  }
  const clonedObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }
  return clonedObj;
};

function getMappedScenario(eventName: string): string {
  return ScenarioMapping[eventName] || eventName; // Fallback to the original if no mapping exists
}

export const handleFailedRecords = async () => {
  try {
    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": "UE_CAMPUSNET_GUS_SF",
      },
    };

    const partitionResponse = await dbService.queryObjects(params);
    console.log("PartitionItemData -->", partitionResponse);
    if (partitionResponse.Items && partitionResponse.Items.length > 0) {
      for (const partitionItem of partitionResponse.Items) {
        console.log("Item -->", partitionItem);
        if (
          partitionItem.status === "Failed" &&
          (partitionItem.retryCount <= 3 || !partitionItem.retryCount)
        ) {
          const params = {
            TableName:
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            KeyConditionExpression: "PK = :partitionKey",
            ExpressionAttributeValues: {
              ":partitionKey": `${partitionItem.PK}#${partitionItem.SK}`,
            },
          };
          const records = await dbService.queryObjects(params);
          console.log("Records -->", records);
          for (const record of records.Items) {
            console.log("Current Record -->", record);
            const eventBody = JSON.parse(record.body);
            const eventMessage = JSON.parse(eventBody.Message);
            eventMessage.status = "Failed";
            console.log("Event message ->", eventMessage);
            const applicationFormId = eventMessage?.data?.ExternalId;
            const publishMessages = await snsService.publishMessages(
              eventMessage,
              applicationFormId,
              process.env.UE_CN_OUTBOUND_TOPIC_ARN
            );
            console.log("publishMessages", publishMessages);
          }
          const currentRetryCount = partitionItem.retryCount || 0;
          await dbService.updateObject(
            process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            {
              PK: partitionItem.PK,
              SK: partitionItem.SK,
            },
            {
              retryCount: currentRetryCount + 1,
            }
          );
        }
      }
      console.log("All records processed successfully");
      return "All records processed successfully";
    } else {
      return "No records to process";
    }
  } catch (error) {
    throw new Error(error);
  }
};
