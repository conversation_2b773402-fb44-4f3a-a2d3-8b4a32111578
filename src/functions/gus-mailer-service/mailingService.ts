import { sendEmail } from "src/common/sesService";
import { hzuPostDepositTemplate, hzuPostDepositeSubject, hzuPreDepositTemplate, hzuPreDepositSubject } from "./emailtemplates/HZU";
import { limPostDepositTemplate, limPostDepositeSubject, limPreDepositTemplate, limPreDepositSubject } from "./emailtemplates/LIM College";
import { ucwPostDepositTemplate, ucwPostDepositeSubject, ucwPreDepositTemplate, ucwPreDepositSubject } from "./emailtemplates/UCW";
import { postData } from "src/connectors/eip-connector";


export const gusMailingService = async (event) => {
  try {
    console.log("platformEventMessage", event);
    const {
      Email__c,
      Scenario__c,
      Programme_Name_Duplicate_Opp__c,
      Programme_Name_Original_Opp__c,
      Submission_Date__c,
      BusinessUnitFilter__c,
      Student_Name__c,
      Form_Link__c,
      Deposit_Date__c,
      Agent_Contact_Info__c,
    } = event.payload;

    const toAddress = Email__c;
    const fromAddress = process.env.GUS_SENDER_EMAIL;

    let replacements;
    let template, subject;

    switch (Scenario__c.toUpperCase()) {
      case "PRE_DEPOSIT": {
        const date = new Date(Submission_Date__c);
        const formattedSubmissionDate = new Intl.DateTimeFormat("en-GB", {
          day: "numeric",
          month: "long",
          year: "numeric",
        }).format(date);

        replacements = {
          "Student Name": Student_Name__c,
          "AppB Programme Name": Programme_Name_Duplicate_Opp__c,
          "Institution Name": BusinessUnitFilter__c,
          "AppA Programme Name": Programme_Name_Original_Opp__c,
          "Date of submission": formattedSubmissionDate,
          "Direct Sales/ Agent Contact Info": Agent_Contact_Info__c,
          Link: Form_Link__c,
          AgentOrDirectSale: "<EMAIL>",
        };

        const emailData = emailTemplates(BusinessUnitFilter__c, Scenario__c);
        template = replacePlaceholders(emailData.template, replacements);
        subject = replacePlaceholders(emailData.subject, replacements);
        break;
      }

      case "POST_DEPOSIT": {
        const date = new Date(Deposit_Date__c);
        const formattedDepositedDate = new Intl.DateTimeFormat("en-GB", {
          day: "numeric",
          month: "long",
          year: "numeric",
        }).format(date);
        replacements = {
          "Student Name": Student_Name__c,
          "AppB Programme Name": Programme_Name_Duplicate_Opp__c,
          "Institution Name": BusinessUnitFilter__c,
          "AppA Programme Name": Programme_Name_Original_Opp__c,
          "deposit date paid": formattedDepositedDate,
        };

        const emailData = emailTemplates(BusinessUnitFilter__c, Scenario__c);
        template = replacePlaceholders(emailData.template, replacements);
        subject = replacePlaceholders(emailData.subject, replacements);
        break;
      }

      default:
        throw new Error(`Unsupported scenario: ${Scenario__c}`);
    }

    const API_KEY = BusinessUnitFilter__c === "LIM College" ? process.env.LIM_KEY : process.env[`${BusinessUnitFilter__c}_KEY`]

    const emailMessageResponse = await postData(`gus/emailmessage`, {
      HtmlBody: template,
      Subject: subject,
      FromAddress: fromAddress,
      ToAddress: toAddress,
      Email_Status__c: "New"
    }, '', API_KEY)
    console.log('emailMessageResponse ->', emailMessageResponse)
    const sentEmailResponse = await sendEmail(toAddress, fromAddress, subject, template, Scenario__c.toUpperCase());
    console.log('sentEmailResponse ->', sentEmailResponse)
    const updateEmailMessage = await postData(`gus/emailmessage/${emailMessageResponse?.data?.id}`, {
      MessageDate: new Date().toISOString(),
      MessageIdentifier: sentEmailResponse?.MessageId,
      Email_Status__c: "Send"
    }, sentEmailResponse?.MessageId, API_KEY, 'PATCH')
    console.log('Update email message', updateEmailMessage)

    return updateEmailMessage
  } catch (error) {
    console.log("Error", JSON.stringify(error))
    throw new Error(error)
  }
};

function replacePlaceholders(template, replacements) {
  console.log("Replacements Keys:", Object.keys(replacements));
  return template.replace(/\[([^\]]+)\]/g, (match, key) => replacements[key] || match);
}

const emailData = {
  HZU: {
    PRE_DEPOSIT: {
      template: hzuPreDepositTemplate,
      subject: hzuPreDepositSubject,
    },
    POST_DEPOSIT: {
      template: hzuPostDepositTemplate,
      subject: hzuPostDepositeSubject,
    },
  },
  LIM: {
    PRE_DEPOSIT: {
      template: limPreDepositTemplate,
      subject: limPreDepositSubject,
    },
    POST_DEPOSIT: {
      template: limPostDepositTemplate,
      subject: limPostDepositeSubject,
    },
  },
  UCW: {
    PRE_DEPOSIT: {
      template: ucwPreDepositTemplate,
      subject: ucwPreDepositSubject,
    },
    POST_DEPOSIT: {
      template: ucwPostDepositTemplate,
      subject: ucwPostDepositeSubject,
    },
  },
};

function emailTemplates(brand, scenario) {
  const brandData = emailData[brand.toUpperCase()];
  if (!brandData) {
    throw new Error(`Unsupported brand: ${brand}`);
  }

  const scenarioData = brandData[scenario.toUpperCase()];
  if (!scenarioData) {
    throw new Error(`Unsupported scenario: ${scenario} for brand: ${brand}`);
  }

  return scenarioData;
}