import { handlerPath } from '@libs/handler-resolver';

export const gusMailer = {
    handler: `${handlerPath(__dirname)}/mailingHandler.gusMailingHandler`,
    name: 'gus-mailer-${self:provider.stage}',
    events: [
        {
            sqs: {
                arn: '${self:provider.environment.GUS_MAILER_SQS_QUEUE_ARN}'
            }
        }
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 180,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};


export const gusDepositEmailStatusTracker = {
    handler: `${handlerPath(__dirname)}/mailingHandler.gusDepositEmailStatusTracker`,
    name: 'gus-deposit-email-status-tracker-${self:provider.stage}',
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    events: [
        {
            sns: {
                arn: '${self:provider.environment.GUS_DEPOSIT_EMAIL_STATUS_TRACKER_ARN}'
            }
        }
    ],
    timeout: 60,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};

