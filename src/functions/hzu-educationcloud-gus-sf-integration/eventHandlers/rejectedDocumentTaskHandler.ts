import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";
import { getData, postData } from "src/connectors/eip-connector";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
export class RejectedDocumentTaskHandler implements PlatformEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private email: string;
  async fetchGusSFOpportunityId(event: any, correlationId: string) {
    try {
      await this.log(
        event,
        `fetch opportunity id by application form id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        event
      );

      return await getData(
        `gus/opportunityId/${event.payload.GUS_Application_Form_ID__c}?scenario=${this.usecase}`,
        correlationId
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`)
    }
  }
  async fetchGusSFDetails(opportunityId: any, correlationId: string) {
    throw new Error("Method not implemented.");
  }
  async syncInHZU(
    event: any,
    transformedData: any,
    correlationId: string
  ): Promise<any> {
    throw new Error("Method not implemented.");
  }
  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Event ->", event);
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      this.correlationId = platformEventMessage.event?.EventUuid;;
      this.applicationFormId = platformEventMessage.payload.GUS_Application_Form_ID__c;
      this.brand = 'HZU';
      this.usecase = platformEventMessage.payload?.Scenario__c;
      this.email = platformEventMessage.payload?.Email_ID__c;
      const gusOpportunityId = await this.fetchGusSFOpportunityId(
        platformEventMessage,
        this.correlationId
      );
      await this.log(
        platformEventMessage,
        `fetch opportunity id by application form id completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage,
        gusOpportunityId
      );
      console.log("GUS opportunity id -->", gusOpportunityId.Id);
      await this.log(
        platformEventMessage,
        `get document status by document checklist id initiated`,
        loggerEnum.Event.GET_DOCUMENT_STATUS_INITIATED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        platformEventMessage
      );
      const documentStatus = await getData(
        `hzu/documentChecklistItemDetailsById/${platformEventMessage.payload.Document_Checklist_Item_ID__c}`
      );
      await this.log(
        platformEventMessage,
        `get document status by document checklist id completed`,
        loggerEnum.Event.GET_DOCUMENT_STATUS_COMPLETED,
        loggerEnum.Component.HZU_EDUCATIONCLOUD,
        platformEventMessage,
        documentStatus
      );

      if (platformEventMessage.payload.GUS_Opportunity_File_ID__c) {
        const publishEventPayload = {
          Brand__c: "HZU",
          Document_Status__c: documentStatus.Status,
          Opportunity_File_Id__c:
            platformEventMessage.payload.GUS_Opportunity_File_ID__c,
          Opportunity_Id__c: gusOpportunityId.Id,
          Rejection_Comments__c: platformEventMessage.payload.Comments__c,
        };
        await this.log(
          platformEventMessage,
          `publish reject document event initiated`,
          loggerEnum.Event.PUBLISH_REJECT_DOCUMENT_EVENT_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          publishEventPayload
        );

        const publishResponse = await postData(
          `gus/publishrejecteddocumentevent`,
          publishEventPayload,
          this.correlationId
        );

        console.log("publishResponse ->", publishResponse);
        if (publishResponse) {
          await this.log(
            platformEventMessage,
            `publish reject document event completed`,
            loggerEnum.Event.OPERATION_COMPLETED,
            loggerEnum.Component.GUS_SALESFORCE,
            publishEventPayload,
            publishResponse
          );
        }
        return publishResponse;
      } else {
        await this.log(
          platformEventMessage,
          `publish reject document task skipped`,
          loggerEnum.Event.OPERATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          platformEventMessage
        );

        return {
          status: true,
          message: `publish reject document task skipped`
        }
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
