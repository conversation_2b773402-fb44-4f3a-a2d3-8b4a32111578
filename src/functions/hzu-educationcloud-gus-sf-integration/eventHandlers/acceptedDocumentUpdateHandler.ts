import { getData, postData } from "src/connectors/eip-connector";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();

export class AcceptedDocumentUpdateHandler implements PlatformEventsHandler {
  private correlationId: string;
  private usecase: string;
  private brand: string;
  private applicationFormId: string;
  private email: string;

  async fetchGusSFDetails(event: any, correlationId?: string): Promise<any> {
    throw new Error("Method not implemented.");
  }

  async syncInHZU(opportunityFileId: any, updateData?: any, correlationId?: string): Promise<any> {
    try{
    const response = await postData(
      `gus/updateopportunityfile/${opportunityFileId}`,
      updateData,
      correlationId
    );
    return response;
    } catch(error) {
      throw new Error(`Error sync in gus salesforce: ${error}`)
    }
  }

  async handleMessage(event: any): Promise<any> {
    try {
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      
      this.correlationId = platformEventMessage.event?.EventUuid;
      this.applicationFormId = platformEventMessage.payload.GUS_Application_Form_ID__c;
      this.brand = 'HZU';
      this.usecase = platformEventMessage.payload?.Scenario__c;
      this.email = platformEventMessage.payload?.Email_ID__c;

      let opportunityFileId = platformEventMessage.payload?.GUS_Opportunity_File_ID__c;

      await this.log(
        platformEventMessage,
        `Update opportunity file initiated`,
        loggerEnum.Event.UPDATE_OPPORTUNITY_FILE_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        opportunityFileId,
        '',
        "OpportunityFile__c",
        opportunityFileId
      );

      const updateData = {
        Additional_Info__c: "Accepted by Institution"
      };

      const response = await this.syncInHZU(
        opportunityFileId,
        updateData,
        this.correlationId
      );

      await this.log(
        platformEventMessage,
        `Update opportunity file completed`,
        loggerEnum.Event.UPDATE_OPPORTUNITY_FILE_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        updateData,
        response,
        "OpportunityFile__c",
        opportunityFileId
      );

      return {
        status: true,
        message: "Opportunity file update completed"
      };
    } catch (error) {
      throw error;
    }
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "DocumentChecklistItem",
      sourcePayload?.payload.Document_Checklist_Item_ID__c,
      response
    );
  }
}
