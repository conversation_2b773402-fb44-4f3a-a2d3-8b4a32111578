import { getData, postData } from "src/connectors/eip-connector";
import { paymentSalesforceConfig } from "../salesforceconfig";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
export class OpportunityPaymentCompletedHandler
  implements PlatformEventsHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private email: string;
  async fetchGusSFOpportunityId(event: any, correlationId: string) {
    try {
      await this.log(
        event,
        `fetch opportunity id by application form id initiated`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE,
        event
      );

      return await getData(
        `gus/opportunityId/${event.payload.GUS_Application_Form_ID__c}?scenario=${this.usecase}`,
        correlationId
      );
    } catch (error) {
      throw new Error(`Error fetching opportunityId: ${error}`)
    }
  }
  async fetchGusSFDetails(opportunityId: any, correlationId: string) {
    return await getData(`hzu/opportunityById/${opportunityId}?scenario=${this.usecase}`, correlationId);
  }
  async syncInHZU(
    event: any,
    transformedData: any,
    correlationId: string
  ): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${event.Id}?scenario=${this.usecase}`,
        transformedData,
        correlationId
      );
      return saveResponse;
    } catch (error) {
      throw new Error(`Error sync in gus salesforce: ${error}`)
    }
  }
  async handleMessage(event: any): Promise<any> {
    try {
      console.log("Inside handle message");
      const eventBody = JSON.parse(event.body);
      const platformEventMessage = JSON.parse(eventBody.Message);
      this.correlationId = platformEventMessage.event?.EventUuid;;
      this.applicationFormId = platformEventMessage.payload.GUS_Application_Form_ID__c;
      this.brand = 'HZU';
      this.usecase = platformEventMessage.payload?.Scenario__c;
      this.email = platformEventMessage.payload?.Email_ID__c
      const opportunity = await this.fetchGusSFOpportunityId(
        platformEventMessage,
        this.correlationId
      );
      await this.log(
        platformEventMessage,
        `fetch opportunity id by application form id completed`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        platformEventMessage,
        opportunity
      );
      if (opportunity) {
        await this.log(
          platformEventMessage,
          `fetch gus application details initiated`,
          loggerEnum.Event.FETCH_GUS_APPLICATION_INTIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          platformEventMessage
        );
        const applicationResponse = await this.fetchGusSFDetails(
          platformEventMessage.payload.OpportunityID__c,
          this.correlationId
        );
        await this.log(
          platformEventMessage,
          `fetch gus application details completed`,
          loggerEnum.Event.FETCH_GUS_APPLICATION_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE,
          platformEventMessage,
          applicationResponse
        );

        console.log("Application res", applicationResponse);
        const transformedData = await this.transformData(
          applicationResponse,
          paymentSalesforceConfig
        );

        if (transformedData.Opportunity) {
          transformedData.Opportunity.StageName = "Payment";
          transformedData.Opportunity.DateMDA__c = new Date().toISOString();
        }
        console.log("Transform data", transformedData);
        await this.log(
          platformEventMessage,
          `sync in gus sf initiated`,
          loggerEnum.Event.SYNC_IN_GUS_SF_INITIATED,
          loggerEnum.Component.GUS_SALESFORCE,
          transformedData.Opportunity
        );
        const response = await this.syncInHZU(
          opportunity,
          transformedData.Opportunity,
          this.correlationId
        );
        if (response) {
          await this.log(
            platformEventMessage,
            `Sync application status update completed`,
            loggerEnum.Event.OPERATION_COMPLETED,
            loggerEnum.Component.GUS_SALESFORCE,
            transformedData,
            response
          );
        }
        console.log("Response", response);
        return response;
      } else {
        throw new Error(
          "Invalid applicationId or No opportunity found for the given applicationId"
        );
      }
    } catch (error) {
      console.log("Error", error);
      throw error;
    }
  }

  async transformData(input: any, mapping: any) {
    const output: any = {};

    for (const [outputKey, fieldsMapping] of Object.entries(mapping)) {
      output[outputKey] = {};

      for (const [inputPath, outputField] of Object.entries(fieldsMapping)) {
        const value = await this.getValueFromPath(input, inputPath);
        if (value !== null && value !== undefined) {
          await this.setValueToPath(output[outputKey], outputField, value);
        }
      }
    }

    return output;
  }

  async getValueFromPath(obj: any, path: string) {
    const keys = path.split(".");
    let currentValue = obj;

    for (const key of keys) {
      if (currentValue[key] !== undefined) {
        currentValue = currentValue[key];
      } else {
        return undefined;
      }
    }

    return currentValue;
  }

  async setValueToPath(obj: any, path: string, value: any) {
    const keys = path.split(".");
    let currentObj = obj;

    keys.forEach((key, index) => {
      if (index === keys.length - 1) {
        currentObj[key] = value;
      } else {
        if (!currentObj[key]) {
          currentObj[key] = {};
        }
        currentObj = currentObj[key];
      }
    });
  }

  async log(
    sourcePayload,
    logMessage,
    event,
    destination?,
    destinationPayload?,
    response?,
    destinationObject?,
    destinationObjectId?
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_HANDLER,
      loggerEnum.Component.HZU_EDUCATIONCLOUD_GUS_SALESFORCE_INTEGRATION_QUEUE,
      destination || loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.email,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
