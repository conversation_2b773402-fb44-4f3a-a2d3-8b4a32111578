import { handlerPath } from '@libs/handler-resolver';

export const hzuEducationcloudGusSfIntegration = {
    handler: `${handlerPath(__dirname)}/hzuEducationcloudGusSfIntegrationService.handleSfRequests`,
    name: 'hzu-educationcloud-gus-sf-integration-${self:provider.stage}',
    events: [
        {
            sqs: {
                arn: '${self:provider.environment.HZU_EDUCATIONCLOUD_GUS_SF_INTEGRATION_SQS_QUEUE_ARN}'
            }
        }
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 180,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};

export const hzuEducationcloudgusSfFailedRecordProcessor = {
    handler: `${handlerPath(__dirname)}/hzuEducationcloudGusSfIntegrationService.handleFailedRecords`,
    name: 'hzu-educationcloud-gus-sf-failed-record-processor-${self:provider.stage}',
    events: [
        {
            schedule: 'rate(60 minutes)',
        },
    ],
    role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
    timeout: 180,
    tags: {
        PROJECT: "EIP",
        ENVIRONMENT: "${self:provider.stage}",
        TEAM: "EIP Development Team"
    }
};

