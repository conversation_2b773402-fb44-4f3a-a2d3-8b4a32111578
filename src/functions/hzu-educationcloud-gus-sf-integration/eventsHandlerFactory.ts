import { OpportunityVisaApprovedHandler } from './eventHandlers/opportunityVisaApprovedHandler';
import { OpportunityOfferHandler } from './eventHandlers/opportunityOfferHandler';
import { OpportunityClosedLostHandler } from './eventHandlers/opportunityClosedLostHandler';
import { OpportunityPaymentCompletedHandler } from './eventHandlers/opportunityPaymentCompletedHandler';
import { OpportunityClosedWonHandler } from './eventHandlers/opportunityClosedWonHandler';
import { DocumentUploadHandler } from './eventHandlers/documentUploadHandler';
import { OpportunityDocumentUploadHandler } from './eventHandlers/opportunityDocumentUploadHandler';
import { OpportunityRevokedClosedLostHandler } from './eventHandlers/opportunityRevokedClosedLostHandler';
import { RejectedDocumentTaskHandler } from './eventHandlers/rejectedDocumentTaskHandler';
import { AcceptedDocumentUpdateHandler } from './eventHandlers/acceptedDocumentUpdateHandler';

export class PlatformEventHandlerFactory {
    static getHandler(scenario): PlatformEventsHandler {
        switch (scenario) {
            case 'OPPORTUNITY_VISA_APPROVED':
                return new OpportunityVisaApprovedHandler();
            case 'CONDITIONAL_OFFER':
                return new OpportunityOfferHandler();
            case 'OPPORTUNITY_REVOKED_CLOSED_LOST':
                return new OpportunityRevokedClosedLostHandler();
            case 'OPPORTUNITY_CLOSED_LOST':
                return new OpportunityClosedLostHandler();
            case 'OPPORTUNITY_CLOSED_WON':
                return new OpportunityClosedWonHandler();
            case 'Payment':
                return new OpportunityPaymentCompletedHandler();
            case 'DOCUMENT_CHECKLIST_CREATED':
                return new OpportunityDocumentUploadHandler();
            case 'REJECTED_DOCUMENT_TASK':
                return new RejectedDocumentTaskHandler();
            case 'I_20':
                return new DocumentUploadHandler();
            case 'ACCEPTED_DOCUMENT_UPDATE':
                return new AcceptedDocumentUpdateHandler();
            default:
                console.log('No handler found for event type: ' + scenario);
                break;
        }
    }
}
