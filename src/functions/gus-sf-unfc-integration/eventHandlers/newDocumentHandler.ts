import { LoggerService } from "src/common/cloudwatchService";
import { getData } from "src/connectors/eip-connector";
import { UNFCEllucianConnector } from "src/connectors/unfc-ellucian-connector";
import { dynamicMapper } from "../unfcMappings";
import { sftpFileInboundMappingMetadata } from "../mappingMetadata";
import { LoggerEnum } from "@gus-eip/loggers";
import { validateMapping, ValidationResult } from "../mappingValidation";
import { MappingMetadata } from "../unfcMappings";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const unfcEllucianConnector = new UNFCEllucianConnector();

/**
 * Interface for object type configuration
 */
interface ObjectTypeConfig {
  mappingMetadata: MappingMetadata;
  validationType: string;
  endpoint: string;
  arrayField?: string;
  condition?: (data: any) => boolean;
  isCritical?: boolean;
}

/**
 * Configuration for all object types
 */
const objectTypeConfigs: Record<string, ObjectTypeConfig> = {
  sftpFileInbound: {
    mappingMetadata: sftpFileInboundMappingMetadata,
    validationType: "sftpFileInbound",
    endpoint: "eip/unfc/sftp",
    arrayField: "OpportunityFile__c",
    condition: (data) => data?.OpportunityFile__c?.length,
  },
};

// Interface for tracking processing results
interface ProcessingResult {
  success: boolean;
  objectType: string;
  id?: string;
  error?: string;
  reason?: string; // Reason for skipping
}

// Interface for tracking error information
interface ErrorInfo {
  objectType: string;
  errors: string[];
  missingFields?: string[];
  sourceData?: any;
}

export class ApplicationDocumentHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private unfcFormattedPickListDetails: any;

  // Arrays to track processing results
  private errors: ErrorInfo[] = [];
  private successfulObjects: ProcessingResult[] = [];
  private skippedObjects: ProcessingResult[] = [];

  async fetchGusSFDetails(
    opportunityId: any,
    correlationId?: string
  ): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `Fetch opportunity files for ${opportunityId}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_FILES_INTIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      return await getData(
        `gus/getopportunityfiles/${opportunityId}`,
        correlationId,
        process.env.UCW_KEY
      );
    } catch (error) {
      await this.error(
        opportunityId,
        error,
        loggerEnum.Event.FETCH_OPPORTUNITY_FILES_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(
        `Error fetching documents from gus by opportunityId: ${error}`
      );
    }
  }

  async syncInSFTP(path: string, data: any, objectType: string): Promise<any> {
    try {
      await this.log(
        data,
        `Sync in SFTP initiated for ${objectType}`,
        loggerEnum.Event.SYNC_IN_UNFC_INITIATED,
        loggerEnum.Component.UNFC_ELLUCIAN
      );

      // Post data to UNFC
      const response = await unfcEllucianConnector.createFileInSFTP(
        `${path}`,
        data
      );

      // Track successful object creation
      this.successfulObjects.push({
        success: true,
        objectType,
        id: response.message,
      });

      return response;
    } catch (error) {
      await this.error(
        data,
        error,
        loggerEnum.Event.SYNC_IN_UNFC_FAILED,
        loggerEnum.Component.UNFC_ELLUCIAN,
        data
      );

      // Track failed object creation
      this.errors.push({
        objectType,
        errors: [error.message || JSON.stringify(error)],
      });

      // Return null instead of throwing to allow processing to continue
      return null;
    }
  }

  async processArrayObjects(
    gusSfObjectDetails: any,
    objectType: string
  ): Promise<any[]> {
    const results = [];
    const config = objectTypeConfigs[objectType];

    if (!config || !config.arrayField) {
      console.error(
        `No array field configuration found for object type: ${objectType}`
      );

      // Track skipped object
      this.skippedObjects.push({
        success: false,
        objectType,
        reason: `No array field configuration found for object type: ${objectType}`,
      });

      return results;
    }

    const arrayFieldName = config.arrayField;

    // Check if the array field exists and is an array
    if (
      !gusSfObjectDetails[arrayFieldName] ||
      !Array.isArray(gusSfObjectDetails[arrayFieldName])
    ) {
      console.log(
        `${arrayFieldName} is not an array or doesn't exist in gusSfObjectDetails`
      );

      // Track skipped object
      this.skippedObjects.push({
        success: false,
        objectType,
        reason: `${arrayFieldName} is not an array or doesn't exist`,
      });

      return results;
    }

    // Process each item in the array
    for (const [index, item] of gusSfObjectDetails[arrayFieldName].entries()) {
      try {
        // Create a copy of gusSfObjectDetails with the current array item
        const itemData = {
          ...gusSfObjectDetails,
          [arrayFieldName]: { ...item, orderNumber: index + 1 },
        };

        // Map the data using the provided mapping metadata
        const mappedData = await dynamicMapper.mapData(
          itemData,
          config.mappingMetadata,
          this.unfcFormattedPickListDetails
        );

        // Validate the mapped data
        const validationResult = validateMapping(
          mappedData,
          config.validationType
        );

        // If validation fails, log the error and continue with the next item
        if (!validationResult?.isValid) {
          this.handleValidationError(
            validationResult,
            objectType,
            `Validation failed for ${objectType} mapping`
          );

          // Track skipped object due to validation failure
          this.skippedObjects.push({
            success: false,
            objectType,
            reason: `Validation failed for array item in ${objectType}`,
          });

          continue;
        }

        // Sync the data with UNFC
        const response = await this.syncInSFTP(
          config.endpoint,
          mappedData,
          objectType
        );

        if (response) {
          results.push(response);
        }
      } catch (error) {
        console.error(`Error processing ${arrayFieldName} item:`, error);
        this.errors.push({
          objectType,
          errors: [error.message || JSON.stringify(error)],
        });
      }
    }

    return results;
  }

  async processObjectByType(
    gusSfObjectDetails: any,
    objectType: string
  ): Promise<any | any[] | null> {
    const config = objectTypeConfigs[objectType];

    if (!config) {
      console.error(`No configuration found for object type: ${objectType}`);

      // Track skipped object
      this.skippedObjects.push({
        success: false,
        objectType,
        reason: `No configuration found for object type: ${objectType}`,
      });

      return null;
    }

    // Check if condition is defined and evaluates to false
    if (config.condition && !config.condition(gusSfObjectDetails)) {
      console.log(`Condition not met for ${objectType}, skipping processing`);

      // Track skipped object
      this.skippedObjects.push({
        success: false,
        objectType,
        reason: `Condition not met for ${objectType}`,
      });

      return null;
    }

    // If it's an array type and the array field exists
    if (
      config.arrayField &&
      gusSfObjectDetails[config.arrayField] &&
      Array.isArray(gusSfObjectDetails[config.arrayField])
    ) {
      return await this.processArrayObjects(gusSfObjectDetails, objectType);
    }
  }

  private handleValidationError(
    validationResult: ValidationResult,
    objectType: string,
    errorMessage: string
  ): void {
    // Log the validation error
    console.error(
      `${errorMessage}: ${JSON.stringify(validationResult.errors)}`
    );

    // Add to the errors array
    this.errors.push({
      objectType,
      errors: validationResult.errors,
      missingFields: validationResult.missingFields,
    });
  }

  private generateProcessingSummary(): any {
    return {
      successful: {
        count: this.successfulObjects.length,
        objects: this.successfulObjects,
      },
      failed: {
        count: this.errors.length,
        errors: this.errors,
      },
      skipped: {
        count: this.skippedObjects.length,
        objects: this.skippedObjects,
      },
    };
  }

  async handleMessage(event: any): Promise<any> {
    // Reset tracking arrays for each new message
    this.errors = [];
    this.successfulObjects = [];
    this.skippedObjects = [];

    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);

    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage.payload.Scenario__c;
    this.applicationFormId =
      platformEventMessage.payload.Application_Form_Id__c;
    this.brand = platformEventMessage.payload.BusinessUnitFilter__c || "UNFC";

    const opportunityId = platformEventMessage.payload.Opportunity_Id__c;
    const colleaguePersonId = platformEventMessage.payload.Applic_Id__c;
    const admissionApplicationId =
      platformEventMessage.payload.Student_External_ID__c;

    const applicationId = platformEventMessage.payload.ApplicationId__c;

    await this.log(
      platformEventMessage.payload,
      `UNFC Document syncing initiated ${opportunityId}`,
      loggerEnum.Event.DOCUMENT_UPLOAD_INITIATED,
      loggerEnum.Component.UNFC_ELLUCIAN,
      {}
    );

    try {
      if (!opportunityId) {
        await this.error(
          platformEventMessage.payload,
          "Opportunity ID not found",
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND
        );
        this.errors.push({
          objectType: "general",
          errors: ["Opportunity ID not found"],
        });
        throw new Error("Opportunity ID not found");
      }

      // Fetch opportunity details from GUS SF
      let gusSfObjectDetails = await this.fetchGusSFDetails(opportunityId);

      gusSfObjectDetails = {
        OpportunityFile__c: gusSfObjectDetails.response || [],
        colleaguePersonId,
        admissionApplicationId,
        applicationId,
        applicationFormId: this.applicationFormId,
      };

      // Process all other object types in sequence
      // Define the processing order
      const objectTypes = ["sftpFileInbound"];

      // Process each object type
      for (const objectType of objectTypes) {
        await this.processObjectByType(gusSfObjectDetails, objectType);
      }

      if (this.errors.length > 0) {
        throw new Error("UNFC Document processing failed");
      }

      await this.log(
        platformEventMessage.payload,
        `UNFC Application file processing completed for ${opportunityId}`,
        loggerEnum.Event.DOCUMENT_UPLOAD_COMPLETED,
        loggerEnum.Component.UNFC_ELLUCIAN,
        {},
        this.generateProcessingSummary()
      );

      // Log successful creation summary
      await this.log(
        platformEventMessage.payload,
        `UNFC Application file processing completed for ${opportunityId}`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.UNFC_ELLUCIAN,
        {},
        this.generateProcessingSummary()
      );

      // Return processing summary
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Processing completed",
          summary: this.generateProcessingSummary(),
        }),
      };
    } catch (error) {
      // Log the error
      await this.error(
        platformEventMessage.payload,
        error.message ? error.message : error,
        loggerEnum.Event.DOCUMENT_UPLOAD_FAILED,
        loggerEnum.Component.UNFC_ELLUCIAN,
        {},
        this.generateProcessingSummary()
      );

      // Add to errors array if not already added
      if (
        !this.errors.some(
          (e) =>
            e.errors.includes(error.message || JSON.stringify(error)) &&
            error.message !== "UNFC Document processing failed"
        )
      ) {
        this.errors.push({
          objectType: "general",
          errors: [error.message || JSON.stringify(error)],
        });
      }

      // Return error summary instead of throwing
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Error processing application",
          error: error.message ? error.message : error,
          summary: this.generateProcessingSummary(),
        }),
      };
    }
  }

  async log(
    sourcePayload: any,
    logMessage: string,
    event: string,
    destination?: string,
    destinationPayload?: any,
    response?: any,
    destinationObject?: string,
    destinationObjectId?: string
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_HANDLER, // Using existing component
      loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_QUEUE, // Using existing component
      destination || loggerEnum.Component.UNFC_ELLUCIAN,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }

  async error(
    sourcePayload: any,
    errorMessage: any,
    event: string,
    destination?: string,
    destinationPayload?: any,
    response?: any,
    destinationObject?: string,
    destinationObjectId?: string
  ): Promise<any> {
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER, // Using existing component
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE, // Using existing component
      destination || loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
