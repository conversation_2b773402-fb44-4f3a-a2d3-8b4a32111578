import { LoggerService } from "src/common/cloudwatchService";
import { getData, postData } from "src/connectors/eip-connector";
import { UNFCEllucianConnector } from "src/connectors/unfc-ellucian-connector";
import { dynamicMapper } from "../unfcMappings";
import {
  academicProgramsMappingMetadata,
  admissionApplicationMappingMetadata,
  applCoopRemarksMappingMetadata,
  aptitudeAssessmentMappingMetadata,
  externalEducationMappingMetadata,
  personMappingMetadata,
  applEducationHistoryRemarksMappingMetadata,
  applicationRemarksMappingMetadata,
  disabilityMetaData,
  emergencyContactMappingMetadata,
  visaApplicationMappingMetadata,
} from "../mappingMetadata";
import { LoggerEnum } from "@gus-eip/loggers";
import { getAllPicklistValues } from "src/common/getPickListValue";
import { validateMapping, ValidationResult } from "../mappingValidation";
import { MappingMetadata } from "../unfcMappings";
import { SnsService } from "src/common/snsService";
import { v4 as uuidv4 } from "uuid";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const unfcEllucianConnector = new UNFCEllucianConnector();
const snsService = new SnsService();

interface ObjectTypeConfig {
  mappingMetadata: MappingMetadata;
  validationType: string;
  endpoint: string;
  arrayField?: string;
  condition?: (data: any) => boolean;
  isCritical?: boolean;
}

const objectTypeConfigs: Record<string, ObjectTypeConfig> = {
  person: {
    mappingMetadata: personMappingMetadata,
    validationType: "person",
    endpoint: "persons",
    isCritical: true,
  },
  admissionApplication: {
    mappingMetadata: admissionApplicationMappingMetadata,
    validationType: "admissionApplication",
    endpoint: "admission-applications",
    isCritical: true,
  },
  academicPrograms: {
    mappingMetadata: academicProgramsMappingMetadata,
    validationType: "academicPrograms",
    endpoint: "student-academic-programs",
  },
  aptitudeAssessment: {
    mappingMetadata: aptitudeAssessmentMappingMetadata,
    validationType: "aptitudeAssessment",
    endpoint: "student-aptitude-assessments",
    arrayField: "LanguageProficiencyRecord__c",
    condition: (data) => !!data.LanguageProficiencyRecord__c,
  },
  externalEducation: {
    mappingMetadata: externalEducationMappingMetadata,
    validationType: "externalEducation",
    endpoint: "person-external-education",
    arrayField: "EducationHistoryRecord__c",
    condition: (data) => !!data.EducationHistoryRecord__c,
  },
  applCoopRemarks: {
    mappingMetadata: applCoopRemarksMappingMetadata,
    validationType: "applCoopRemarks",
    endpoint: "unf-appl-remarks",
    condition: (data) => data.Opportunity?.Coop_WorkPermitRequired__c === "Yes",
  },
  applEducationHistoryRemarks: {
    mappingMetadata: applEducationHistoryRemarksMappingMetadata,
    validationType: "applEducationHistoryRemarks",
    endpoint: "unf-appl-remarks",
    arrayField: "EducationHistoryRecord__c",
    condition: (data) => !!data.EducationHistoryRecord__c,
  },
  applicationRemarks: {
    mappingMetadata: applicationRemarksMappingMetadata,
    validationType: "applicationRemarks",
    endpoint: "unf-appl-remarks",
  },
  disability: {
    mappingMetadata: disabilityMetaData,
    validationType: "disability",
    endpoint: "x-person-health",
    condition: (data) =>
      !!data.ColleaguePersonId && data.Opportunity?.Has_Disability__c === "Yes",
  },
  emergencyContact: {
    mappingMetadata: emergencyContactMappingMetadata,
    validationType: "emergencyContact",
    endpoint: "person-emergency-contacts",
    condition: (data) => !!data.Connection__c,
  },
  visaApplication: {
    mappingMetadata: visaApplicationMappingMetadata,
    validationType: "visaApplication",
    endpoint: "person-visas",
    condition: (data) => !!data.Visa_Application__c.Visa_Required__c,
  },
};

interface ProcessingResult {
  success: boolean;
  objectType: string;
  id?: string;
  error?: string;
  reason?: string; // Reason for skipping
}

// Interface for tracking error information
interface ErrorInfo {
  objectType: string;
  errors: string[];
  missingFields?: string[];
  sourceData?: any;
}

export class ApplicationHandler {
  private correlationId: string;
  private applicationFormId: string;
  private brand: string;
  private usecase: string;
  private unfcFormattedPickListDetails: any;

  // Arrays to track processing results
  private errors: ErrorInfo[] = [];
  private successfulObjects: ProcessingResult[] = [];
  private skippedObjects: ProcessingResult[] = [];

  async fetchGusSFDetails(
    opportunityId: any,
    _correlationId?: string // Prefix with underscore to indicate it's not used
  ): Promise<any> {
    try {
      await this.log(
        opportunityId,
        `Fetch GUS SF details initiated for opportunity ID: ${opportunityId}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_INITIATED,
        loggerEnum.Component.GUS_SALESFORCE
      );

      const response = await getData(
        `gus/getapplicationsdetails/${opportunityId}?scenario=${this.usecase}`,
        this.correlationId,
        process.env.UNFC_KEY
      );

      await this.log(
        opportunityId,
        `Fetch GUS SF details completed for opportunity ID: ${opportunityId}`,
        loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        response
      );

      return response;
    } catch (error) {
      await this.error(
        opportunityId,
        error,
        loggerEnum.Event.FETCH_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error fetching GUS SF details: ${error}`);
    }
  }

  async syncInUNFC(path: string, data: any, objectType: string): Promise<any> {
    try {
      await this.log(
        data,
        `Sync in UNFC initiated for ${objectType}`,
        loggerEnum.Event.SYNC_IN_UNFC_INITIATED,
        loggerEnum.Component.UNFC_ELLUCIAN
      );

      // Post data to UNFC
      const response = await unfcEllucianConnector.post(`${path}`, data);

      // Track successful object creation
      this.successfulObjects.push({
        success: true,
        objectType,
        id: response.id,
      });

      return response;
    } catch (error) {
      await this.error(
        data,
        error,
        loggerEnum.Event.SYNC_IN_UNFC_FAILED,
        loggerEnum.Component.UNFC_ELLUCIAN,
        data
      );

      this.errors.push({
        objectType,
        errors: [error.message || JSON.stringify(error)],
      });

      // Return null instead of throwing to allow processing to continue
      return null;
    }
  }

  /**
   * Process a single object based on its configuration
   * @param gusSfObjectDetails Source data containing the object
   * @param objectType Type of object being processed
   * @returns Processing result
   */
  async processObject(
    gusSfObjectDetails: any,
    objectType: string
  ): Promise<any | null> {
    try {
      const config = objectTypeConfigs[objectType];
      if (!config) {
        console.error(`No configuration found for object type: ${objectType}`);

        // Track skipped object
        this.skippedObjects.push({
          success: false,
          objectType,
          reason: `No configuration found for object type: ${objectType}`,
        });

        return null;
      }

      // Check if condition is defined and evaluates to false
      if (config.condition && !config.condition(gusSfObjectDetails)) {
        console.log(`Condition not met for ${objectType}, skipping processing`);

        // Track skipped object
        this.skippedObjects.push({
          success: false,
          objectType,
          reason: `Condition not met for ${objectType}`,
        });

        return null;
      }

      // Map the data using the provided mapping metadata
      const mappedData = await dynamicMapper.mapData(
        gusSfObjectDetails,
        config.mappingMetadata,
        this.unfcFormattedPickListDetails
      );

      // Validate the mapped data
      const validationResult = validateMapping(
        mappedData,
        config.validationType
      );

      // If validation fails, log the error and return null
      if (!validationResult?.isValid) {
        this.handleValidationError(
          validationResult,
          objectType,
          `Validation failed for ${objectType} mapping`
        );

        // If this is a critical object, throw an error
        if (config.isCritical) {
          throw new Error(`Critical object ${objectType} validation failed`);
        }

        return null;
      }

      // Special case for disability - check if personHealthId exists
      if (objectType === "disability" && !mappedData?.personHealthId) {
        // Track skipped object
        this.skippedObjects.push({
          success: false,
          objectType,
          reason: `Missing personHealthId for disability object`,
        });
        return null;
      }

      // Sync the data with UNFC
      return await this.syncInUNFC(config.endpoint, mappedData, objectType);
    } catch (error) {
      console.error(`Error processing ${objectType}:`, error);
      this.errors.push({
        objectType,
        errors: [error.message || JSON.stringify(error)],
      });

      // Re-throw if it's a critical object
      if (objectTypeConfigs[objectType]?.isCritical) {
        throw error;
      }

      return null;
    }
  }

  /**
   * Process array type objects from gusSfObjects
   * @param gusSfObjectDetails Source data containing array objects
   * @param objectType Type of object being processed
   * @returns Array of processing results
   */
  async processArrayObjects(
    gusSfObjectDetails: any,
    objectType: string
  ): Promise<any[]> {
    const results = [];
    const config = objectTypeConfigs[objectType];

    if (!config || !config.arrayField) {
      console.error(
        `No array field configuration found for object type: ${objectType}`
      );

      // Track skipped object
      this.skippedObjects.push({
        success: false,
        objectType,
        reason: `No array field configuration found for object type: ${objectType}`,
      });

      return results;
    }

    const arrayFieldName = config.arrayField;

    // Check if the array field exists and is an array
    if (
      !gusSfObjectDetails[arrayFieldName] ||
      !Array.isArray(gusSfObjectDetails[arrayFieldName])
    ) {
      console.log(
        `${arrayFieldName} is not an array or doesn't exist in gusSfObjectDetails`
      );

      // Track skipped object
      this.skippedObjects.push({
        success: false,
        objectType,
        reason: `${arrayFieldName} is not an array or doesn't exist`,
      });

      return results;
    }

    // Process each item in the array
    for (const [index, item] of gusSfObjectDetails[arrayFieldName].entries()) {
      try {
        // Create a copy of gusSfObjectDetails with the current array item
        const itemData = {
          ...gusSfObjectDetails,
          [arrayFieldName]: { ...item, orderNumber: index + 1 },
        };

        // Map the data using the provided mapping metadata
        const mappedData = await dynamicMapper.mapData(
          itemData,
          config.mappingMetadata,
          this.unfcFormattedPickListDetails
        );

        // Validate the mapped data
        const validationResult = validateMapping(
          mappedData,
          config.validationType
        );

        // If validation fails, log the error and continue with the next item
        if (!validationResult?.isValid) {
          this.handleValidationError(
            validationResult,
            objectType,
            `Validation failed for ${objectType} mapping`
          );

          // Track skipped object due to validation failure
          this.skippedObjects.push({
            success: false,
            objectType,
            reason: `Validation failed for array item in ${objectType}`,
          });

          continue;
        }

        // Sync the data with UNFC
        const response = await this.syncInUNFC(
          config.endpoint,
          mappedData,
          objectType
        );

        if (response) {
          results.push(response);
        }
      } catch (error) {
        console.error(`Error processing ${arrayFieldName} item:`, error);
        this.errors.push({
          objectType,
          errors: [error.message || JSON.stringify(error)],
        });
      }
    }

    return results;
  }

  /**
   * Process an object based on its configuration, handling both single and array objects
   * @param gusSfObjectDetails Source data containing the object
   * @param objectType Type of object being processed
   * @returns Processing result or array of results
   */
  async processObjectByType(
    gusSfObjectDetails: any,
    objectType: string
  ): Promise<any | any[] | null> {
    const config = objectTypeConfigs[objectType];

    if (!config) {
      console.error(`No configuration found for object type: ${objectType}`);

      // Track skipped object
      this.skippedObjects.push({
        success: false,
        objectType,
        reason: `No configuration found for object type: ${objectType}`,
      });

      return null;
    }

    // Check if condition is defined and evaluates to false
    if (config.condition && !config.condition(gusSfObjectDetails)) {
      console.log(`Condition not met for ${objectType}, skipping processing`);

      // Track skipped object
      this.skippedObjects.push({
        success: false,
        objectType,
        reason: `Condition not met for ${objectType}`,
      });

      return null;
    }

    // If it's an array type and the array field exists
    if (
      config.arrayField &&
      gusSfObjectDetails[config.arrayField] &&
      Array.isArray(gusSfObjectDetails[config.arrayField])
    ) {
      return await this.processArrayObjects(gusSfObjectDetails, objectType);
    }
    // Otherwise process as a single object
    else {
      return await this.processObject(gusSfObjectDetails, objectType);
    }
  }

  /**
   * Handle validation errors by logging them and adding to the errors array
   * @param validationResult Validation result from validateMapping
   * @param objectType Type of object being validated
   * @param errorMessage Error message to log
   */
  private handleValidationError(
    validationResult: ValidationResult,
    objectType: string,
    errorMessage: string
  ): void {
    // Log the validation error
    console.error(
      `${errorMessage}: ${JSON.stringify(validationResult.errors)}`
    );

    // Add to the errors array
    this.errors.push({
      objectType,
      errors: validationResult.errors,
      missingFields: validationResult.missingFields,
    });
  }

  /**
   * Generate a summary of processing results
   * @returns Object containing success and error summaries
   */
  private generateProcessingSummary(): any {
    return {
      successful: {
        count: this.successfulObjects.length,
        objects: this.successfulObjects,
      },
      failed: {
        count: this.errors.length,
        errors: this.errors,
      },
      skipped: {
        count: this.skippedObjects.length,
        objects: this.skippedObjects,
      },
    };
  }

  async handleMessage(event: any): Promise<any> {
    // Reset tracking arrays for each new message
    this.errors = [];
    this.successfulObjects = [];
    this.skippedObjects = [];

    const eventBody = JSON.parse(event.body);
    const platformEventMessage = JSON.parse(eventBody.Message);

    this.correlationId = platformEventMessage.event?.EventUuid;
    this.usecase = platformEventMessage.payload.Scenario__c;
    this.applicationFormId =
      platformEventMessage.payload.Application_Form_Id__c;
    this.brand = platformEventMessage.payload.BusinessUnitFilter__c || "UNFC";

    const opportunityId = platformEventMessage.payload.Opportunity_Id__c;

    await this.log(
      platformEventMessage.payload,
      `UNFC Application create initiated ${opportunityId}`,
      loggerEnum.Event.NEW_APPLICATION,
      loggerEnum.Component.UNFC_ELLUCIAN,
      {}
    );

    try {
      if (!opportunityId) {
        await this.error(
          platformEventMessage.payload,
          "Opportunity ID not found",
          loggerEnum.Event.OPPORTUNITY_NOT_FOUND
        );
        this.errors.push({
          objectType: "general",
          errors: ["Opportunity ID not found"],
        });
        throw new Error("Opportunity ID not found");
      }

      // Fetch opportunity details from GUS SF
      let gusSfObjectDetails = await this.fetchGusSFDetails(opportunityId);

      if (gusSfObjectDetails.Opportunity.Application_Misc_Details__c != null) {
        const miscDetails = JSON.parse(
          gusSfObjectDetails.Opportunity.Application_Misc_Details__c
        );

        gusSfObjectDetails.Opportunity = {
          ...gusSfObjectDetails.Opportunity,
          ...miscDetails,
        };
      }

      const unfcPicklistDetails = await getAllPicklistValues("GUS_SF_UNFC");

      this.unfcFormattedPickListDetails = unfcPicklistDetails.reduce(
        (acc: any, curr: any) => {
          acc[curr.SK] = curr.Picklist;
          return acc;
        },
        {}
      );

      // Process person object (critical)
      const personResponse = await this.processPerson(gusSfObjectDetails);
      if (!personResponse) {
        return {
          statusCode: 500,
          body: JSON.stringify({
            message: "Error processing application",
            error: "Failed to create person in UNFC",
            summary: this.generateProcessingSummary(),
          }),
        };
      }

      // Update gusSfObjectDetails with person IDs for subsequent processing
      gusSfObjectDetails.PersonId = personResponse.id;
      gusSfObjectDetails.ColleaguePersonId = personResponse.credentials?.filter(
        (cred: any) => cred.type === "colleaguePersonId"
      )[0]?.value;

      // Process admission application (critical)
      const applicationResponse = await this.processObject(
        gusSfObjectDetails,
        "admissionApplication"
      );

      if (!applicationResponse) {
        return {
          statusCode: 500,
          body: JSON.stringify({
            message: "Error processing application",
            error: "Failed to create application in UNFC ",
            summary: this.generateProcessingSummary(),
          }),
        };
      }

      // Update gusSfObjectDetails with application IDs for subsequent processing
      gusSfObjectDetails.AdmissionApplicationId = applicationResponse.id;
      gusSfObjectDetails.ApplicationsId = applicationResponse.applicationsId;

      // Process all other object types in sequence
      // Define the processing order
      const objectTypes = [
        "academicPrograms",
        "aptitudeAssessment",
        "externalEducation",
        "applCoopRemarks",
        "applEducationHistoryRemarks",
        "applicationRemarks",
        "disability",
        "emergencyContact",
        "visaApplication",
      ];

      // Process each object type
      for (const objectType of objectTypes) {
        await this.processObjectByType(gusSfObjectDetails, objectType);
      }

      await this.updateGusOpportunity(gusSfObjectDetails, opportunityId);

      if (!gusSfObjectDetails.Account?.Student_External_ID__c) {
        await this.updateGusAccount(gusSfObjectDetails);
      }

      // Prepare SNS message for document processing
      try {
        const documentEventMessage = {
          event: {
            EventUuid: uuidv4(),
          },
          payload: {
            Scenario__c: "GUS_NEW_DOCUMENT",
            Application_Form_Id__c: this.applicationFormId,
            BusinessUnitFilter__c: this.brand,
            Opportunity_Id__c: gusSfObjectDetails.Opportunity?.Id,
            Applic_Id__c: gusSfObjectDetails.ColleaguePersonId,
            Student_External_ID__c: gusSfObjectDetails.AdmissionApplicationId,
            ApplicationId__c: gusSfObjectDetails.Opportunity?.ApplicationId__c,
          },
        };

        // Log the SNS message preparation
        await this.log(
          documentEventMessage,
          `Preparing to publish document event to SNS for ${gusSfObjectDetails.Opportunity.Id}`,
          loggerEnum.Event.PUBLISH_SNS_MSG,
          loggerEnum.Component.GUS_SALESFORCE_OUTBOUND_SNS_TOPIC
        );

        // Publish to SNS topic
        const publishResponse = await snsService.publishMessages(
          documentEventMessage,
          this.applicationFormId,
          process.env.GUS_SF_OUTBOUND_TOPIC_ARN,
          this.brand
        );

        await this.log(
          publishResponse,
          `Document event published to SNS for ${gusSfObjectDetails.Opportunity.Id}`,
          loggerEnum.Event.PUBLISH_TO_SNS_COMPLETED,
          loggerEnum.Component.GUS_SALESFORCE_OUTBOUND_SNS_TOPIC
        );
      } catch (error) {
        await this.error(
          { opportunityId: gusSfObjectDetails.Opportunity.Id },
          error,
          loggerEnum.Event.PUBLISH_SNS_MSG,
          loggerEnum.Component.GUS_SALESFORCE_OUTBOUND_SNS_TOPIC
        );

        console.error("Error publishing document event to SNS:", error);
      }

      await this.log(
        platformEventMessage.payload,
        `UNFC Application processing completed for ${opportunityId}`,
        loggerEnum.Event.APPLICATION_PROCESSING_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        this.generateProcessingSummary()
      );

      if (this.errors.length > 0) {
        throw new Error("UNFC Application processing failed for some objects");
      }

      // Log successful creation summary
      await this.log(
        platformEventMessage.payload,
        `UNFC Application processing completed for ${opportunityId}`,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        this.generateProcessingSummary()
      );

      // Return processing summary
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Processing completed",
          summary: this.generateProcessingSummary(),
        }),
      };
    } catch (error) {
      // Log the error
      await this.error(
        platformEventMessage.payload,
        error.message ? error.message : error,
        loggerEnum.Event.CREATE_APPLICATION_FAILED,
        loggerEnum.Component.GUS_SALESFORCE,
        {},
        this.generateProcessingSummary()
      );

      // Add to errors array if not already added
      if (
        !this.errors.some(
          (e) =>
            e.errors.includes(error.message || JSON.stringify(error)) &&
            error.message !==
              "UNFC Application processing failed for some objects"
        )
      ) {
        this.errors.push({
          objectType: "general",
          errors: [error.message || JSON.stringify(error)],
        });
      }

      // Return error summary instead of throwing
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Error processing application",
          error: error.message ? error.message : error,
          summary: this.generateProcessingSummary(),
        }),
      };
    }
  }

  /**
   * Special handling for person object which requires additional checks
   * @param gusSfObjectDetails Source data containing person information
   * @returns Person response from UNFC
   */
  async processPerson(gusSfObjectDetails: any): Promise<any> {
    try {
      // Map the data using the provided mapping metadata
      const unfcPersonDetails = await dynamicMapper.mapData(
        gusSfObjectDetails,
        personMappingMetadata,
        this.unfcFormattedPickListDetails
      );

      // Validate the mapped data
      const personValidationResult = validateMapping(
        unfcPersonDetails,
        "person"
      );

      // Check if validation failed
      if (!personValidationResult.isValid) {
        this.handleValidationError(
          personValidationResult,
          "person",
          "Validation failed for person mapping"
        );
        throw new Error("Person validation failed");
      }

      // Check if person already exists in UNFC
      let checkPersonAlreadyExist = [];
      if (gusSfObjectDetails.Account?.Student_External_ID__c) {
        try {
          checkPersonAlreadyExist = await unfcEllucianConnector.get(
            `persons?criteria={"credentials":[{"type":"colleaguePersonId","value":"${gusSfObjectDetails.Account?.Student_External_ID__c}"}]}`
          );
        } catch (error) {
          console.log("Error fetching person details", error);
        }
      }

      // Add emergency email if available
      const { Email_c__c: Emergency_Email__c } =
        gusSfObjectDetails.Connection__c || {};
      if (Emergency_Email__c) {
        const mapEmergencyId =
          this.unfcFormattedPickListDetails["personEmergencyContactEmail"];

        // Initialize emails array if it doesn't exist
        if (!unfcPersonDetails.emails) {
          unfcPersonDetails.emails = [];
        }

        // Check if email already exists
        const existingEmails = checkPersonAlreadyExist.length
          ? checkPersonAlreadyExist[0]?.emails
              ?.filter((email: any) => email?.type?.emailType === "other")
              .map((email: any) => email.address)
          : [];

        if (!existingEmails.includes(Emergency_Email__c)) {
          unfcPersonDetails.emails.push({
            address: Emergency_Email__c,
            type: {
              emailType: "other",
              detail: {
                id: mapEmergencyId[existingEmails.length + 1],
              },
            },
          });
        }
      }

      // Set the person ID based on whether they already exist
      if (checkPersonAlreadyExist.length) {
        console.log("Existing person found", checkPersonAlreadyExist);

        unfcPersonDetails.id = checkPersonAlreadyExist[0].id;
        await this.log(
          unfcPersonDetails,
          "Updating existing person record in UNFC",
          loggerEnum.Event.UPDATE_PERSON_RECORD,
          loggerEnum.Component.UNFC_ELLUCIAN
        );
        return await unfcEllucianConnector.patch(
          "persons",
          unfcPersonDetails,
          unfcPersonDetails.id
        );
      } else {
        const isDuplicatePersonRecord =
          await unfcEllucianConnector.checkDuplicatePerson(unfcPersonDetails);

        console.log("isDuplicatePersonRecord", isDuplicatePersonRecord);

        if (isDuplicatePersonRecord.length) {
          await this.log(
            unfcPersonDetails,
            "Duplicate application found -  Updating existing person record in UNFC",
            loggerEnum.Event.UPDATE_EXISTING_PERSON_RECORD,
            loggerEnum.Component.UNFC_ELLUCIAN
          );
          unfcPersonDetails.id = isDuplicatePersonRecord[0].id;
          return await unfcEllucianConnector.patch(
            "persons",
            unfcPersonDetails,
            unfcPersonDetails.id
          );
        }

        return await this.syncInUNFC("persons", unfcPersonDetails, "person");
      }
    } catch (error) {
      console.error("Error processing person:", error);
      this.errors.push({
        objectType: "person",
        errors: [error.message || JSON.stringify(error)],
      });
      throw error; // Re-throw as person is critical
    }
  }

  private async updateGusOpportunity(
    gusSfObjectDetails,
    opportunityId
  ): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateOpportunity/${opportunityId}?scenario=${this.usecase}`,
        { ApplicId__c: gusSfObjectDetails.AdmissionApplicationId },
        this.correlationId,
        process.env.UCW_KEY
      );
      await this.log(
        { ApplicId__c: gusSfObjectDetails.AdmissionApplicationId },
        "Opportunity update completed",
        loggerEnum.Event.UPDATE_OPPORTUNITY_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        "Opportunity",
        saveResponse
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        { ApplicId__c: gusSfObjectDetails.AdmissionApplicationId },
        error,
        loggerEnum.Event.UPDATE_OPPORTUNITY_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error updating opportunity: ${error}`);
    }
  }

  private async updateGusAccount(gusSfObjectDetails): Promise<any> {
    try {
      const saveResponse = await postData(
        `gus/updateAccount/${gusSfObjectDetails?.Account?.Id}`,
        { Student_External_ID__c: gusSfObjectDetails.ColleaguePersonId },
        this.correlationId,
        process.env.UCW_KEY,
        "PATCH"
      );
      await this.log(
        { Student_External_ID__c: gusSfObjectDetails.ColleaguePersonId },
        "Account update completed",
        loggerEnum.Event.UPDATE_ACCOUNT_COMPLETED,
        loggerEnum.Component.GUS_SALESFORCE,
        "Account",
        saveResponse
      );
      return saveResponse;
    } catch (error) {
      await this.error(
        { Student_External_ID__c: gusSfObjectDetails.ColleaguePersonId },
        error,
        loggerEnum.Event.UPDATE_ACCOUNT_FAILED,
        loggerEnum.Component.GUS_SALESFORCE
      );
      throw new Error(`Error updating account: ${error}`);
    }
  }

  async log(
    sourcePayload: any,
    logMessage: string,
    event: string,
    destination?: string,
    destinationPayload?: any,
    response?: any,
    destinationObject?: string,
    destinationObjectId?: string
  ): Promise<any> {
    await loggerService.log(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_HANDLER, // Using existing component
      loggerEnum.Component.GUS_SALESFORCE_MYUCW_INTEGRATION_QUEUE, // Using existing component
      destination || loggerEnum.Component.UNFC_ELLUCIAN_CONNECTOR,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      logMessage,
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }

  async error(
    sourcePayload: any,
    errorMessage: any,
    event: string,
    destination?: string,
    destinationPayload?: any,
    response?: any,
    destinationObject?: string,
    destinationObjectId?: string
  ): Promise<any> {
    await loggerService.error(
      this.correlationId,
      new Date().toISOString(),
      loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_HANDLER, // Using existing component
      loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_QUEUE, // Using existing component
      destination || loggerEnum.Component.UNFC_ELLUCIAN,
      event,
      this.usecase,
      sourcePayload,
      destinationPayload || {},
      errorMessage.message
        ? errorMessage.message
        : JSON.stringify(errorMessage),
      this.brand,
      this.applicationFormId,
      "Application_Form_Id__c",
      this.applicationFormId,
      destinationObject,
      destinationObjectId,
      "",
      this.applicationFormId,
      response
    );
  }
}
