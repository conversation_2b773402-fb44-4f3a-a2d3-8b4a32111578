# GUS SF UNFC Integration

This module handles the integration between Salesforce and UNFC (Ellucian) systems.

## Mapping Validation Mechanism

The mapping validation mechanism ensures that all required fields are present in the source data before attempting to transform it. This helps prevent errors and ensures data integrity.

### How It Works

1. **Required Fields Definition**: Each mapping metadata object has a corresponding required fields definition that specifies which fields must be present in the source data.

2. **Validation Function**: The `validateMapping` function checks if all required fields exist in the source data before transformation.

3. **Integration with Mapping**: The validation is integrated with the existing `mapData` function in the `DynamicMappingService` class.

4. **Error Handling**: If validation fails, the mapping function returns an object with validation errors instead of proceeding with the transformation.

### Usage

```typescript
// With validation enabled
const mappedData = await dynamicMapper.mapData(
  sourceData,
  mappingMetadata,
  picklistDetails,
  "person", // Mapping type for validation
  true      // Enable validation
);

// Check if validation failed
if (mappedData.__isValid === false) {
  console.error("Validation failed:", mappedData.__validationErrors);
  // Handle validation failure
}
```

### Required Fields Definitions

Required fields are defined for each mapping type:

- **Person**: Basic personal information like name, date of birth, etc.
- **Admission Application**: Application details like program, site, academic period, etc.
- **Academic Programs**: Program details like student ID, program ID, academic level, etc.
- **Aptitude Assessment**: Assessment details like student ID, assessment ID, score, etc.
- **Emergency Contact**: Contact details like person ID, contact name, relationship, etc.
- **Visa Application**: Visa details like person ID, visa type, expiration date, etc.
- **External Education**: Education history details like student ID, institution, attendance periods, etc.

### Validation Types

The validation supports two types of requirements:

1. **All Required**: All specified fields must be present in the source data.
   ```typescript
   "fieldName": ["path.to.field1", "path.to.field2"]
   ```

2. **At Least One Required**: At least one of the specified fields must be present.
   ```typescript
   "fieldName": {
     paths: ["path.to.field1", "path.to.field2"],
     atLeastOne: true
   }
   ```

### Nested Path Support

The validation mechanism supports nested object paths and array notation:

- Simple paths: `"Account.FirstName"`
- Array paths: `"names[0].firstName"`
- Deeply nested paths: `"Account.Addresses[0].Street"`

## Implementation Details

The validation mechanism is implemented in the following files:

- `mappingValidation.ts`: Contains the validation functions and required fields definitions
- `unfcMappings.ts`: Modified to integrate validation with the mapping process
- `applicationHandler.ts`: Updated to use validation when mapping data

## Error Handling

When validation fails, the mapping function returns an object with the following properties:

- `__isValid`: Set to `false` to indicate validation failure
- `__validationErrors`: Array of error messages describing the validation failures
- `__missingFields`: Array of field paths that are missing in the source data

The application handler checks for these properties and throws appropriate errors with detailed messages.
