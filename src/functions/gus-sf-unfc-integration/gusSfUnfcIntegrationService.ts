import { DynamoDBService } from "src/common/dynamodbService";
import { EventHandlerFactory } from "./eventHandlerFactory";
import { SnsService } from "src/common/snsService";
import { storeFailedRecordsQueue } from "src/common/storeFailedRecords";
import { checkExistingMessageGroupId } from "src/common/checkFailedRecords";
import { LoggerService } from "src/common/cloudwatchService";
import { LoggerEnum } from "@gus-eip/loggers";

const loggerService = new LoggerService();
const loggerEnum = new LoggerEnum();
const dbService = new DynamoDBService();

let correlationId: string;
let usecase: string;
let applicationFormId: string;
let brand: string;

export const handleSfRequests = async (event) => {
  const clonedEvent = deepClone(event);
  console.log("clonedEvent-->", clonedEvent);
  const responses = [];

  for (const record of clonedEvent.Records) {
    try {
      const isFailedMessageGroupData = await checkExistingMessageGroupId(
        record,
        process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
        "GUS_SF_UNFC"
      );

      console.log("isFailedMessageGroupData", isFailedMessageGroupData);

      if (isFailedMessageGroupData === "No messages to process") {
        console.log("Record", record.body);

        const eventBody = JSON.parse(record.body);
        const platformEventMessage = JSON.parse(eventBody.Message);

        console.log("platformEventMessage", platformEventMessage);

        correlationId = platformEventMessage.event?.EventUuid;
        usecase = platformEventMessage.payload.Scenario__c;
        applicationFormId = platformEventMessage.payload.Application_Form_Id__c;
        brand = platformEventMessage.payload.BusinessUnitFilter__c || "UNFC";

        await loggerService.log(
          correlationId,
          new Date().toISOString(),
          loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_HANDLER,
          loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_QUEUE,
          loggerEnum.Component.UNFC_ELLUCIAN,
          loggerEnum.Event.EVENT_INITIATED,
          usecase,
          platformEventMessage.payload,
          {},
          `Event initiated for ${usecase}`,
          brand,
          applicationFormId,
          "Application_Form_Id__c",
          applicationFormId
        );

        const handler = EventHandlerFactory.getHandler(usecase);

        if (handler && typeof handler.handleMessage === "function") {
          const response = await handler.handleMessage(record);
          console.log("Response", response);

          await loggerService.log(
            correlationId,
            new Date().toISOString(),
            loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_HANDLER,
            loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_QUEUE,
            loggerEnum.Component.UNFC_ELLUCIAN,
            loggerEnum.Event.OPERATION_COMPLETED,
            usecase,
            platformEventMessage.payload,
            {},
            `Operation completed for ${usecase}`,
            brand,
            applicationFormId,
            "Application_Form_Id__c",
            applicationFormId,
            "",
            "",
            "",
            applicationFormId,
            response
          );

          responses.push(response);
        } else {
          console.log(`No handler found for scenario: ${usecase}`);

          await loggerService.error(
            correlationId,
            new Date().toISOString(),
            loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_HANDLER,
            loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_QUEUE,
            loggerEnum.Component.UNFC_ELLUCIAN,
            loggerEnum.Event.OPERATION_FAILED,
            usecase,
            platformEventMessage.payload,
            {},
            `No handler found for scenario: ${usecase}`,
            brand,
            applicationFormId,
            "Application_Form_Id__c",
            applicationFormId
          );

          responses.push(null);
        }
      } else {
        console.log(
          `Message already processed and failed: ${applicationFormId}`
        );
      }
    } catch (error) {
      console.error("Error processing record:", error);

      await loggerService.error(
        correlationId,
        new Date().toISOString(),
        loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_HANDLER,
        loggerEnum.Component.GUS_SALESFORCE_UNFC_INTEGRATION_QUEUE,
        loggerEnum.Component.UNFC_ELLUCIAN,
        loggerEnum.Event.OPERATION_FAILED,
        usecase,
        {},
        {},
        `Error processing record: ${error.message || JSON.stringify(error)}`,
        brand,
        applicationFormId,
        "Application_Form_Id__c",
        applicationFormId
      );
    }
  }

  return responses;
};

export const handleFailedRecords = async () => {
  try {
    console.log("Processing failed records");

    // Query for failed records
    const params = {
      TableName: process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
      KeyConditionExpression: "PK = :partitionKey",
      ExpressionAttributeValues: {
        ":partitionKey": "GUS_SF_UNFC",
      },
    };

    const partitionResponse = await dbService.queryObjects(params);
    console.log("PartitionItemData -->", partitionResponse);

    if (partitionResponse.Items && partitionResponse.Items.length > 0) {
      for (const partitionItem of partitionResponse.Items) {
        console.log("Item -->", partitionItem);

        if (partitionItem.retryCount < 3) {
          const params = {
            TableName:
              process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
            KeyConditionExpression: "PK = :partitionKey AND SK = :sortKey",
            ExpressionAttributeValues: {
              ":partitionKey": partitionItem.PK,
              ":sortKey": partitionItem.SK,
            },
          };

          const failedRecordResponse = await dbService.queryObjects(params);
          console.log("FailedRecordResponse -->", failedRecordResponse);

          if (
            failedRecordResponse.Items &&
            failedRecordResponse.Items.length > 0
          ) {
            for (const failedRecord of failedRecordResponse.Items) {
              try {
                // Process the failed record
                await handleSfRequests({
                  Records: [JSON.parse(failedRecord.record)],
                });

                // Update retry count
                await dbService.updateObject(
                  process.env.GUS_EIP_INTEGRATION_FAILED_RECORDS_TABLE_NAME,
                  {
                    PK: partitionItem.PK,
                    SK: partitionItem.SK,
                  },
                  {
                    retryCount: (partitionItem.retryCount || 0) + 1,
                  }
                );
              } catch (error) {
                console.error("Error processing failed record:", error);
              }
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Error processing failed records:", error);
  }
};

const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item));
  }
  const clonedObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      clonedObj[key] = deepClone(obj[key]);
    }
  }
  return clonedObj;
};
