import * as countries from "i18n-iso-countries";

countries.registerLocale(require("i18n-iso-countries/langs/en.json"));

/**
 * Converts a country code or name to ISO 3166-1 alpha-3 format
 * @param countryInput - Country code (alpha-2 or alpha-3) or country name
 * @returns ISO 3166-1 alpha-3 country code or the original input if conversion fails
 */
export function convertToAlpha3CountryCode(countryInput: string): string {
  if (!countryInput) return countryInput;

  try {
    // If it's neither alpha-2 nor alpha-3, try to get the alpha-3 code by name
    const alpha3Code = countries.getAlpha3Code(countryInput, "en");
    if (alpha3Code) {
      return alpha3Code;
    }
  } catch (error) {
    console.warn(
      `Error converting country code ${countryInput}: ${error.message}`
    );
    return countryInput;
  }

  // Return the original input if conversion fails
  return countryInput;
}

/**
 * Creates an address object with proper ISO 3166-1 alpha-3 country code
 * Format differs based on whether the address is domestic (Canadian) or international
 *
 * @param addressLines - Array of address lines
 * @param city - City name
 * @param state - State or province
 * @param countryCode - Country code (will be converted to alpha-3)
 * @param postalCode - Postal or zip code
 * @param addressType - Type of address (e.g., 'mailing', 'other')
 * @param isPrimary - Whether this is the primary address
 * @returns Formatted address object
 */
export function createAddress(
  addressLines: string[],
  city: string,
  state: string | null,
  country: string,
  postalCode: string,
  addressType: any,
  isPrimary: boolean = false
): Record<string, any> {
  const alpha3CountryCode = convertToAlpha3CountryCode(country);

  const address: Record<string, any> = {
    address: {
      addressLines: addressLines,
      place: {
        country: {
          code: alpha3CountryCode,
          locality: city,
          postalCode: postalCode,
        },
      },
    },
    type: addressType,
  };

  if (state && alpha3CountryCode === "CAN") {
    address.address.place.country.region = {
      code: state,
    };
  }

  // Add preference if this is the primary address
  if (isPrimary) {
    address.preference = "primary";
  }

  return address;
}

/**
 * Common mapping function that maps fields from source to target based on mapping definition
 * @param event - Source data object
 * @param mappings - Mapping definition object
 * @returns Mapped data object
 */
export async function mapFields(
  event: any,
  mappings: Record<string, any>
): Promise<Record<string, any>> {
  const mappedDetails: Record<string, any> = {};

  for (const [key, value] of Object.entries(mappings)) {
    if (key.includes(".")) {
      const [objectKey, propertyKey] = key.split(".");
      if (event?.[objectKey] && event[objectKey][propertyKey] !== undefined) {
        if (Array.isArray(value)) {
          value.forEach((mappedKey: string) => {
            if (event[objectKey][propertyKey]) {
              mappedDetails[mappedKey] = event[objectKey][propertyKey];
            }
          });
        } else if (typeof value === "string") {
          if (event[objectKey][propertyKey]) {
            mappedDetails[value] = event[objectKey][propertyKey];
          }
        }
      }
    } else {
      if (event?.[key] !== undefined) {
        if (Array.isArray(value)) {
          value.forEach((mappedKey: string) => {
            if (event[key]) {
              mappedDetails[mappedKey] = event[key];
            }
          });
        } else if (typeof value === "string") {
          if (event[key]) {
            mappedDetails[value] = event[key];
          }
        }
      }
    }
  }

  return mappedDetails;
}

// Type definitions for the dynamic mapping system
export type TransformFunction = (
  value: any,
  sourceData: any,
  unfcPicklistDetails: any
) => any;

export interface MappingMetadata {
  [targetPath: string]:
    | string
    | string[]
    | {
        sourcePath?: string;
        transform?: TransformFunction;
        condition?: {
          field: string;
          value: any;
          operator?: "eq" | "neq" | "contains" | "startsWith" | "endsWith";
        };
        default?: any;
      };
}

/**
 * Dynamic mapping service that uses metadata to transform data
 */
export class DynamicMappingService {
  /**
   * Maps data from source to target based on metadata definition
   * @param sourceData - Source data object
   * @param mappingMetadata - Metadata defining how to map fields
   * @param unfcPicklistDetails - Picklist details for mapping
   * @returns Mapped data object
   */
  public async mapData(
    sourceData: Record<string, any>,
    mappingMetadata: MappingMetadata,
    unfcPicklistDetails?: any
  ): Promise<Record<string, any>> {
    const result: Record<string, any> = {};

    for (const [targetPath, mapping] of Object.entries(mappingMetadata)) {
      try {
        // Handle simple string mapping (direct path)
        if (typeof mapping === "string") {
          const value = this.getValueByPath(sourceData, mapping);
          if (value != null) {
            this.setValueByPath(result, targetPath, value);
          }
          continue;
        }

        // Handle array of source paths (map to multiple targets)
        if (Array.isArray(mapping)) {
          for (const sourcePath of mapping) {
            const value = this.getValueByPath(sourceData, sourcePath);
            if (value != null) {
              this.setValueByPath(result, targetPath, value);
              break; // Use the first non-undefined value
            }
          }
          continue;
        }

        // Handle complex mapping with conditions and transformations
        if (typeof mapping === "object") {
          // Check condition if present
          if (mapping.condition) {
            const { field, value, operator = "eq" } = mapping.condition;
            const fieldValue = this.getValueByPath(sourceData, field);

            const conditionMet = this.evaluateCondition(
              fieldValue,
              value,
              operator
            );
            if (!conditionMet) {
              // If condition not met and default exists, use default
              if (mapping.default !== undefined) {
                this.setValueByPath(result, targetPath, mapping.default);
              }
              continue;
            }
          }

          // Get value from source path if specified
          let mappedValue: any = undefined;
          if (mapping.sourcePath) {
            mappedValue = this.getValueByPath(sourceData, mapping.sourcePath);
          }

          // Apply transformation if specified
          if (mapping.transform && typeof mapping.transform === "function") {
            mappedValue = await mapping.transform(
              mappedValue,
              sourceData,
              unfcPicklistDetails
            );
          }

          // Set the value if not undefined
          if (mappedValue != null) {
            this.setValueByPath(result, targetPath, mappedValue);
          } else if (mapping.default !== undefined) {
            this.setValueByPath(result, targetPath, mapping.default);
          }
        }
      } catch (error) {
        console.error(
          `Error mapping field ${targetPath}:`,
          JSON.stringify(error)
        );
        // Continue with other mappings even if one fails
      }
    }

    return result;
  }

  /**
   * Gets a value from an object by path (e.g., "Account.FirstName")
   * @param obj - Source object
   * @param path - Path to the value
   * @returns Value at the path or undefined if not found
   */
  private getValueByPath(obj: Record<string, any>, path: string): any {
    if (!path) return undefined;

    // Handle array notation like "names[0].firstName"
    const normalizedPath = path.replace(/\[(\d+)\]/g, ".$1");
    const parts = normalizedPath.split(".");

    let current = obj;
    for (const part of parts) {
      if (current != null) {
        current = current[part];
      }
    }

    return current;
  }

  /**
   * Sets a value in an object by path (e.g., "names[0].firstName")
   * @param obj - Target object
   * @param path - Path where to set the value
   * @param value - Value to set
   */
  private setValueByPath(
    obj: Record<string, any>,
    path: string,
    value: any
  ): void {
    if (!path) return;

    // Handle array notation like "names[0].firstName"
    const normalizedPath = path.replace(/\[(\d+)\]/g, ".$1");
    const parts = normalizedPath.split(".");

    let current = obj;
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];

      // Check if next part is an array index
      const nextIsArrayIndex =
        i < parts.length - 1 && /^\d+$/.test(parts[i + 1]);

      if (!(part in current)) {
        // Create object or array based on next part
        current[part] = nextIsArrayIndex ? [] : {};
      } else if (nextIsArrayIndex && !Array.isArray(current[part])) {
        // Convert to array if needed
        current[part] = [];
      } else if (!nextIsArrayIndex && Array.isArray(current[part])) {
        // Convert to object if needed
        current[part] = {};
      }

      current = current[part];
    }

    const lastPart = parts[parts.length - 1];
    current[lastPart] = value;
  }

  /**
   * Evaluates a condition based on the specified operator
   * @param fieldValue - Value from the source data
   * @param conditionValue - Value to compare against
   * @param operator - Comparison operator
   * @returns Whether the condition is met
   */
  private evaluateCondition(
    fieldValue: any,
    conditionValue: any,
    operator: "eq" | "neq" | "contains" | "startsWith" | "endsWith"
  ): boolean {
    if (fieldValue === undefined) return false;

    switch (operator) {
      case "eq":
        return fieldValue === conditionValue;
      case "neq":
        return fieldValue !== conditionValue;
      case "contains":
        return String(fieldValue)
          .toLowerCase()
          .includes(String(conditionValue).toLowerCase());
      case "startsWith":
        return String(fieldValue)
          .toLowerCase()
          .startsWith(String(conditionValue).toLowerCase());
      case "endsWith":
        return String(fieldValue)
          .toLowerCase()
          .endsWith(String(conditionValue).toLowerCase());
      default:
        return false;
    }
  }
}

// Create a singleton instance for export
export const dynamicMapper = new DynamicMappingService();
