import { handlerPath } from "@libs/handler-resolver";

export const gusSfUnfcIntegration = {
  handler: `${handlerPath(
    __dirname
  )}/gusSfUnfcIntegrationService.handleSfRequests`,
  name: "gus-sf-unfc-integration-${self:provider.stage}",
  events: [
    {
      sqs: {
        arn: "${self:provider.environment.GUS_SF_UNFC_INTEGRATION_SQS_QUEUE_ARN}",
      },
    },
  ],
  role: "${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}",
  timeout: 180,
  tags: {
    PROJECT: "EIP",
    ENVIRONMENT: "${self:provider.stage}",
    TEAM: "EIP Development Team",
  },
};

// export const gusSfUnfcFailedRecordProcessor = {
//     handler: `${handlerPath(__dirname)}/gusSfUnfcIntegrationService.handleFailedRecords`,
//     name: 'gus-sf-unfc-failed-record-processor-${self:provider.stage}',
//     events: [
//         {
//             schedule: 'rate(5 minutes)'
//         }
//     ],
//     role: '${self:provider.environment.GUS_EIP_INTEGRATION_HANDLER_ACCESS_ROLE}',
//     timeout: 180,
//     tags: {
//         PROJECT: "EIP",
//         ENVIRONMENT: "${self:provider.stage}",
//         TEAM: "EIP Development Team"
//     }
// };
