import { ApplicationHandler } from "./eventHandlers/applicationHandler";
import { ApplicationDocumentHandler } from "./eventHandlers/newDocumentHandler";
import { UpdateDocumentHandler } from "./eventHandlers/updateDocumentHandler";

export class EventHandlerFactory {
  static getHandler(scenario) {
    switch (scenario) {
      case "GUS_NEW_APPLICATION":
        return new ApplicationHandler();
      case "GUS_NEW_DOCUMENT":
        return new ApplicationDocumentHandler();
      case "GUS_DOCUMENT_UPDATE":
        return new UpdateDocumentHandler();
      default:
        console.log("No handler found for event type: " + scenario);
        break;
    }
  }
}
