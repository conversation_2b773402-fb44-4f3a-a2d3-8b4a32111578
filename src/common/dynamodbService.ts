import * as A<PERSON> from "aws-sdk";

export class DynamoDBService {
  private dynamoDB: AWS.DynamoDB.DocumentClient;

  constructor() {
    this.dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION || "eu-west-1",
    });
  }

  async putObject(
    table: string,
    object: AWS.DynamoDB.DocumentClient.PutItemInputAttributeMap
  ): Promise<AWS.DynamoDB.DocumentClient.PutItemOutput | void> {
    const params: AWS.DynamoDB.DocumentClient.PutItemInput = {
      TableName: table,
      Item: {
        ...object,
        createdAt: new Date().toISOString(),
      },
    };

    try {
      return await this.dynamoDB.put(params).promise();
    } catch (err) {
      throw err;
    }
  }
  async updateObject(
    table: string,
    key: AWS.DynamoDB.DocumentClient.Key,
    object: AWS.DynamoDB.DocumentClient.AttributeMap
  ): Promise<void> {
    const updatedAt = new Date().toISOString();
    object.updatedAt = updatedAt;

    const { keys, update } = this.dynamodbUpdateRequest({
      keys: key,
      values: object,
    });
    const params: AWS.DynamoDB.DocumentClient.UpdateItemInput = {
      TableName: table,
      Key: keys,
      ...update,
    };
    await this.dynamoDB.update(params).promise();
  }

  async getObject(
    table: string,
    keys: AWS.DynamoDB.DocumentClient.Key,
    projectionExpression?: string,
    expressionAttributeNames?: { [key: string]: string }
  ): Promise<AWS.DynamoDB.DocumentClient.GetItemOutput> {
    const params: AWS.DynamoDB.DocumentClient.GetItemInput = {
      TableName: table,
      Key: keys,
    };

    if (projectionExpression) {
      params.ProjectionExpression = projectionExpression;
    }
    if (expressionAttributeNames) {
      params.ExpressionAttributeNames = expressionAttributeNames;
    }

    return await this.dynamoDB.get(params).promise();
  }

  async queryObjects(
    params: AWS.DynamoDB.DocumentClient.QueryInput
  ): Promise<AWS.DynamoDB.DocumentClient.QueryOutput> {
    try {
      return await this.dynamoDB.query(params).promise();
    } catch (error) {
      throw error;
    }
  }
  async deleteItem(
    table: string,
    key: AWS.DynamoDB.DocumentClient.Key
  ): Promise<any> {
    const params: AWS.DynamoDB.DocumentClient.DeleteItemInput = {
      TableName: table,
      Key: key,
    };

    try {
      await this.dynamoDB.delete(params).promise();
      return "Item deleted successfully";
    } catch (error) {
      return error;
    }
  }
  private dynamodbUpdateRequest(params: {
    keys: AWS.DynamoDB.DocumentClient.Key;
    values: AWS.DynamoDB.DocumentClient.AttributeMap;
  }): {
    keys: AWS.DynamoDB.DocumentClient.Key;
    update: Partial<AWS.DynamoDB.DocumentClient.UpdateItemInput>;
  } {
    const { keys, values } = params;
    const sets: string[] = [];
    const removes: string[] = [];
    const expressionNames: { [key: string]: string } = {};
    const expValues: { [key: string]: any } = {};

    for (const [key, value] of Object.entries(values)) {
      expressionNames[`#${key}`] = key;
      if (value !== undefined && value !== null) {
        sets.push(`#${key} = :${key}`);
        expValues[`:${key}`] = value;
      } else {
        removes.push(`#${key}`);
      }
    }

    let expression = sets.length ? `SET ${sets.join(", ")}` : "";
    expression += removes.length ? ` REMOVE ${removes.join(", ")}` : "";
    return {
      keys,
      update: {
        UpdateExpression: expression,
        ExpressionAttributeNames: expressionNames,
        ExpressionAttributeValues: expValues,
      },
    };
  }
  async scanTable(
    table: string,
    filterExpression?: string,
    expressionAttributeValues?: AWS.DynamoDB.DocumentClient.ExpressionAttributeValueMap,
    projectionExpression?: string,
    expressionAttributeNames?: { [key: string]: string }
  ): Promise<AWS.DynamoDB.DocumentClient.ScanOutput> {
    const params: AWS.DynamoDB.DocumentClient.ScanInput = {
      TableName: table,
    };
    if (filterExpression) params.FilterExpression = filterExpression;
    if (expressionAttributeValues)
      params.ExpressionAttributeValues = expressionAttributeValues;
    if (projectionExpression)
      params.ProjectionExpression = projectionExpression;
    if (expressionAttributeNames)
      params.ExpressionAttributeNames = expressionAttributeNames;
    return await this.dynamoDB.scan(params).promise();
  }
}
