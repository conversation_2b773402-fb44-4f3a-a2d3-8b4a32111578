import { CloudWatchLoggerService } from "@gus-eip/loggers";

export class LoggerService {
  cloudWatchLoggerService: CloudWatchLoggerService;

  constructor() {
    this.cloudWatchLoggerService = new CloudWatchLoggerService(
      process.env.REGION,
      process.env.LOGGER_LOG_GROUP_NAME,
      process.env.TEAMS_WEBHOOK_URL,
      true
    );
  }

  async log(
    requestId,
    timestamp,
    component,
    source,
    destination,
    event,
    usecase,
    sourcePayload,
    destinationPayload,
    logMessage,
    brand,
    secondaryKey,
    entityKeyField,
    entityKey,
    destinationObjectType?,
    destinationObjectId?,
    sourceObjectType?,
    sourceObjectId?,
    destinationResponse?
  ) {
    console.log(`gus-eip-integration-handlers/${entityKey}/${requestId}`)
    await this.cloudWatchLoggerService.log(
      requestId,
      timestamp,
      component,
      source,
      destination,
      event,
      usecase,
      sourcePayload,
      destinationPayload,
      logMessage,
      brand,
      secondaryKey,
      `gus-eip-integration-handlers/${entityKey}/${requestId}`,
      entityKeyField,
      entityKey,
      destinationObjectType,
      destinationObjectId,
      sourceObjectType,
      sourceObjectId,
      destinationResponse
    );
  }

  async error(
    requestId,
    timestamp,
    component,
    source,
    destination,
    event,
    usecase,
    sourcePayload,
    destinationPayload,
    errorMessage,
    brand,
    secondaryKey,
    entityKeyField,
    entityKey,
    destinationObjectType?,
    destinationObjectId?,
    sourceObjectType?,
    sourceObjectId?,
    destinationResponse?
  ) {
    await this.cloudWatchLoggerService.error(
      requestId,
      timestamp,
      component,
      source,
      destination,
      event,
      usecase,
      sourcePayload,
      destinationPayload,
      errorMessage,
      brand,
      secondaryKey,
      `gus-eip-integration-handlers/${entityKey}/${requestId}`,
      entityKeyField,
      entityKey,
      destinationObjectType,
      destinationObjectId,
      sourceObjectType,
      sourceObjectId,
      destinationResponse
    );
  }
}
