import { SNS } from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';

export class SnsService {
    private readonly sns: SNS;
    constructor() {
        this.sns = new SNS({
            region: process.env.REGION
        })
    }

    async publishMessages(messageData, messageGroupId, topicArn, brand?) {
        let messageAttributes = {};

        if (brand) {
            messageAttributes = {
                source: {
                    DataType: 'String',
                    StringValue: brand,
                },
            };
        }

        let response;
        try {
            if (Array.isArray(messageData)) {
                for (const message of messageData) {
                    response = await this.publishMessageToSNS(topicArn, message, messageGroupId, brand ? messageAttributes : undefined);
                }
            } else {
                response = await this.publishMessageToSNS(topicArn, messageData, messageGroupId, brand ? messageAttributes : undefined);
            }

            return response;
        } catch (err) {
            console.log('ERR', err);
            return "Error publishing messages";
        }
    }

    async publishMessageToSNS(topicArn, message, messageGroupId, messageAttributes?) {
        try {
            const params: SNS.PublishInput = {
                TopicArn: topicArn,
                Message: JSON.stringify(message),
                MessageGroupId: messageGroupId,
            };

            if (messageAttributes) {
                params.MessageAttributes = messageAttributes;
            }

            const res = await this.sns.publish(params).promise();

            console.log('SNS', res);
            console.log('Message published successfully');

            return res;
        } catch (err) {
            console.log('ERR', err);
            throw err;
        }
    }
}
