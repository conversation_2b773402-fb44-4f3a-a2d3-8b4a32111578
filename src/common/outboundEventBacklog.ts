import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  PutCommand,
  QueryCommand,
  UpdateCommand,
  DynamoDBDocumentClient,
} from "@aws-sdk/lib-dynamodb";

const dynamoClient = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(dynamoClient);

interface OutboundBacklogItem {
  applicationId: string;
  eventType: string;
  payload: any;
  brand?: string;
  status?: "pending" | "processed" | string;
  timestamp?: string;
}

export const outboundBacklogService = {
  /**
   * Store new backlog item
   */
  async store(tableName: string, item: OutboundBacklogItem): Promise<void> {
    const dbItem = {
      PK: item.applicationId,
      eventType: item.eventType,
      payload: item.payload,
      brand: item.brand || "Unknown",
      status: item.status || "pending",
      timestamp: item.timestamp || new Date().toISOString(),
    };

    await docClient.send(
      new PutCommand({
        TableName: tableName,
        Item: dbItem,
      })
    );
    console.log(
      `✅ Stored backlog for ${item.applicationId} (${item.eventType})`
    );
  },

  /**
   * Get existing event by applicationId and eventType
   */
  async get(tableName: string, applicationId: string, eventType: string) {
    const result = await docClient.send(
      new QueryCommand({
        TableName: tableName,
        KeyConditionExpression: "PK = :pk",
        ExpressionAttributeValues: {
          ":pk": applicationId,
        },
      })
    );

    return result.Items?.find((item) => item.eventType === eventType) || null;
  },

  /**
   * Mark event as processed
   */
  async markProcessed(
    tableName: string,
    applicationId: string,
    eventType: string
  ): Promise<"marked-processed" | "not-found"> {
    const result = await docClient.send(
      new QueryCommand({
        TableName: tableName,
        KeyConditionExpression: "PK = :pk",
        ExpressionAttributeValues: {
          ":pk": applicationId,
        },
      })
    );

    const item = result.Items?.find((i) => i.eventType === eventType);
    if (!item) return "not-found";

    await docClient.send(
      new UpdateCommand({
        TableName: tableName,
        Key: { PK: item.PK },
        UpdateExpression: "SET #status = :processed",
        ExpressionAttributeNames: {
          "#status": "status",
        },
        ExpressionAttributeValues: {
          ":processed": "processed",
        },
      })
    );

    console.log(`✅ Marked ${applicationId} (${eventType}) as processed`);
    return "marked-processed";
  },
};
