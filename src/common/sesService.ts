import * as AWS from 'aws-sdk';

AWS.config.update({ region: process.env.REGION });

const ses = new AWS.SES();

export async function sendEmail(
  toAddress: string,
  fromAddress: string,
  subject: string,
  bodyHtml: string,
  source?: string,
): Promise<any> {
  let rawEmail = `From: ${fromAddress}\n`;
  rawEmail += `To: ${toAddress}\n`;
  rawEmail += `Subject: ${subject}\n`;
  rawEmail += `MIME-Version: 1.0\n`;
  rawEmail += `Content-Type: text/html; charset=UTF-8\n`;

  if (source) {
    rawEmail += `X-Source: ${source}\n`;
  }

  rawEmail += `\n${bodyHtml}`;

  const params: AWS.SES.SendRawEmailRequest = {
    RawMessage: {
      Data: Buffer.from(rawEmail),
    },
  };

  try {
    const result = await ses.sendRawEmail(params).promise();
    console.log('Raw email sent successfully:', result);
    return result;
  } catch (error) {
    console.error('Error sending raw email:', error);
    throw error;
  }
}
