import { SQSClient, SendMessageCommand } from "@aws-sdk/client-sqs";

const REGION = process.env.AWS_REGION;

const sqsClient = new SQSClient({ region: REGION });

export const sendToSQS = async (
  queueUrl: string,
  data: any,
  messageGroupId: string,
  messageDeduplicationId: string
) => {
  try {
    const params = {
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(data),
      MessageGroupId: messageGroupId,
      MessageDeduplicationId: messageDeduplicationId,
    };

    const command = new SendMessageCommand(params);
    await sqsClient.send(command);
    console.log("Message sent to SQS successfully", data);
  } catch (error) {
    console.error("Error sending message to SQS", error);
    throw new Error("Failed to send message to SQS");
  }
};
