import * as AWS from 'aws-sdk';
const s3 = new AWS.S3();
const sts = new AWS.STS();

export class S3Service {
    constructor() { }
    async getBase64File(
        bucket: string,
        key: string,
        roleArn: string,
    ): Promise<string> {
        const s3 = await this.getS3CredentialsByRole(roleArn);

        console.log("S3 -->", s3)
        const params = {
            Bucket: bucket,
            Key: key,
        };
        console.log("Params -->", params)

        const data = await new Promise<AWS.S3.GetObjectOutput>(
            (resolve, reject) => {
                s3.getObject(params, (err, data) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(data);
                    }
                });
            },
        );

        console.log("Data -->", params)
        if (!data.Body) {
            throw new Error('File not found or empty');
        }

        const base64File = data.Body.toString('base64');
        console.log("base64File -->", base64File)
        return base64File;
    }

    async getS3CredentialsByRole(roleArn): Promise<any> {
        const sessionName = `Session-${Date.now()}`;
        console.log("sessionName",sessionName)
        const param: AWS.STS.AssumeRoleRequest = {
            RoleArn: roleArn,
            RoleSessionName: sessionName,
        };
        console.log("param",param)
        const data = await new Promise((resolve, reject) => {
            sts.assumeRole(param, (err, data) => {
                if (err) reject(err);
                else resolve(data);
            });
        });
        console.log("data",data)
        const credentials = data['Credentials'];
        const s3 = new AWS.S3({
            accessKeyId: credentials.AccessKeyId,
            secretAccessKey: credentials.SecretAccessKey,
            sessionToken: credentials.SessionToken,
        });
        console.log("s3",s3)
        return s3;
    }

    async uploadFile(
        bucket: string,
        key: string,
        fileContent: Buffer,
        roleArn: string,
    ): Promise<void> {
        const s3 = await this.getS3CredentialsByRole(roleArn);
        const params = {
            Bucket: bucket,
            Key: key,
            Body: fileContent,
        };
        await new Promise<void>((resolve, reject) => {
            s3.putObject(params, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    }
}
