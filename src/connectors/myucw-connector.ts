import {
  SecretsManagerClient,
  GetSecretValueCommand,
  UpdateSecretCommand,
  CreateSecretCommand,
} from "@aws-sdk/client-secrets-manager";
import axios from "axios";

const secretName = process.env.UCW_ACCESS_TOKEN_SECRET;
const client = new SecretsManagerClient({ region: process.env.REGION });

let myUCWCred = JSON.parse(process.env.MYUCW_CRED || "{}");
export const myUCWAuth = async () => {
  const myUCWCred = JSON.parse(process.env.MYUCW_CRED);
  console.log("MYUCW Cred ", myUCWCred);
  const path = `${myUCWCred.myUcwPath}api/auth/`;
  const response = await axios.post(path, {
    sis_token: myUCWCred.sis_token,
    username: myUCWCred.username,
    password: myUCWCred.password,
  });
  response.data.refresh_token_expiry = new Date(
    Date.now() + 12 * 60 * 60 * 1000
  ).toISOString();
  return response.data;
};

const makeRequest = async (method, endpoint, data = null) => {
  console.log(
    `Making ${method.toUpperCase()} request to endpoint: ${endpoint}`
  );
  const myUCWAuthToken = await getMyUCWAuthToken();
  console.log("Using access token:", myUCWAuthToken.access_token);

  const requestConfig = {
    method,
    url: `${myUCWCred.myUcwPath}api/${endpoint}/`,
    headers: { Authorization: `Bearer ${myUCWAuthToken.access_token}` },
    ...(data && { data }),
  };

  try {
    const response = await axios(requestConfig);
    console.log("API response:", response.data);
    if (response.data.error) {
      throw { response: { data: JSON.stringify(response.data) } };
    }
    return response.data;
  } catch (error) {
    console.log(`Error during ${method} request:`, error);
    if (error.response?.status === 401) {
      console.log("Token expired. Refreshing...");
      const refreshedToken = await myUCWAuth();
      await updateSecret(
        refreshedToken,
        refreshedToken.refresh_token,
        refreshedToken.refresh_token_expiry
      );
      requestConfig.headers.Authorization = `Bearer ${refreshedToken.access_token}`;
      try {
        const response = await axios(requestConfig);
        console.log("API response after refresh:", response.data);
        return response.data;
      } catch (error) {
        console.log("Error after token refresh:", error);
        throw error?.response?.data;
      }
    }
    throw error?.response?.data;
  }
};

export const getMYUCWData = async (endpoint) => makeRequest("get", endpoint);
export const postMYUCWData = async (endpoint, data) =>
  makeRequest("post", endpoint, data);
export const putMYUCWData = async (endpoint, data) =>
  makeRequest("put", endpoint, data);

export const getMyUCWAuthToken = async () => {
  console.log("Fetching UCW Auth Token...");
  try {
    const secret = await getSecret();
    console.log("Secret fetched:", secret);
    if (secret) {
      const { access_token, refresh_token, expiry, refresh_token_expiry } =
        secret;
      const currentTime = Math.floor(Date.now() / 1000);
      const expiryTimestamp = Math.floor(new Date(expiry).getTime() / 1000);
      const refreshExpiryTimestamp = Math.floor(
        new Date(refresh_token_expiry).getTime() / 1000
      );

      console.log(
        "Current Time:",
        currentTime,
        "Expiry Time:",
        expiryTimestamp,
        "Refresh Expiry Time:",
        refreshExpiryTimestamp
      );

      if (expiryTimestamp && currentTime < expiryTimestamp) {
        console.log("Returning existing valid access token.");
        return { access_token };
      }

      if (refreshExpiryTimestamp && currentTime >= refreshExpiryTimestamp) {
        console.log("Refresh token expired, obtaining new token...");
        const newTokenData = await myUCWAuth();
        await updateSecret(
          newTokenData,
          newTokenData.refresh_token,
          newTokenData.refresh_token_expiry
        );
        return { access_token: newTokenData.access_token };
      }

      console.log("Refreshing access token...");
      const newTokenData = await refreshAuthToken(access_token, refresh_token);
      console.log("newTokenData", newTokenData);
      const newRefreshToken = newTokenData.refresh_token || refresh_token;
      const newRefreshTokenExpiry =
        newTokenData.refresh_token_expiry || refresh_token_expiry;
      await updateSecret(newTokenData, newRefreshToken, newRefreshTokenExpiry);
      return { access_token: newTokenData.access_token };
    }

    console.log("No secret found, fetching new token...");
    const newTokenData = await myUCWAuth();
    await createSecret(newTokenData);
    return { access_token: newTokenData.access_token };
  } catch (error) {
    console.error("Error getting UCW auth token:", error);
    const newTokenData = await myUCWAuth();
    await updateSecret(
      newTokenData,
      newTokenData.refresh_token,
      newTokenData.refresh_token_expiry
    );
    return { access_token: newTokenData.access_token };
  }
};

const getSecret = async () => {
  try {
    const command = new GetSecretValueCommand({ SecretId: secretName });
    const response = await client.send(command);

    if (response.SecretString) {
      return JSON.parse(response.SecretString);
    }
  } catch (error) {
    console.log("Error fetching secret: ", error);
    if (error.name === "ResourceNotFoundException") {
      try {
        const newTokenData = await myUCWAuth();
        console.log("New token data in get secret: ", newTokenData);
        await createSecret(newTokenData);
        return newTokenData;
      } catch (error) {
        throw error;
      }
    }
  }
};

const createSecret = async (data) => {
  try {
    const secretValue = JSON.stringify({
      access_token: data.access_token,
      refresh_token: data.refresh_token,
      expiry: data.expiry,
      refresh_token_expiry: data.refresh_token_expiry,
    });

    const command = new CreateSecretCommand({
      Name: secretName,
      SecretString: secretValue,
    });

    await client.send(command);
    console.log("Secret created successfully.");
  } catch (error) {
    console.error("Error creating secret:", error);
  }
};

const updateSecret = async (data, refresh_token, refresh_token_expiry) => {
  try {
    let updatePayload = {
      access_token: data.access_token,
      refresh_token,
      expiry: data.expiry,
      refresh_token_expiry,
    };
    const secretValue = JSON.stringify(updatePayload);

    const command = new UpdateSecretCommand({
      SecretId: secretName,
      SecretString: secretValue,
    });

    await client.send(command);
    console.log("Secret updated successfully.");
  } catch (error) {
    console.log("Error updating secret ", error);
    if (error.name === "ResourceNotFoundException") {
      try {
        const newTokenData = await myUCWAuth();
        await createSecret(newTokenData);
        return newTokenData;
      } catch (error) {
        throw error;
      }
    }
  }
};

export const refreshAuthToken = async (access_token, refresh_token) => {
  try {
    console.log("Initiate Refresh auth token ...");
    const myUCWCred = JSON.parse(process.env.MYUCW_CRED);
    const response = await axios.post(
      `${myUCWCred.myUcwPath}api/auth/`,
      {
        access_token,
        refresh_token,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error refreshing auth token:", error);
    const refreshedToken = await myUCWAuth();
    await updateSecret(
      refreshedToken,
      refreshedToken.refresh_token,
      refreshedToken.refresh_token_expiry
    );
    return refreshedToken;
  }
};

export const getRequiredDocuments = async (sid) => {
  const { requirements } = await getMYUCWData(
    `students/requirements/?sid=${sid}/`
  );
  return requirements.filter((req) => req.requirement_type === "document");
};
