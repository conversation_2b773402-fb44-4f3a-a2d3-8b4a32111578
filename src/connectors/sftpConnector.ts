import Client from "ssh2-sftp-client";
import path from "path";

interface SftpConfig {
  host: string;
  port: number;
  username: string;
  password: string;
}

export class SftpConnector {
  private sftp: Client;
  private config: SftpConfig;

  constructor(config: SftpConfig) {
    console.log("Initializing SFTP connector with config:", {
      host: config.host,
      port: config.port,
      username: config.username,
      // Not logging password for security
    });
    this.sftp = new Client();
    this.config = config;
  }

  async connect(): Promise<void> {
    try {
      console.log("Connecting to SFTP server...", {
        host: this.config.host,
        port: this.config.port,
        username: this.config.username,
      });
      const res = await this.sftp.connect(this.config);
      console.log("Res ->", res);
      console.log("Successfully connected to SFTP server");
    } catch (error) {
      console.error("Error connecting to SFTP server", {
        error,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorStack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.sftp) {
      console.log("Disconnecting from SFTP server...");
      await this.sftp.end();
      console.log("Successfully disconnected from SFTP server");
    }
  }

  async getFile(filePath: string): Promise<Buffer> {
    try {
      console.log("Attempting to get file from SFTP:", { filePath });
      const fileContent = await this.sftp.get(filePath);
      console.log("File content", fileContent);
      console.log("Successfully retrieved file from SFTP", {
        filePath,
        fileSize: fileContent.length,
        fileType: typeof fileContent,
      });
      return fileContent;
    } catch (error) {
      console.error("Error getting file from SFTP", {
        error,
        filePath,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorStack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  async uploadFile(filePath: string, fileContent: Buffer): Promise<void> {
    try {
      console.log("Attempting to upload file to SFTP:", {
        filePath,
        fileSize: fileContent.length,
        fileType: typeof fileContent,
      });
      // Ensure the remote directory exists (SFTP doesn't create directories automatically)
      const dir = path.posix.dirname(filePath);
      console.log("Directory to be created :", dir);
      await this.sftp.mkdir(dir, true);
      await this.sftp.put(fileContent, filePath);
      console.log("Successfully uploaded file to SFTP", { filePath });
    } catch (error) {
      console.error("Error uploading file to SFTP", {
        error,
        filePath,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorStack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  async listFiles(folderPath: string): Promise<Array<{ name: string; modifyTime: Date }>> {
    try {
      console.log("Attempting to list files in SFTP directory:", { folderPath });
      const fileList = await this.sftp.list(folderPath);
      console.log("File list ", fileList)
      const files = fileList.map(file => ({
        name: file.name,
        modifyTime: new Date(file.modifyTime)
      }));
      console.log("Successfully listed files from SFTP directory", {
        folderPath,
        fileCount: files.length,
      });
      return files;
    } catch (error) {
      console.error("Error listing files from SFTP directory", {
        error,
        folderPath,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorStack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }
}
