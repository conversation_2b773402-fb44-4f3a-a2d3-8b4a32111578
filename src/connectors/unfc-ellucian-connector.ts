import axios from "axios";
import { SNS } from "aws-sdk";
import { DynamoDBService } from "../common/dynamodbService";

/**
 * UNFC Ellucian Connector Class
 * Handles authentication, data fetching, and publishing to SNS for the UNFC Ellucian integration
 */
export class UNFCEllucianConnector {
  private apiBaseUrl: string;
  private outboundApiKey: string;
  private inboundApiKey: string;
  private topicArn: string;
  private region: string;
  private authToken: string | null = null;
  private sns: SNS;
  private dynamodbService: DynamoDBService;
  private readonly tableName: string;
  private readonly partitionKey: string = "Unfc_Change_Notification";

  /**
   * Constructor for UNFCEllucianConnector
   */
  constructor() {
    // Get configuration from environment variables
    const unfcCred = { "domain": "https://integrate.elluciancloud.ca/","outboundApiKey": "2220ed5d-1eb7-482e-8c42-b7634ef2895a","inboundApiKey": "219dfb42-16a3-4485-b5e4-4f534976d944"};
    this.apiBaseUrl = unfcCred.domain || "";
    this.outboundApiKey = unfcCred.outboundApiKey || "";
    this.inboundApiKey = unfcCred.inboundApiKey || "";
    this.topicArn = process.env.UNFC_OUTBOUND_TOPIC_ARN || "";
    this.region = process.env.REGION;
    this.sns = new SNS({ region: this.region });
    this.dynamodbService = new DynamoDBService();
    this.tableName = `gus-eip-integration-event-${process.env.stage}`;

    // Validate required configuration
    if (!this.apiBaseUrl || !this.outboundApiKey) {
      console.error(
        "Missing required environment variables for UNFC API configuration"
      );
    }

    if (!this.topicArn) {
      console.error(
        "Missing required environment variable: UNFC_OUTBOUND_TOPIC_ARN"
      );
    }
  }

  /**
   * Authenticates with the UNFC API
   * @returns Authentication token or null if authentication fails
   */
  public async authenticate(apiKey = this.inboundApiKey): Promise<any> {
    try {
      // Make the authentication request
      const response = await axios.post(
        `${this.apiBaseUrl}auth`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${apiKey}`,
          },
        }
      );

      // Check if the response contains a token
      if (response && response.data) {
        this.authToken = response.data;
        return response.data;
      } else {
        throw new Error("Authentication response did not contain a token");
      }
    } catch (error) {
      console.error("Authentication error:", error);
      throw new Error(
        `Authentication with UNFC failed - ${error.message || error}`
      );
    }
  }

  /**
   * Get authentication headers
   * @param authToken Optional auth token to use instead of the stored one
   * @returns Headers object with authentication
   */
  private async getAuthHeaders(
    pathname?: string
  ): Promise<Record<string, string>> {
    const token = await this.authenticate();

    if (pathname && pathname.includes("persons")) {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/vnd.hedtech.integration.v12.6.0+json",
      };
    } else if (pathname && pathname === "person-emergency-contacts") {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/vnd.hedtech.integration.v1+json",
        Accept: "application/vnd.hedtech.integration.v1+json",
      };
    } else if (pathname && pathname === "admission-applications") {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type":
          "application/vnd.hedtech.integration.admission-applications-submissions.v1+json",
        Accept: "application/vnd.hedtech.integration.v16+json",
      };
    } else if (pathname && pathname === "person-visas") {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/vnd.hedtech.integration.v11+json",
        Accept: "application/vnd.hedtech.integration.v11+json",
      };
    } else if (pathname && pathname === "student-academic-programs") {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type":
          "application/vnd.hedtech.integration.student-academic-programs-submissions.v1+json",
        Accept: "application/vnd.hedtech.integration.v17+json",
      };
    } else if (pathname && pathname === "student-aptitude-assessments") {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/vnd.hedtech.integration.v16+json",
        Accept: "application/vnd.hedtech.integration.v16+json",
      };
    } else if (pathname && pathname.includes("unf-appl-remarks")) {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/vnd.hedtech.integration.v1+json",
        Accept: "application/vnd.hedtech.integration.v1+json",
      };
    } else if (pathname && pathname.includes("person-external-education")) {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/vnd.hedtech.integration.v1+json",
        Accept: "application/vnd.hedtech.integration.v1+json",
      };
    } else if (pathname && pathname.includes("x-person-health")) {
      return {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/vnd.hedtech.integration.v1+json",
      };
    }

    return {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };
  }

  /**
   * Retrieves the last processed ID from DynamoDB
   * @returns The last processed ID or null if not found
   */
  public async getLastProcessedID(): Promise<number | null> {
    try {
      console.log("Retrieving last processed ID from DynamoDB");
      const result = await this.dynamodbService.getObject(this.tableName, {
        PK: this.partitionKey,
      });

      if (result.Item && result.Item.lastProcessedID) {
        console.log(`Last processed ID: ${result.Item.lastProcessedID}`);
        return result.Item.lastProcessedID;
      }

      console.log("No last processed ID found in DynamoDB");
      return null;
    } catch (error) {
      console.error("Error retrieving last processed ID:", error);
      return null;
    }
  }

  /**
   * Stores the last processed ID and payload in DynamoDB
   * @param lastProcessedID The ID to store
   * @param payload The payload to store
   */
  public async updateLastProcessedID(
    lastProcessedID: number,
    payload: any
  ): Promise<void> {
    try {
      console.log(`Updating last processed ID to ${lastProcessedID}`);
      await this.dynamodbService.updateObject(
        this.tableName,
        {
          PK: this.partitionKey,
        },
        {
          lastProcessedID,
          lastProcessedRecord: payload,
          updatedAt: new Date().toISOString(),
        }
      );
      console.log("Last processed ID updated successfully");
    } catch (error) {
      console.error("Error updating last processed ID:", error);
      throw error;
    }
  }

  /**
   * Base method to make GET requests to the API
   * @param pathname The path to append to the base URL
   * @param authToken Optional auth token to use instead of the stored one
   * @returns Response data from the API
   */
  public async get(pathname: string, authToken?: string, params?: any
  ): Promise<any> {
    try {
      console.log(`Making GET request to ${pathname}`);

      // Ensure we have an auth token
      if (!this.authToken) {
        console.log("No auth token provided, authenticating first");
        if (pathname.includes("consume")) {
          await this.authenticate(this.outboundApiKey);
        } else {
          await this.authenticate(this.inboundApiKey);
        }
      }

      const headers = await this.getAuthHeaders(pathname);

      const response = await axios.get(`${this.apiBaseUrl}api/${pathname}`, {
        headers,
        params,
      });

      console.log(`Response from ${pathname}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error making GET request to ${pathname}:`, error);

      // Throw a more descriptive error
      if (error.response) {
        throw new Error(
          `Error fetching ${pathname}: ${error.response.status} - ${error.response.data}`
        );
      } else {
        throw new Error(`Error fetching ${pathname}: ${error.message}`);
      }
    }
  }

  /**
   * Base method to make POST requests to the API
   * @param pathname The path to append to the base URL
   * @param data The data to send in the request body
   * @param authToken Optional auth token to use instead of the stored one
   * @returns Response data from the API
   */
  public async post(
    pathname: string,
    data: any = {},
    authToken?: string
  ): Promise<any> {
    try {
      console.log(
        `Making POST request to ${pathname} - ${JSON.stringify(data)}`
      );

      // Ensure we have an auth token
      if (!authToken && !this.authToken) {
        console.log("No auth token provided, authenticating first");
      }

      await this.authenticate(this.inboundApiKey);

      const headers = await this.getAuthHeaders(pathname);
      const response = await axios.post(
        `${this.apiBaseUrl}api/${pathname}`,
        data,
        {
          headers,
        }
      );

      console.log(`Response from ${pathname}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error making POST request to ${pathname}:`, error);

      // Throw a more descriptive error
      if (error.response) {
        throw new Error(
          `Error fetching ${pathname}: ${
            error.response.status
          } - ${JSON.stringify(error.response.data)}`
        );
      } else {
        throw new Error(`Error fetching ${pathname}: ${error.message}`);
      }
    }
  }

  public async patch(
    pathname: string,
    data: any = {},
    id: string
  ): Promise<any> {
    try {
      console.log(
        `Making PUT request to ${pathname} - ${JSON.stringify(data)}`
      );

      // Ensure we have an auth token
      if (!this.authToken) {
        console.log("No auth token provided, authenticating first");
      }

      await this.authenticate(this.inboundApiKey);

      const headers = await this.getAuthHeaders(pathname);
      const response = await axios.put(
        `${this.apiBaseUrl}api/${pathname}/${id}`,
        data,
        {
          headers,
        }
      );

      console.log(`Response from ${pathname}:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error making PUT request to ${pathname}:`, error);

      // Throw a more descriptive error
      if (error.response) {
        throw new Error(
          `Error fetching ${pathname}: ${
            error.response.status
          } - ${JSON.stringify(error.response.data)}`
        );
      } else {
        throw new Error(`Error fetching ${pathname}: ${error.message}`);
      }
    }
  }

  /**
   * Publishes a message to the SNS topic
   * @param data Data to publish
   * @returns The SNS publish response
   */
  public async publishMessageToSNS(data: any): Promise<SNS.PublishResponse> {
    const messageAttributes: SNS.MessageAttributeMap = {
      source: {
        DataType: "String",
        StringValue: "UNFC",
      },
    };
    const params: SNS.PublishInput = {
      TopicArn: process.env.UNFC_OUTBOUND_TOPIC_ARN,
      Message: JSON.stringify(data),
      MessageGroupId: data?.content?.id,
      MessageAttributes: messageAttributes,
    };

    try {
      console.log("Publishing message to SNS:", params);
      const res = await this.sns.publish(params).promise();
      return res;
    } catch (err) {
      console.error("Error publishing message to SNS:", err);
      throw err;
    }
  }

  public async consumeData(lastProcessedID?: number): Promise<any> {
    const accessToken = await this.authenticate(this.outboundApiKey);
    try {
      let endpoint = "consume";
      if (lastProcessedID) {
        endpoint = `consume?lastProcessedID=${lastProcessedID}`;
      }
      const consumeData = await axios.get(`${this.apiBaseUrl}${endpoint}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      });
      return consumeData;
    } catch (error) {
      console.error("Error consuming data:", error);
      throw error;
    }
  }

  public async checkDuplicatePerson(data: any): Promise<any> {
    const accessToken = await this.authenticate(this.inboundApiKey);
    try {
      let endpoint = "qapi/persons";
      const existingPerson = await axios.post(
        `${this.apiBaseUrl}${endpoint}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/vnd.hedtech.integration.v12+json",
            Accept: "application/vnd.hedtech.integration.v12+json",
          },
        }
      );
      return existingPerson.data;
    } catch (error) {
      if (error.response) {
        throw new Error(
          `Error fetching ${error.response.status} - ${JSON.stringify(
            error.response.data
          )}`
        );
      } else {
        throw new Error(`Error fetching ${error.message}`);
      }
    }
  }

  public async createFileInSFTP(pathname: string, data: any) {
    try {
      const accessToken = await this.authenticate(this.outboundApiKey);
      const createFile = await axios.post(
        `${process.env.GUS_MIDDLEWARE_API}/${pathname}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );
      return createFile.data;
    } catch (error) {
      console.error("Error creating file in SFTP:", error);
      throw error;
    }
  }
}

// Create singleton instance for export
const unfcEllucianConnector = new UNFCEllucianConnector();

// Export the connector instance for direct use
export default unfcEllucianConnector;
