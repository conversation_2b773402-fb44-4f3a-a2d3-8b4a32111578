import axios from "axios";
import fetch from "node-fetch";

interface ResponseData {
  body: any;
  httpHeaders: any;
  httpStatusCode: any;
  referenceId: string;
}

export const postData = async (
  endpoint,
  requestData,
  correlationId?,
  apiKey?,
  method?
) => {
  try {
    const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
    console.log("Path ->", path);
    const headers = {
      "Content-Type": "application/json",
      "x-api-key": apiKey ? apiKey : process.env.GUS_MIDDLEWARE_API_KEY,
    };
    console.log("headers ->", headers);
    console.log("requestData -->", JSON.stringify(requestData));
    if (correlationId) {
      headers["Correlation-Id"] = correlationId;
    }

    const response = await fetch(path, {
      method: method ? method : "POST",
      headers: headers,
      body: JSON.stringify(requestData),
    });

    console.log("Response -->", response);
    if (!response.ok) {
      const errorResponse = await response.text();
      console.log("Error in response -->", errorResponse);
      throw errorResponse;
    }
    let responseData;
    const contentLength = response.headers.get("content-length");

    if (contentLength && parseInt(contentLength) > 0) {
      responseData = await response.json();
    } else {
      responseData = null; // No content to parse
    }
    console.log("responseData", responseData);
    if (Array.isArray(responseData)) {
      console.log("response is an array");
      return responseData as ResponseData[];
    } else {
      console.log("response is not a array");
      return responseData;
    }
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

export const getData = async (endpoint, correlationId?: string, apiKey?) => {
  try {
    const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
    const headers = {
      "x-api-key": apiKey ? apiKey : process.env.GUS_MIDDLEWARE_API_KEY,
    };
    if (correlationId) {
      headers["Correlation-Id"] = correlationId;
    }
    const response = await axios.get(path, {
      headers: headers,
    });

    return await response.data;
  } catch (error) {
    console.log("Err", error);
    throw error?.response?.data;
  }
};

export const post = async (endpoint, requestData, correlationId?, apiKey?) => {
  try {
    const path = `${process.env.GUS_MIDDLEWARE_API}/${endpoint}`;
    const headers = {
      "Content-Type": "application/json",
      "x-api-key": apiKey ? apiKey : process.env.GUS_MIDDLEWARE_API_KEY,
    };
    if (correlationId) {
      headers["Correlation-Id"] = correlationId;
    }
    const response = await axios.post(path, requestData, {
      headers: headers,
    });
    return await response.data;
  } catch (error) {
    console.log("Err", error);
    throw error?.response?.data;
  }
};
