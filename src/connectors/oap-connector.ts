import axios from "axios"

export const getOapData = async (endpoint) => {
    try {
        const path = `${process.env.OAP_API}/oap/${endpoint}`;
        console.log("PATH -->", path)
        const response = await axios.get(path);
        return await response.data;
    } catch (error) {
        console.log('Error while get data --->', error)
        throw error?.response?.data;
    }
};

export const postOapData = async (endpoint, data?) => {
    try {
        const path = `${process.env.OAP_API}/oap/${endpoint}`;
        console.log("OAP path", path)
        const response = await axios.post(path, data);

        return response.data;
    } catch (error) {
        console.log('Err', error)
        throw error?.response?.data;
    }
};
