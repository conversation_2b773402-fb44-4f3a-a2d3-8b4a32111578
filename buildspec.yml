version: 0.2
run-as: root

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - node --version
      - npm install -g yarn
      - yarn install
      - yarn global add serverless@3.38.0
      - yarn global add serverless-esbuild
  pre_build:
    commands:
      - git config --global credential.helper '!aws codecommit credential-helper $@'
      - git config --global credential.UseHttpPath true
      - echo "Fetching changes..."
      - git fetch --depth=2
      - echo "Detecting changes..."
      - |
        PREV_COMMIT=$(git rev-parse HEAD^1 || echo "")
        if [ -n "$PREV_COMMIT" ]; then
          CHANGED_FILES=$(git diff --name-only $PREV_COMMIT HEAD)
        else
          CHANGED_FILES=""
        fi
        echo "Changed files: $CHANGED_FILES"
        echo "$CHANGED_FILES" > changed_files.txt
      - cat changed_files.txt
  build:
    commands:
      - echo "Building and deploying based on changes..."
      - |
        if grep -qE '(src/connectors|src/libs|src/common|serverless.ts|buildspec.yml|package.json|tsconfig.json|tsconfig.path.json)' changed_files.txt; then
          echo "Common files changed, deploying whole stack"
          sls deploy --stage ${stage} --verbose
        else
          if grep -q 'src/functions/gus-sf-hzu-educationcloud-integration' changed_files.txt; then
            echo "Deploying gusSfHzuEducationcloudIntegration functions"
            sls deploy function --function gusSfHzuEducationcloudIntegration --stage ${stage} --verbose
            sls deploy function --function gusSfHzuEducationcloudFailedRecordProcessor --stage ${stage} --verbose
          else
            echo "No changes in gusSfHzuEducationcloudIntegration functionS"
          fi
          if grep -q 'src/functions/hzu-educationcloud-gus-sf-integration' changed_files.txt; then
            echo "Deploying hzuEducationcloudGusSfIntegration function"
            sls deploy function --function hzuEducationcloudGusSfIntegration --stage ${stage} --verbose
            sls deploy function --function hzuEducationcloudgusSfFailedRecordProcessor --stage ${stage} --verbose
          else
            echo "No changes in hzuEducationcloudGusSfIntegration function"
          fi
          if grep -q 'src/functions/myucw-gus-sf-integration' changed_files.txt; then
            echo "Deploying myucwGusSfIntegration function"
            sls deploy function --function myucwGusSfIntegration --stage ${stage} --verbose
            sls deploy function --function myucwGusSfIntegrationFailedRecordProcessor --stage ${stage} --verbose
          else
            echo "No changes in myucwGusSfIntegration function"
          fi
          if grep -q 'src/functions/gus-sf-myucw-integration' changed_files.txt; then
            echo "Deploying gusSfMyUCWIntegration function"
            sls deploy function --function gusSfMyUCWIntegration --stage ${stage} --verbose
            sls deploy function --function gusSfMyUCWFailedRecordProcessor --stage ${stage} --verbose
          else
            echo "No changes in gusSfMyUCWIntegration function"
          fi
          if grep -q 'src/functions/gus-mailer-service' changed_files.txt; then
            echo "Deploying gusMailer function"
            sls deploy function --function gusMailer --stage ${stage} --verbose
          else
            echo "No changes in gusMailer function"
          fi
          if grep -q 'src/functions/gus-sf-ue-cn-integration' changed_files.txt; then
            echo "Deploying gusSfUECampusNetIntegration function"
            sls deploy function --function gusSfUECampusNetIntegration --stage ${stage} --verbose
          else
            echo "No changes in gusSfUECampusNetIntegration function"
          fi
          if grep -q 'src/functions/ue-campusnet-gus-sf-integration' changed_files.txt; then
            echo "Deploying UECampusNetGusSfIntegration function"
            sls deploy function --function UECampusNetGusSfIntegration --stage ${stage} --verbose
          else
            echo "No changes in UECampusNetGusSfIntegration function"
          fi
          if grep -q 'src/functions/unfc-integration-polling-service' changed_files.txt; then
            echo "Deploying unfcIntegrationPollingService function"
            sls deploy function --function unfcIntegrationPollingService --stage ${stage} --verbose
          else
            echo "No changes in unfcIntegrationPollingService function"
          fi
          if grep -q 'src/functions/unfc-gus-sf-integration' changed_files.txt; then
            echo "Deploying unfcGusSfIntegration function"
            sls deploy function --function unfcGusSfIntegration --stage ${stage} --verbose
          else
            echo "No changes in unfcGusSfIntegration function"
          fi
          if grep -q 'src/functions/gus-sf-unfc-integration' changed_files.txt; then
            echo "Deploying gusSfUnfcIntegration function"
            sls deploy function --function gusSfUnfcIntegration --stage ${stage} --verbose
          else
            echo "No changes in gusSfUnfcIntegration function"
          fi
          if grep -q 'src/functions/unfc-sftp-service' changed_files.txt; then
            echo "Deploying unfcSftpService function"
            sls deploy function --function unfcSftpService --stage ${stage} --verbose
          else
            echo "No changes in unfcSftpService function"
          fi
          if grep -q 'src/functions/ulaw-gus-sf-integration' changed_files.txt; then
            echo "Deploying ULAWGusSfIntegration function"
            sls deploy function --function ULAWGusSfIntegration --stage ${stage} --verbose
          else
            echo "No changes in ULAWGusSfIntegration function"
          fi
          if grep -q 'src/functions/outbound-event-integration' changed_files.txt; then
            echo "Deploying outboundEventIntegration function"
            sls deploy function --function outboundEventIntegration --stage ${stage} --verbose
            sls deploy function --function outboundEventIntegrationFailedRecordProcessor --stage ${stage} --verbose
          else
            echo "No changes in outboundEventIntegration function"
          fi
        fi
cache:
  paths:
    - node_modules
