export const permissions = {
    UnfcSftpHandleInvokePermission: {
      Type: "AWS::Lambda::Permission",
      Properties: {
        FunctionName: {
          "Fn::Sub":
            "arn:aws:lambda:${self:custom.variables.region.${opt:stage}}:${self:custom.variables.AccountId.${opt:stage}}:function:unfc-sftp-service-${opt:stage}",
        },
        Action: "lambda:InvokeFunction",
        Principal: "apigateway.amazonaws.com",
        SourceArn: {
          "Fn::Sub":
            "arn:aws:execute-api:${self:custom.variables.region.${opt:stage}}:${self:custom.variables.AccountId.${opt:stage}}:${self:custom.variables.EipApiId.${opt:stage}}/*/*/eip/unfc/sftp",
        },
      },
    },
  };